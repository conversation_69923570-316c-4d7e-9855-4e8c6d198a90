<template>
	<view class="page">
		<!-- 文章数据 -->
		<mescroll-body ref="mescrollRef" @init="mescrollInit" @down="downCallback" @up="upCallback" :down="downOption"
			:up="upOption" :top="0">
			<view class="banner">
				<swiper class="screen-swiper square-dot" indicator-dots="true" circular="true" autoplay="true"
					interval="5000" duration="500">
					<swiper-item v-for="(item,index) in swiperList" :key="index"
						@click="$fun.jump(`/pages/home/<USER>">
						<image :src="$fun.imgUrl(item.image)" mode="aspectFill"></image>
					</swiper-item>
				</swiper>
			</view>
			<view class="menu-nav" v-if="categoryList.length>0">
				<scroll-view scroll-x @scroll="ScrollMenu" class="nav-list" style="width: calc(100vw - 48rpx);">
					<view :style="{display: 'flex',width:`calc(100vw*${ListLength})`}">
						<view class="nav" ref="nav" :style="'flex-direction:column'"
							v-for="(item,index) in categoryList" :key="index">
							<view class="list" v-for="(item2,index2) in item" @click="navListJump(item2)"
								:key="index2">
								<image :src="$fun.imgUrl(item2.image)" mode=""></image>
								<text>{{item2.name}}</text>
							</view>
						</view>
					</view>
				</scroll-view>
				<view class="indicator" v-if="categoryList.length>1">
					<view class="plan">
						<view class="bar" :style="'left:'+slideNum+'%'"></view>
					</view>
				</view>
			</view>
			<view class="goods-list">
				<view :class="'list-li'" v-for="(item,index) in goodsList"
					@click="item.types==1?$fun.jump(`/pages/discover/discoverDetails?id=${item.id}&lat=${lat}&lng=${lng}`):$fun.jump(`/pages/classify/classify_store?id=${item.id}&name=${item.buname}`)"
					:key="index">
					<view class="thumb" style="padding-left: 15rpx;">
						<image :src="$fun.imgUrl(item.logo)"></image>
					</view>
					<view class="item">
						<view class="title">
							<text class="two-omit">{{item.buname}}</text>
							<text class="info">{{item.content}}</text>
						</view>
						<view class="price" style="color: gray;font-size: 26rpx;">
							<view class="vip-price">
								<text class="min">{{item.types==0?'线上店铺':'实体门店'}}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</mescroll-body>
		<!-- tabbar -->
		<TabBar :tabBarShow="2"></TabBar>
	</view>
</template>

<script>
	// #ifdef H5
	var wx = require('@/util/jweixin.js')
	// #endif
	import TabBar from '@/components/TabBar/TabBar.vue';
	// 引入mescroll-mixins.js
	import MescrollMixin from "@/components/mescroll-uni/mescroll-mixins.js";
	export default {
		mixins: [MescrollMixin], // 使用mixin
		components: {
			TabBar,
		},
		data() {
			return {
				mescroll: null, // mescroll实例对象 (此行可删,mixins已默认)
				// 下拉刷新的配置(可选, 绝大部分情况无需配置)
				downOption: {},
				// 上拉加载的配置(可选, 绝大部分情况无需配置)
				upOption: {},
				swiperList: [],
				categoryList: [],
				goodsList: [],
				slideNum: 0,
				ListLength: 0,
				lat: '',
				lng: ''
			};
		},
		onReady(){
			// #ifdef H5
			// this.getConfig()
			// #endif
		},
		methods: {
			/**
			 * 初始化
			 */
			async init() {
				await this.getSwiper()
				await this.getCategory()
			},
			getConfig() {
				let _this = this;
				this.$fun.ajax.post('config/wxconfig', {
					url: location.href.split('#')[0]
				}).then(res => {
					if (res.status == 1) {
						let prams = res.data
						wx.config({
							debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
							appId: prams.appId, // 必填，公众号的唯一标识
							timestamp: prams.timestamp, // 必填，生成签名的时间戳
							nonceStr: prams.nonceStr, // 必填，生成签名的随机串
							signature: prams.signature, // 必填，签名
							jsApiList: ["getLocation", "openLocation"] //根据需要看需要哪些SDK的功能
						})
						wx.ready(function() {
							wx.getLocation({
								success: function(res) {
									_this.lat = res.latitude;
									_this.lng = res.longitude;
									this.downCallback();
								}
							})
							
						});
						wx.error(function(err) {
							console.error('微信JS-SDK初始化失败', err);
						});
					}
				})
			},
			/**
			 * 获取轮播图
			 */
			getSwiper() {
				this.$fun.ajax.post('news/lists', {
					type: 'business'
				}).then(res => {
					console.log(res)
					if (res.status == 1) {
						this.swiperList = res.data
					}
				})
			},
			/**
			 * 获取分类
			 */
			getCategory() {
				this.$fun.ajax.post('category/list', {
					type: 'business'
				}).then(res => {
					console.log(res)
					if (res.status == 1) {
						if (res.status == 1) {
							this.categoryList = []
							let data = res.data
							let l = res.data.length / 10 < 1 ? 1 : Math.ceil(res.data.length / 10)
							this.ListLength = l
							let index = 0
							for (var i = 0; i < l; i++) {
								let o = res.data.slice(i * 10, i * 10 + 10)
								console.log(o)
								this.categoryList.push(o)
							}
						}
						// this.categoryList = res.data
					}
				})
			},
			/*下拉刷新的回调, 有三种处理方式:*/
			downCallback() {
				this.mescroll.endSuccess();
			},
			/*上拉加载的回调*/
			async upCallback(e) {
				this.mescroll.removeEmpty();
				const data = {
					page: e.num,
					pagenum: e.size,
				};
				await this.getSwiper()
				await this.getCategory()
				this.$fun.ajax.post('business/index', data).then(res => {
					if (res.status == 1) {
						const curList = res.data.data;
						if (e.num === 1) {
							this.goodsList = [];
							//第一页清空数据重载
							if (curList.length > 0) {
								uni.pageScrollTo({
									scrollTop: 0,
									duration: 200
								});
							}
						}
						this.goodsList = this.goodsList.concat(curList); //追加新数据
						this.mescroll.endSuccess(curList.length); //结束加载状态
					}
				})
			},
			/**
			 * 菜单导航滚动
			 */
			ScrollMenu(e) {
				let scrollLeft = e.target.scrollLeft;
				const query = uni.createSelectorQuery().in(this);
				query.select('.nav').boundingClientRect(data => {
					let wid = e.target.scrollWidth - data.width - (data.left * 2 + 5);
					this.slideNum = (scrollLeft / wid * 300) / 2;
				}).exec();
			},
			/**
			 * 分类跳转
			 */
			navListJump(item) {
				console.log(item);
				let keywords = `/pages/discover/discoverList?bid=${item.id}`
				if (keywords.indexOf('name') == -1) {
				if(keywords.indexOf('?')==-1){
					keywords = keywords + '?name=' + item.name	
				}else{
					keywords = keywords + '&name=' + item.name
					}
				}
				this.$fun.jump(keywords, 0, 0)
			},
		}
	}
</script>

<style scoped lang="scss">
	@import 'discover.scss';
</style>