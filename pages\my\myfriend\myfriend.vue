/**
* @Description:  太和永康 



* 
*/
<template>
	<view class="index_class">
<!-- 		<view class="bus-top" style="padding: 20rpx;">
			<text v-for="(item,index) in mList" :key='index' style="margin-right: 10rpx;">{{item.name}}:{{item.value}}</text>
		</view> -->

		<view class="all-order">
			<!--  app 端状态栏站位 -->
			<mescroll-uni top="0" style="padding-top:0!important" :up="upOption" @up="upCallback" @down="downCallback"
				@init="mescrollInit" @emptyclick="emptyClick">
				<view style="background-color:#f4f4f4">
					<view class="team-box u-m-b-10">
							<view class="u-flex u-p-20"
								@click=""
								v-for="(item,index) in list" :key="index">
								<u-avatar class="u-m-r-20" :src="$fun.imgUrl(item.avatar)" size="100" mode="circle"></u-avatar>
								<view style="margin-left: 50rpx;">
									<view class="introduce">用户名：{{item.nickname}}</view>
									<view class="u-m-t-10"> 手机号：{{item.mobile}}</view>
								</view>
							</view>
					</view>
				</view>
			</mescroll-uni>
		</view>
	</view>
</template>

<script>
	let zf_number = []; // 支付订单号
	let iconarr = []; // 合并支付订单号
	let orderNumber = "";
	let backindex = "";
	let payOrderNumer;
	import MescrollUni from "@/components/mescroll-uni/mescroll-uni.vue";

	export default {
		components: {
			MescrollUni
		},
		data() {
			return {
				showingIndex: 1,
				list: [],
				valShow: false,
				numberpay: false, // 合并付款
				valueStatus: 0, // 更新页面状态 防止页面卡死
				mescroll: null, //mescroll实例对象
				upOption: {
					page: {
						num: 0, // 当前页码,默认0,回调之前会加1,即callback(page)会从1开始
						size: 10 // 每页数据的数量
					},
					noMoreSize: 4, //如果列表已无数据,可设置列表的总数量要大于半页才显示无更多数据;避免列表数据过少(比如只有一条数据),显示无更多数据会不好看; 默认5
					empty: {
						tip: "~暂无数据~", // 提示
						icon: 'https://woshop-1258844920.cos.ap-nanjing.myqcloud.com/static/images/nullStatus/noList.png'
					},
					wechatMiniProgram: 0,
				},
				totalPrice: 0,
				mList:[]
			};
		},
		onLoad(option) {
			if (option) {
				this.showingIndex = option.index ? option.index : 1;
				backindex = option.backindex;
			}
			this.list = [];
			this.mescroll && this.mescroll.resetUpScroll();
			//#ifdef MP-WEIXIN
			this.wechatMiniProgram = 1;
			//#endif

		},
		onShow() {

		},

		watch: {
			valShow() {
				this.statusMath(this);
			}

		},
		onReachBottom: function() {
			this.mescroll && this.mescroll.onReachBottom();
		},
		//注册列表滚动事件,用于下拉刷新
		onPageScroll(e) {
			this.mescroll && this.mescroll.onPageScroll(e);
		},
		onBackPress() {
			if (backindex == 2) {
				uni.switchTab({
					url: "/pages/home"
				});
				return true;
			}
		},
		methods: {
			toPt(order_num) {
				this.getnav(`/pagesC/goods/assemble?order_num=${order_num}`);
			},

			mescrollInit(mescroll) {
				this.mescroll = mescroll;
			},
			// 下拉刷新的回调
			downCallback(mescroll) {
				mescroll.resetUpScroll(); // 重置列表为第一页 (自动执行 mescroll.num=1, 再触发upCallback方法 )
				iconarr = [] //下拉刷新重置  iconarr
				this.numberpay = false; // 重置合并付款显示状态
			},
			/*上拉加载的回调: mescroll携带page的参数, 其中num:当前页 从1开始, size:每页数据条数,默认10 */
			upCallback(mescroll) {
				var pageNum = mescroll.num; // 页码, 默认从1开始
				var pageSize = mescroll.size; // 页长, 默认每页10条
				this.$fun.ajax.post('user/getUsers',{
						page: pageNum
					}).then(res => {
						if (res.status == 1) {
							this.mescroll.endByPage(
								res.data.length
							);
							if (pageNum == 1) this.list = []; //如果是第一页需手动制空列表
							this.list = this.list.concat(res.data); //追加新数据
							// this.mList = res.data.list
						}
					})
					.catch(err => {
						// 失败隐藏下拉加载状态
						this.mescroll.endErr();
					});
			},
			// 点击空布局按钮的回调
			emptyClick() {
				uni.switchTab({
					url: "/pages/home"
				});
			},
			getbacktel() {
				if (backindex == 2) {
					uni.switchTab({
						url: "/pages/cart"
					});
				} else {
					this.getback();
				}
			},
			showOptions: function(i) {
				if (this.showingIndex == i) return false;
				this.showingIndex = i;
				this.list = []; // 在这里手动置空列表,可显示加载中的请求进度
				this.mescroll.resetUpScroll(); // 刷新列表数据
				this.numberpay = false;
			},
			hideAl() {
				this.valShow = false;
			},
		}
	};
</script>

<style lang="scss" scoped>
	@import "uview-ui/index.scss";
	page,
	.index_class {
		height: 100%;
	}
	.team-box{
		background-color: #fff;
	}
	.active {
	    border-bottom: 2upx solid #310FFF;
	    color: #310FFF;
	}
	.shou_sure {
		span {
			display: block;
			width: 100upx;
			height: 40upx;
			line-height: 40upx;
			border-radius: 20upx;
			border: 1upx solid #999;
			color: #999;
			text-align: center;
			font-size: 24upx;
		}
	}

	.padding-btn {
		padding-bottom: 80upx;
	}

	.mescroll-uni.mescroll-uni-fixed {
		top: 96upx !important;
	}

	.to_comment {
		width: 100upx !important;
		margin-left: 0 !important;
	}

	.paybtn {
		width: 100%;
		position: fixed;
		bottom: 0;
		left: 0;
		background-color: #fff;
		padding: 20upx;
	}

	.all-order {
		padding-top: 30upx;

		.order_list {
			margin-bottom: 20upx;
			background-color: #fff;

			.order_top {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 20upx 30upx;
				border-bottom: 1upx solid #f4f4f4;
				color: #333;
			}
		}

		.order-all-text {
			text-align: right;
			font-size: 24upx;
			color: #333;
			margin-top: 15upx;
		}
	}
</style>
