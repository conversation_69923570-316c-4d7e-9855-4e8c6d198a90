<template>
	<view class="">
		<video style="width: 100%;" v-if="vInfo.videofile" :src="$fun.imgUrl(vInfo.videofile)"></video>
		<view style="padding: 30rpx;" v-html="vHtml">

		</view>
	</view>

</template>

<script>
	export default {
		data() {
			return {
				id: '',
				vHtml: '',
				vInfo: ''
			}
		},
		onLoad(option) {
			this.id = option.id
			uni.setNavigationBarTitle({
				title: option.name
			})
			this.init()
		},
		methods: {
			init() {
				if (this.id == 'GZH') {
					this.$fun.ajax.post('news/content', {
						id: this.id
					}).then(res => {
						this.vHtml = res.data.content
						this.vInfo = res.data
					})
					return
				}
				this.$fun.ajax.post('news/content', {
					id: this.id
				}).then(res => {
					this.vHtml = res.data.content
					this.vInfo = res.data
				})
			}
		}
	}
</script>

<style lang="scss">
	page {
		background-color: #FFFFFF;
	}
</style>