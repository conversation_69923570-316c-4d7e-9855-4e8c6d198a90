<!-- 我的钱包 -->
<template>
	<view class="wallet-wrap">
		<!-- 钱包卡片 -->
		<view class="head_box u-flex u-row-center u-col-center">
			<view class="card-box">
				<view class="l">
					<view class="card-head u-flex u-col-center">
						<view class="card-title u-m-r-10">{{balance.name}}</view>
						<u-icon @click="showMoney==1?showMoney=0:showMoney=1"
							:name="showMoney==1 ? 'eye-fill' : 'eye-off'" size="46" color="#fff"></u-icon>
					</view>
					<view class="money-num u-p-t-20">{{showMoney==1?balance.money||'0.00':'***' }}</view>
				</view>
				<view style="display: flex;display: flex;justify-content: space-between;width: 50%;flex-wrap: wrap;">
					<button class="u-reset-button withdraw-btn "
						@tap="$navigateTo(`./recharge?id=${id}&name=${balance.name}`)"
						v-if="balance.recharge==1">充值</button>
					<button class="u-reset-button withdraw-btn"
						@tap="$navigateTo(`./withdraw?id=${id}&name=${balance.name}`)"
						v-if="balance.withdrawal==1">提现</button>
					<button class="u-reset-button withdraw-btn"
						@tap="$navigateTo(`./transfer?id=${id}&name=${balance.name}`)"
						v-if="balance.transfer==1">转账</button>
					<button class="u-reset-button withdraw-btn"
						@tap="$navigateTo(`./inverted?id=${id}&name=${balance.name}`)"
						v-if="balance.exchange==1">划转</button>
				</view>
			</view>
		</view>
		<!-- 筛选 -->
		<u-sticky offset-top="0" :enable="true">
			<view class="filter-box u-flex u-row-between">
				<button class="u-reset-button date-btn" style="heught:44rpx;padding:0 20rpx" @click="showCalendar=true">
					{{ selDateText }}
					<u-icon class="u-m-l-20" name="arrow-down-fill" size="28" color="#e5e5e5"></u-icon>
				</button>
				<!-- <view class="total-box">收入 {{ incomeMoney || '0.00' }} 支出 {{ expendMoney || '0.00' }}</view> -->
			</view>
			<!-- 状态分类 -->
			<view class="u-flex nav-box">
				<view class="state-item u-flex-1 " v-for="(state, index) in statusList" :key="state.value"
					@tap="onTab(state.value)">
					<text class="state-title"
						:class="{ 'title-active': stateCurrent === state.value }">{{ state.name }}</text>
					<text class="underline" :class="{ 'underline-active': stateCurrent === state.value }"></text>
				</view>
			</view>
		</u-sticky>

		<!-- 钱包记录 -->
		<view class="wallet-list u-flex" v-for="item in walletLog" :key="item.id">
			<!-- <image class="head-img u-m-r-20" :src="$IMG_URL+item.avatar" mode=""></image> -->
			<view class="list-content">
				<view class="title-box u-flex u-row-between">
					<text class="title u-ellipsis-1">{{ item.memo }}{{ item.title ? '-' + item.title : '' }}</text>
					<view class="money">
						<text v-if="item.wallet >= 0" class="add font-OPPOSANS"
							style="color: #310FFF;">+{{ item.money }}</text>
						<text v-else class="minus font-OPPOSANS" style="color: #310FFF;">{{ item.money }}</text>
					</view>
				</view>
				<view style="display: flex;justify-content: space-between;margin-top: 20rpx;">
					<text class="time" style="font-size: 25rpx;">剩余金额:{{item.oldmoney}}</text>
					<text class="time"></text>
				</view>
				<view style="display: flex;justify-content: space-between;margin-top: 10rpx;">
					<text class="time">{{ $u.timeFormat(item.createtime, 'yyyy-mm-dd hh:MM') }}</text>
					<text v-if="item.type_name" class="time"><u-tag bg-color="#310FFF" :text="item.type_name" mode="dark" /></text>
				</view>
			</view>
		</view>
		<!-- 空置页 -->
		<view style="height: 20rpx;">

		</view>
		<shopro-empty v-show="isEmpty" marginTop="200rpx" :image="'/static/images/no_data.png'" tipText="暂无数据~">
		</shopro-empty>
		<!-- 更多 -->
		<!-- <u-loadmore v-if="!isEmpty" height="80rpx" :status="loadStatus" icon-type="flower" color="#ccc" /> -->
		<!-- 日期选择 -->
		<u-calendar v-model="showCalendar" ref="uCalendar" safeAreaInsetBottom mode="range" start-text="开始"
			end-text="结束" active-bg-color="#310FFF" active-color="#fff" range-bg-color="#ffb68d" range-color="#310FFF"
			:btn-type="'error'"
			:customStyle="{ background: 'linear-gradient(90deg, #310FFF, #310FFF)', boxShadow: '0 7rpx 11rpx 2rpx rgba(255, 92, 0, 0.34)' }"
			@change="selDate"></u-calendar>
	</view>
</template>

<script>
	export default {
		components: {},
		data() {
			return {
				isEmpty: false,
				stateCurrent: 'all', //默认
				statusList: [{
						name: '全部',
						value: 'all'
					},
					{
						name: '收入',
						value: '1'
					},
					{
						name: '支出',
						value: '-1'
					}
				],
				showMoney: 1,
				//日期选择
				showCalendar: false,
				selDateText: '',
				walletLog: [], //钱包记录
				propsDate: '', //日期参数
				incomeMoney: '', //收入
				expendMoney: '', //支出
				loadStatus: 'loadmore', //loadmore-加载前的状态，loading-加载中的状态，nomore-没有更多的状态
				currentPage: 1,
				lastPage: 1,
				wallet_type: '',
				titleNmae: "",
				balance: {},
				id: null
			};
		},
		onLoad(options) {
			uni.setNavigationBarTitle({
				title: options.name
			})
			this.id = options.id
			this.titleNmae = options.name
			this.wallet_type = options.wallet_type
			this.showMoney = options.showMoney
			this.$forceUpdate()
		},
		onShow() {
			this.isLogin && this.getUserInfo();
			this.getToday();
			this.currentPage = 1;
			this.lastPage = 1;
			this.walletLog = [];
			this.getWallet();
			this.getWalletLog();
		},
		// 触底加载更多
		onReachBottom() {
			if (this.currentPage != 1) {
				this.getWalletLog();
			}
		},
		methods: {
			$navigateTo(url) {
				console.log(url)
				uni.navigateTo({
					url
				})
			},
			//  今日
			getToday() {
				let now = new Date();
				let dateText = `${now.getFullYear()}/${now.getMonth() + 1}/${now.getDate()}`;
				this.selDateText = `${now.getFullYear()}-${now.getMonth() + 1}-${now.getDate()}`;
				this.propsDate = `${dateText}-${dateText}`;
			},
			// tab切换
			onTab(state) {
				if (this.stateCurrent !== state) {
					this.stateCurrent = state;
					this.currentPage = 1;
					this.lastPage = 1;
					this.walletLog = [];
					this.getWalletLog();
				}
			},
			// 日期选择
			selDate(e) {
				this.walletLog = [];
				this.currentPage = 1;
				this.lastPage = 1;
				this.selDateText = `${e.startYear}-${e.startMonth}-${e.startDay},${e.endYear}-${e.endMonth}-${e.endDay}`;
				let dateText = `${e.startYear}/${e.startMonth}/${e.startDay}-${e.endYear}/${e.endMonth}/${e.endDay}`;
				this.propsDate = dateText;
				this.getWalletLog();
				this.$refs.uCalendar.init();
			},
			// 钱包
			getWallet(id = this.id) {
				let that = this;
				this.$fun.ajax.post('wallet/balance', {
					id
				}).then(res => {
					if (res.status == 1) {
						this.balance = res.data[0]
					}
				})
			},
			// 钱包明细
			getWalletLog(id = this.id) {
				let that = this;
				that.loadStatus = 'loading';
				this.$fun.ajax.post('wallet/flow', {
					id,
					type: this.stateCurrent,
					time: this.selDateText,
					page: this.currentPage
				}).then(res => {
					if (res.status == 1) {
						if (res.data.data.length > 0) {
							this.walletLog = [...that.walletLog, ...res.data.data]
							this.incomeMoney = res.data.income
							this.expendMoney = res.data.expend
							this.currentPage += 1;
							if (res.data.data.length < 10) {
								that.loadStatus = "nomore"
							}
						} else {
							that.loadStatus = "nomore"
						}
					}
				})
			}
		}
	};
</script>

<style lang="scss">
	.u-flex {
		display: flex;
	}

	.nav-box {
		justify-content: space-around;
		// background: #ffffff
	}

	// 钱包记录
	.wallet-list {
		width: 750rpx;
		padding: 20rpx 30rpx;
		padding-bottom: 0;
		// background-color: #ffff;
		border-bottom: 1rpx solid #eeeeee;
		box-sizing: border-box;
		overflow: hidden;

		.head-img {
			width: 70rpx;
			height: 70rpx;
			border-radius: 50%;
			background: #ccc;
		}

		.list-content {
			justify-content: space-between;
			align-items: flex-start;
			background: #FFFFFF;
			padding: 32rpx;
			border-radius: 8rpx 8rpx 8rpx 8rpx;
			flex: 1;
			width: 100%;

			.title {
				font-size: 28rpx;
				color: #333;
				width: 500rpx;
			}

			.time {
				color: #c0c0c0;
				font-size: 22rpx;
			}
		}

		.money {
			width: calc(100% - 500rpx);
			font-size: 28rpx;
			font-weight: bold;
			box-sizing: border-box;
			// overflow: hidden;

			.add {
				display: inherit;
				width: 100%;
				color: #008467;
				overflow: hidden;
				text-overflow: ellipsis;
				display: -webkit-box;
				-webkit-line-clamp: 2;

			}

			.minus {
				display: inherit;
				width: 100%;
				color: #008467;
				overflow: hidden;
				text-overflow: ellipsis;
				display: -webkit-box;
				-webkit-line-clamp: 2;

			}
		}
	}

	// 钱包
	.head_box {
		width: 750rpx;
		// background-color: #fff;
		padding: 30rpx 0;

		.card-box {
			width: 690rpx;
			max-height: 291rpx;
			padding: 40rpx;
			// background: url('/static/wallet-bg.jpeg') no-repeat;
			// background-size: 100% 100%;
			background-color: #310FFF;
			box-shadow: 1rpx 5rpx 12rpx 0 #949494;
			border-radius: 30rpx;
			overflow: hidden;
			position: relative;
			display: flex;

			.l {
				width: 50%;

				.card-head {
					color: #fff;
					font-size: 30rpx;
				}

				.money-num {
					font-size: 70rpx;
					line-height: 70rpx;
					font-weight: 500;
					color: #ffffff;
				}

				.reduce-num {
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 500;
					font-size: 48rpx;
					color: #FFFFFF;
				}
			}


			.withdraw-btn {
				width: 120rpx;
				height: 60rpx;
				line-height: 60rpx;
				background: #ffffff;
				border-radius: 30px;
				font-size: 24rpx;
				font-weight: 500;
				color: #310FFF;

				// position: absolute;
				// right: 30rpx;
				// top: 40rpx;
			}
		}
	}

	// 筛选

	.filter-box {
		padding: 30rpx;
		line-height: 54rpx;
		background-color: #f6f6f6;

		.date-btn {
			background-color: #fff;
			line-height: 54rpx;
			border-radius: 27rpx;
			padding: 0 20rpx;
			font-size: 24rpx;
			font-weight: 500;
			color: #666666;
			margin-left: 0
		}

		.total-box {
			font-size: 24rpx;
			font-weight: 500;
			color: #999999;
		}
	}

	// 分类
	.state-item {
		height: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		background-color: #fff;
		border-bottom: 1rpx solid transparent;

		.state-title {
			color: #666;
			font-weight: 500;
			font-size: 28rpx;
			line-height: 90rpx;
		}

		.title-active {
			color: #333;
		}

		.underline {
			display: block;
			width: 68rpx;
			height: 4rpx;
			background: #fff;
			border-radius: 2rpx;
		}

		.underline-active {
			background: #310FFF;
			display: block;
			width: 68rpx;
			height: 4rpx;
			border-radius: 2rpx;
		}
	}
</style>