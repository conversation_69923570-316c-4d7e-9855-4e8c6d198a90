<template>
	<view class="index_class">
		<view style="padding: 30rpx;color:#310FFF;text-align: right;"
			@click="$fun.jump('./withdrawList?type=withdrawal')">
			提现记录
		</view>
		<view class="cash-cont">
			<div class="cash_list">
				<!-- 	<div class="cash_tit" style="flex-direction: column;justify-content: flex-start;">
					<div style="width: 100%;">{{titleName}}</div>
					<div style="width: 100%;font-size: 36rpx;font-weight: bold;color: #000000;margin-top: 20rpx;">{{}}</div>
				</div> -->
				<div class="extract">
					<span>提现金额</span>
					<div class="money">
						<text>¥</text>
						<input type="digit" v-model="withdrawMoney" placeholder="请输入实际到账金额"
							placeholder-style="font-szie:28upx;">
					</div>
				</div>
				<div class="withdrawable">
					可提现金额: <span>¥{{withdrawInfo.money}}</span>
					<picker class="settess" :range="splitArr('bank','bankcode',withdrawList)"
						@change="withdrawListChange">
						<view v-if="withdrawindex==null">请选择提现方式</view>
						<view v-else>{{withdrawList[withdrawindex].bank}}</view>
					</picker>
				</div>
				<!-- <view class="top">
					<view class="left">
						<view class="title">上传发票</view>
					</view>
				</view>
				<view class="top">
					<view class="left">
						<image :src="invoicefile?'/static/ico-103.png':'/static/ico-102.png'"
							@click="uploadImage('invoicefile')" style="width: 150rpx;height:150rpx;" mode=""></image>
					</view>
				</view> -->
			</div>


			<!-- <div class="cash_list cashbank" v-if="withdrawInfo.bank_card">
				<div class="u-flex">
					<image src="../../static/images/img/bank.png" class="cashimg" />
					<view class="cashname">
						<p>账号:{{withdrawInfo.bank_card.card_number}}</p>
						<p class="u-m-t-20">姓名:{{withdrawInfo.bank_card.name}}</p>
					</view>
				</div>
			</div> -->

			<view class="settled">
				<view class="person-button">
					<button type="warn" class="cash_btn" @click="submitWithdraw">提现</button>
				</view>
				<view class="btn-text">{{withdrawInfo.withdrawal_txt}}</view>
			</view>
		</view>
		<u-toast ref="uToast" />
		<u-modal v-model="payTipShow" :show-cancel-button="true" content="请先设置支付密码" confirm-color="#310FFF"
			cancel-text="取消" confirm-text="去设置" :show-title="false" @confirm="$u.route('pagesB/setting/setPayPwd')"
			ref="uModal"></u-modal>
		<!-- 支付密码 -->
		<!-- <u-keyboard default="" ref="uKeyboard" mode="number" :mask="true" :mask-close-able="false" :dot-enabled="false"
			v-model="keyboardShow" :safe-area-inset-bottom="true" :tooltip="false" @change="onChange"
			@backspace="onBackspace">
			<view>
				<view class="u-text-center u-padding-20 money">
					<text>{{withdrawInfo.type_name}}</text>
					<view class="u-padding-10 close" data-flag="false" @click="showKeyboard(false)">
						<u-icon name="close" color="#333333" size="28"></u-icon>
					</view>
				</view>
				<view class="u-flex u-row-center">
					<u-message-input mode="box" :maxlength="6" active-color="#310FFF" :dot-fill="true"
						v-model="password" :disabled-keyboard="true" @finish="finish"></u-message-input>
				</view>
				<view class="u-text-center u-padding-top-10 u-padding-bottom-20 tips">请输入支付密码</view>
			</view>
		</u-keyboard> -->
		<passkeyborad ref="passkeyborad" @configPay="configPay" :show="keyboardShow" @close="onSubmit"
			:payTitle="titleName" :payMoney="withdrawMoney"></passkeyborad>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				withdrawInfo: {},
				withdrawMoney: "", // 提现金额
				withdrawType: 1, // 1-余额提现，2-佣金提现
				keyboardShow: false,
				password: '',
				payTipShow: false,
				titleName: "",
				id: "",
				withdrawList: [],
				withdrawindex: null,
				invoicefile: ''
			}
		},
		onLoad(option) {
			this.withdrawType = option.type
			this.titleName = option.name
			this.id = option.id
			this.getWithdrawInfo(option.id, 'withdrawal')
		},
		onShow() {

			this.$fun.ajax.post('/bank/lists', {}).then(res => {
				if (res.status == 1) {
					this.withdrawList = res.data
				}
			})
		},
		methods: {
			uploadImage(str, str1) {
				// 从相册选择图片
				const _this = this;
				uni.chooseImage({
					count: 1,
					type:'file',
					extension:['pdf'],
					success: function(res) {
						_this.handleUploadFile(res.tempFilePaths, str, str1);
					}
				});
			},
			// 上传头像
			handleUploadFile(data, str, str1) {
				const _this = this;
				const filePath = data.path || data[0];
				this.$fun.uploadPic(
					filePath
				).then(res => {
					this.$fun.msg(res.msg)
					if (res.status == 1) {
						_this[str] = res.data.url;
					}
				})
			},
			splitArr(str, str1, arr) {
				let strArr = []
				for (var i = 0; i < arr.length; i++) {
					strArr.push(`${arr[i][str]}`)
				}
				console.log(strArr)
				return strArr
			},
			withdrawListChange(e) {
				this.withdrawindex = e.detail.value
			},
			// 获取余额提现信息
			getWithdrawInfo(id, type) {
				this.$fun.ajax.post('/wallet/config', {
					id,
					type
				}).then(res => {
					if (res.status == 1) {
						this.withdrawInfo = res.data
					}
				})
			},
			// 关闭支付框
			onSubmit() {
				this.keyboardShow = !this.keyboardShow
				this.$refs.passkeyborad.clear()
			},
			configPay(pay) {
				this.$fun.ajax.post('/wallet/withdrawal', {
					bid: this.withdrawList[this.withdrawindex].id,
					id: this.id,
					money: this.withdrawMoney,
					// invoicefile: this.invoicefile,
					pay
				}).then(res => {
					this.onSubmit()
					if (res.status == 1) {
						this.$fun.msg(res.msg);
						setTimeout(() => {
							uni.navigateBack({

							})
						}, 1200)
					}
				})
			},
			/**
			 * 提现到银行卡
			 */
			submitWithdraw() {
				if (this.withdrawMoney == '' || this.withdrawMoney <= 0) {
					this.$fun.msg('请输入提现金额');
					return
				}
				if (this.withdrawindex == null) {
					this.$fun.msg('请选择提现方式');
					return
				}
				if (this.invoicefile == null) {
					this.$fun.msg('请上传发票');
					return
				}
				this.keyboardShow = true;
			},
			doWithdraw() {
				this.$http.doWithdraw({
					paypwd: this.password,
					type: this.withdrawType,
					price: this.withdrawMoney,
					bankCardId: this.withdrawInfo.bank_card.id,
				}).then(res => {
					if (res.status == 200) {
						this.$fun.msg(res.mess)
						setTimeout(() => {
							this.$u.route('pagesC/wallet/withdrawList')
						}, 1500)
					}
				});
			},
			showKeyboard(flag = true) {
				this.password = '';
				this.keyboardShow = flag;
			},
			onChange(val) {
				if (this.password.length < 6) {
					this.password += val;
				}

				if (this.password.length >= 6) {
					this.doWithdraw();
				}
			},
			onBackspace(e) {
				if (this.password.length > 0) {
					this.password = this.password.substring(0, this.password.length - 1);
				}
			},

			finish(e) {},
		}
	}
</script>

<style lang="scss" scoped>
	@import "uview-ui/index.scss";

	.settled {
		background-color: transparent !important;
	}

	.btn-text {
		text-align: center;
		margin-top: 17upx;
		font-size: 26upx;
		color: #999;
	}

	.withdrawable {
		position: relative;
	}

	.settess {
		position: absolute;
		right: 0;
		top: 50%;
		transform: translateY(-50%);
		font-size: 26upx;
	}

	.u-indent-0 {
		text-indent: 0;
	}

	.cashbank {
		display: flex;
		align-items: center;
		padding-bottom: 20upx !important;
		margin-top: 20upx !important;

		.cashimg {
			width: 90upx;
			height: 73upx;
			margin-right: 20upx;
		}

		.cashname {
			display: flex;
			flex-direction: column;

		}
	}

	.money {
		font-size: 40rpx;
		color: #333;
		position: relative;

		.close {
			position: absolute;
			top: 20rpx;
			right: 20rpx;
			line-height: 28rpx;
			font-size: 28rpx;
		}
	}

	.top {
		display: flex;
		justify-content: space-between;
		margin-left: 20rpx;

		.left {
			margin-top: 40rpx;

			.title {
				font-size: 24rpx;
				color: #666;
			}

			.num {
				font-size: 48rpx;
				color: #000;
				margin-top: 30rpx;
				font-weight: bold;
			}

			.jyjl {
				font-size: 24rpx;
				color: #dd1021;
				margin-top: 50rpx;
			}
		}

		.rightimg {
			width: 233rpx;
			height: 240rpx;
			margin-right: 50rpx;
		}
	}

	// 余额提现页面
	.cash-cont {
		height: 100vh;
		padding-top: 20rpx;

		.cash_list {
			width: 720rpx;
			margin: 0 auto;
			box-shadow: 0rpx 0rpx 20rpx rgba(0, 0, 0, 0.1);
			border-radius: 20rpx;
			padding: 30rpx;
			padding-bottom: 0;
			box-sizing: border-box;
			background-color: #FFFFFF;

			.cash_tit {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding-bottom: 10rpx;
				color: #000000;
				font-size: 28rpx;
			}

			.extract {
				height: 190rpx;
				border-top: 1rpx solid #F4F4F4;
				border-bottom: 1rpx solid #F4F4F4;
				padding-top: 40rpx;
				box-sizing: border-box;

				.money {
					font-size: 70rpx;
					display: flex;
					align-items: center;
					height: 120rpx;

					input {
						display: inline-block;
						font-size: 60rpx;
						height: 120rpx;
						line-height: 120rpx;
						padding-left: 20rpx;

					}
				}
			}

			.withdrawable {
				height: 98rpx;
				line-height: 98rpx;
				color: #999;
				font-size: 28rpx;

				span {
					color: #310FFF;
				}
			}
		}

		.cash_btn {
			width: 584rpx;
			height: 98rpx;
			line-height: 98rpx;
			text-align: center;
			color: #FFFFFF;
			background-color: #310FFF;
			border: none;
			border-radius: 49rpx;
			// box-shadow: 0px 5px 10px @fontColor;
			margin-top: 115rpx;
		}

		.zf_list {
			display: flex;
			justify-content: space-between;
			align-items: center;
			height: 98rpx;
			border-top: 1rpx solid #F4F4F4;

			span {
				width: 25%;
				font-size: 30rpx;
			}

			input {
				width: 75%;
				padding-left: 10rpx;
				font-size: 30rpx;
			}
		}
	}
</style>