/**
* @Description: 兰图兴村
*
*/
<template>
	<view class="page">
		<view class="logo">
			<view class="login_title">
				<view>
					您好，
				</view>
				<view>
					欢迎注册
				</view>
			</view>
			<image :src="$fun.imgUrl('/static/register_top.png')" mode="widthFix"></image>
		</view>
		<view style="height:430rpx ;">

		</view>
		<view class="login_content">
			<view class="input-info">
				<view class="title">
					昵称
				</view>
				<view class="info">
					<input type="text"  v-model="nickname" placeholder="请输入您的昵称">
					<view class="more"></view>
				</view>
				<view class="title">
					手机号
				</view>
				<view class="info">
					<input type="tel" maxlength="11" v-model="phone" placeholder="请输入您的手机号">
					<view class="more"></view>
				</view>
				<view class="title" v-if="userConfig.sms_status == 1">
					验证码
				</view>
				<view class="info" v-if="userConfig.sms_status == 1">
					<input type="number" v-model="code" placeholder="请输入您的验证码" maxlength="6" />
					<view class="more"></view>
					<view class="sendCode btn-color" v-if="showTime">{{ time }}s后重新获取</view>
					<view class="sendCode btn-color" @click="getCode()" hover-class="text-hover" v-else>获取验证码</view>

				</view>
				<view class="title">
					账号
				</view>
				<view class="info">
					<input type="text" v-model="invitation_m"  placeholder="请输入您的账号" />
			<!-- 		<view class="more" @click="rotateIcon">
						<u-icon name="reload" color="#310FFF" size="40" :class="{'rotate-animation': isRotating}"></u-icon>
					</view> -->
				</view>
				<view class="title">
					邀请码
				</view>
				<view class="info">
					<input type="text" v-model="invitation" placeholder="请输入您的邀请码" />
					<view class="more" style="color: #310FFF;">{{ userName }}</view>
				</view>
			<!-- 	<block v-if="userConfig.status">
					<view class="title">
						服务人
					</view>
					<view class="info">
						<input type="text" v-model="invitation_tid" placeholder="请输入您的服务人" />
					</view>
				</block> -->

				<view class=" btn-info" style="margin-top: 20rpx;">
					<view class="btn" @click="formSubmit()">
						<text>注册</text>
					</view>
				</view>
				<view style="margin-top: 20rpx;">
					<view class="wrapper" style="display: flex;justify-content: center;margin-top: 10rpx;">
						<view class="input-content">

							<view style="color: #310FFF;">
								默认密码123456 注册成功后请及时修改
							</view>
							<!-- #ifdef H5 -->
							<view v-if="this.userConfig.appUrl"
								style="font-size: 24rpx;text-align: right;color: #310FFF;">
								<text @click="gotoApp">下载app</text>
							</view>
							<!-- #endif -->
						</view>

					</view>
					<view style="height: 60rpx;">

					</view>
					<view style="display: flex;justify-content: center;margin: auto;">
						<u-checkbox-group>
							<u-checkbox active-color="#310FFF" v-model="checked">

							</u-checkbox>
						</u-checkbox-group>
						<view class="zc">
							<text> 我已同意 </text>
							<view class="t" @click="$fun.jump(`/pages/home/<USER>">
								隐私政策
							</view>

							<text> 和 </text>
							<view class="t" @click="$fun.jump(`/pages/home/<USER>">
								用户协议
							</view>
						</view>
					</view>
				</view>

			</view>

		</view>
		<u-action-sheet @click="xzList" :list="list" v-model="show"></u-action-sheet>
		<lotusAddress v-on:choseVal="choseValue" :lotusAddressData="lotusAddressData"></lotusAddress>
	</view>
</template>

<script>
// import graceChecker from "../../api/graceChecker.js"
// import agreement from '@/pagesB/components/agreement';
// import thirdLogin from '@/pagesB/components/thirdLogin';
import lotusAddress from "@/components/Winglau14-lotusAddress/Winglau14-lotusAddress.vue";
export default {
	components: {
		lotusAddress
	},
	data() {
		return {
			phone: '', //手机号
			code: '', //验证码
			password: '123456', //密码
			username: "",
			nickname: "",
			username1: "",
			usercode: "",
			sex: 1,
			address: '',
			age: '',
			invitation_m: '',
			paypwd: "123456",
			address: '',
			tid: '',
			fw: '',
			invitation: "",
			invitation_tid: "",
			agreement: true, //是否同意协议
			timer: null, //定时器
			isClick: true, //避免用户重复点击
			time: 60, //60s后重置
			showTime: false, //时间与获取验证码切换
			parmas: null, //绑定手机号需要的参数
			providerList: [],
			isWechatShow: false,
			userConfig: {},
			position_num: '',
			position_num_t: '',
			checked: false,
			type: 1,
			list: [],
			lotusAddressData: {
				visible: false,
				provinceName: '',
				cityName: '',
				townName: '',
			},
			show: false,
			userName: '==',
			isRotating: false,
		}
	},
	onLoad(option) {
		if (option.type) {
			this.type = option.type;
		}
		// this.AppName = this.$AppName
		this.invitation = option.scene || option.invitation || ''
		if (option.invitation_tid) {
			this.invitation_tid = option.invitation_tid
		}
		// console.log('0000', option);
		this.getloginConfig()
	},
	watch: {
		invitation(val) {
			if (val) {
				this.getqy(val, 'username')
			}
		},
		invitation_tid(val) {
			if (val) {
				this.getqy(val, 'username1')
			}
		}
	},
	methods: {
		clickOpen() {
			this.lotusAddressData.visible = true
		},
		//回传已选的省市区的值
		choseValue(res) {
			//res数据源包括已选省市区与省市区code
			console.log(res);
			//res.isChose = 1省市区已选 res.isChose = 0;未选
			if (res.isChose) {
				this.lotusAddressData.visible = res.visible; //visible为显示与关闭组件标识true显示false隐藏
				this.lotusAddressData.provinceName = res.province; //省
				this.lotusAddressData.cityName = res.city; //市
				this.lotusAddressData.townName = res.town; //区
				this.address = `${res.province}-${res.city}-${res.town}`; //region为已选的省市区的值
				this.$forceUpdate()
			}
			this.lotusAddressData.visible = res.visible;
		},
		xzList(e) {
			this.position_num = this.list[e].value;
			this.position_num_t = this.list[e].text;
		},
		showNum() {
			this.show = !this.show;
		},
		gotoApp() {
			if (this.userConfig.appUrl) {
				location.href = this.userConfig.appUrl
			} else {
				this.$fun.msg('暂时还没有App，请联系技术');
				return;
			}
		},
		getqy(val, str) {
			this.$fun.ajax.post('wallet/getUser', {
				invitation: val
			}).then(res => {
				if (res.status == 1) {
					this[str] = res.data
				}
			})
		},
		getloginConfig() {
			this.$fun.ajax.post('config/index', {}).then(res => {
				if (res.status == 1) {
					this.userConfig = res.data;
					// this.getInvitation()
				}
			})
		},
		getInvitation() {
			this.$fun.ajax.post('user/getCode', {}).then(res => {
				if (res.status == 1) {
					this.invitation_m = res.data
				}
			})
		},
		changeAgree(e) {
			this.agreement = e;
			console.log(this.agreement)
		},
		formSubmit(e) {
			//进行表单检查
			let prams = {
				mobile: this.phone,
				nickname: this.nickname,
				password: this.password,
				paypwd: this.paypwd,
				invitation: this.invitation,
				invitation_m: this.invitation_m,
				// position_num: this.position_num,
				// sex: this.sex,
				// age: this.age,
				// usercode: this.usercode,
				// address: this.address
				// invitation_tid: this.invitation_tid,
			}
			if (!this.checked) {
				this.$fun.msg('请勾选隐私政策和用户协议')
				return false;
			}
			if (prams.nickname == '') {
				this.$fun.msg('请输入姓名')
				return false;
			}
			// if (prams.username == '') {
			// 	this.$fun.msg('请选择性别')
			// 	return false;
			// }
			// if (prams.age == '') {
			// 	this.$fun.msg('请输入年龄')
			// 	return false;
			// }
			// if (prams.usercode == '') {
			// 	this.$fun.msg('请输入身份证号')
			// 	return false;
			// }
			// if (prams.address == '') {
			// 	this.$fun.msg('请选择所在地')
			// 	return false;
			// }

			if (!/^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/.test(prams.mobile)) {
				this.$fun.msg('请输入正确的手机号')
				return false;
			}
			if (this.userConfig.sms_status == 1) {
				prams.code = this.code
				if (prams.code == '') {
					this.$fun.msg('请输入验证码')
					return false;
				}
			}

			// if (this.userConfig.status) {
			// 	if (prams.position_num == '') {
			// 		this.$fun.msg('请输选择社区')
			// 		return false;
			// 	}
			// }
			// if (prams.invitation == '') {
			// 	this.$fun.msg('请输入推荐人ID')
			// 	return false;
			// }
			this.$fun.ajax.post('user/register', {
				...prams
			}).then(res => {
				if (res.status == 1) {
					this.$fun.msg(res.msg);

					// #ifdef H5
					if (this.userConfig.login.indexOf('wechat') != -1) {
						// #ifdef H5
						this.$fun.jump('/pages/my/webHtml/webHtml?name=wechat_gzh&title=公众号', 0, 1500)
						return
						// #endif
					} else {
						if (!this.userConfig.appUrl) {
							this.$fun.jump('/pages/my/login/login', 3, 1200)
						} else {
							location.href = this.userConfig.appUrl
						}
						return
					}
					// #endif
					// #ifdef APP-PLUS
					if (this.type == 1) {
						this.$fun.jump('/pages/my/login/login', 3, 1200)
					} else {
						setTimeout(() => {
							uni.navigateBack()
						}, 500)
					}
					// #endif
				}
			})
		},
		// 获取验证码
		getCode() {
			if (this.isClick) {
				if (this.phone.length != 11) {
					return this.$fun.msg('手机格式不正确')
				}
				// this.$loading()
				this.$fun.ajax.post('sms/send', {
					mobile: this.phone,
					event: 'register'
				}).then(res => {
					if (res.status == 1) {
						this.showTime = true;
						this.$fun.msg(res.msg)
						this.timer = setInterval(() => {
							this.isClick = false;
							this.time = this.time - 1;
							if (this.time <= 0) {
								this.isClick = true;
								this.time = 60;
								this.showTime = false;
								clearInterval(this.timer);
								this.timer = null;
							}
						}, 1000)
					}

				})
			}
		},
		rotateIcon() {
			this.isRotating = true;
			setTimeout(() => {
				this.getInvitation();
				this.isRotating = false;
			}, 1000);
		},
	},
	// 页面卸载
	onUnload() {
		this.isClick = true;
		this.time = 60;
		// 清空定时器
		clearInterval(this.timer);
		this.timer = null;
	},

}
</script>
<style lang="scss">
@import '../login/login.scss';

.zc {
	display: flex;
	font-size: 28rpx;
	align-items: center;
	color: #000000;


	.t {
		display: inline-block;
		margin: 0 10rpx;
		color: #310FFF;
	}
}

.rotate-animation {
	animation: rotate-720 1s linear;
}

@keyframes rotate-720 {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}
</style>