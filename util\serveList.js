// Date: 2018-12-14
// import ajax from "@/util/req/ajax.js";
import fun from '@/util/fun.js';
let DateObj = [{
		type: 'init',
		fun: bind
	},
	{
		type: 'chat',
		fun: jsonstr
	}
];

function handleDate(jsonstr) {
	let json = JSON.parse(jsonstr);
	if(json.value){
		json.value = JSON.parse(json.value);
	}
	// 循环判断类型
	for (var i = 0; i < DateObj.length; i++) {
		if (DateObj[i].type == json.type) {
			DateObj[i].fun(json,jsonstr)
		}
	}
}
// 转换数据
function bind(jsonstr) {
	let prams = {
		client_id: jsonstr.client_id
	}
	fun.ajax.post(`chat/bind`, prams).then(res => {});
}


function jsonstr(json,jsonstr) {
	console.log(json, '===============接收===onMessage===============');
	uni.$emit(json.type, jsonstr)
}
module.exports = {
	handleDate: handleDate
};