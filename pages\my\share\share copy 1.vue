<template>
	<view class="swiper-3d" :style="{width: '100%',height:canvasHeight+'px'}">
		<swiper :style="{width: '100%',height:canvasHeight+'px'}" :circular="true" previous-margin="50rpx"
			next-margin="40rpx">
			<swiper-item v-for="(item,index) in  imgbox" :key="index">
				<view class="swiper-item" :style="{width: '640rpx',height:canvasHeight+'px'}">
					<canvas :ref="`firstCanvas${index}`" :style="{width: '640rpx',height:canvasHeight+'px'}"
						:canvas-id="`firstCanvas${index}`"
						@longpress="saveImgToLocal(`firstCanvas${index}`,0)"></canvas>
				</view>
			</swiper-item>
		</swiper>
		<uqrcode :id="'batchQRCode'" style="position: absolute;top:-1000px;left: 10000px;" ref="batchQRCode"
			:text="text" :size="256" :margin="10" background-color="#FFFFFF" foreground-color="#000000"></uqrcode>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				imgbox: [],
				imgSrc: '',
				canvasQrPath: "",
				text: '',
				size: 150,
				codeImg: "",
				colorDark: '#000000',
				colorLight: '#ffffff',
				list: [],
				canvasHeight: 0
			};
		},
		onLoad() {
			// this.$api.inviteImg().then(res => {
			// 	let data = []
			// 	res.data.forEach((item) => {
			// 		data.push(this.$fun.imgUrl(item.image))
			// 	})
			// 	this.imgbox = data
			// })
			// config/invitation
			this.$fun.ajax.post('/config/invitation', {}).then(res => {
				if (res.status == 1) {
					let data = []
					res.data.forEach((item) => {
						data.push(this.$fun.imgUrl(item.image))
					})
					this.imgbox = data
					this.list = res.data
				}
			})
			// #ifdef H5
			this.getQrPath()
			// #endif
			// #ifdef MP-WEIXIN
			this.getaccesstoken()
			// #endif
		},
		methods: {
			// 获取token
			getaccesstoken() {
				let that = this
				// this.$api.getaccesstoken().then(res => {
				// 	if (res.code == 1) {
				// 		// this.getWxCode(res.data)
				// 		uni.getImageInfo({
				// 			src: that.$imgUrl(res.data),
				// 			success(res) {
				// 				that.codeImg = res.path
				// 				that.drawImage()
				// 			}
				// 		})
				// 	}
				// })	
				that.$fun.ajax.post('/user/miniImage', {}).then(res => {
					// console.log(res.data)
					console.log('-------')
					console.log(that.$fun.imgUrl(res.data))
					console.log('-------')
					uni.getImageInfo({
						src: that.$fun.imgUrl(res.data),
						success(res) {

							that.codeImg = res.path
							that.drawImage()
						}
					})
				})
			},
			/**
			 * @param {Object} shareToken
			 */
			getWxCode(shareToken) { //获取小程序码
				let that = this
				uni.showLoading({
					title: '加载中',
					mask: true
				})
				uni.request({
					url: `https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=${shareToken}`,
					method: "POST",
					data: {
						width: 300,
						page: 'pages/public/login',
						scene: uni.getStorageSync('userInfo').id,
					},
					responseType: 'arraybuffer',
					success: function(res) {
						console.log(res)
						uni.hideLoading();
						let src = uni.arrayBufferToBase64(res.data);
						that.codeImg = 'data:image/png;base64,' + src;

					}
				})
			},
			// 绘制二维码  H5
			getQrPath() {
				this.$fun.ajax.post('/user/gitH5Qrcode', {}).then(res => {
					if (res.status == 1) {
						this.text = res.data
						var that = this;
						setTimeout(function() {
							that.$refs.batchQRCode.toTempFilePath({
								success: res => {
									that.canvasQrPath = res.tempFilePath
									that.drawImage()
								},
								fail: err => {
									uni.showToast({
										icon: 'none',
										title: JSON.stringify(err)
									})
								}
							})
						}, 800)
					}
				})
			},
			// 保存图片
			saveImgToLocal(index) {
				let that = this
				uni.showModal({
					title: '提示',
					content: '确定保存到相册吗',
					success: (res) => {
						if (res.confirm) {
							uni.canvasToTempFilePath({
								canvasId: `firstCanvas${index}`,
								success: function(res1) {
									uni.saveImageToPhotosAlbum({
										filePath: res1
											.tempFilePath,
										success: function() {
											uni.showToast({
												title: "保存成功",
												icon: "none"
											});
										},
										fail: function() {
											uni.showToast({
												title: "保存失败",
												icon: "none"
											});
										}
									});

								}
							}, this)
						} else if (res.cancel) {

						}
					}
				});
			},
			// 画图
			drawImage() {
				var that = this;
				for (let i = 0; i < this.imgbox.length; i++) {
					that.canvasHeight = that.list[i].bg_height
					uni.getImageInfo({
						src: this.imgbox[i],
						success(res) {
							let ctx = uni.createCanvasContext(`firstCanvas${i}`) // 使用画布创建上下文 图片
							ctx.drawImage(res.path, 0, 0, 320,
								that.list[i].bg_height) // 设置图片坐标及大小，括号里面的分别是（图片路径，x坐标，y坐标，width，height）
							ctx.drawImage(res.path, 0, 0, 320,
								that.list[i].bg_height) // 设置图片坐标及大小，括号里面的分别是（图片路径，x坐标，y坐标，width，height）
							// x y
							let x = 320 * (that.list[i].x_axis_bl);
							let y = that.list[i].bg_height * (that.list[i].y_axis_bl);
							let x1 = x - 5;
							let y1 = that.list[i].bg_height * (that.list[i].y_axis_bl) + that.list[i].qr_height +
							5;
							// 起始点
							ctx.moveTo(x - 5, y - 5)
							// 02 划线  坐标
							ctx.lineTo(x1, y1)
							ctx.lineTo(x + that.list[i].qr_width + 5, y1)
							ctx.lineTo(x + that.list[i].qr_width + 5, y - 5)
							ctx.lineTo(x1, y - 5)
							// 以上两行代码只是一个路径，但还没有绘制
							// 03 绘制
							ctx.fillStyle = "#FFFFFF"
							ctx.fill();

							// #ifdef MP-WEIXIN
							ctx.drawImage(that.codeImg, 320 * (that.list[i].x_axis_bl), that.list[i].bg_height * (
									that.list[i].y_axis_bl), that.list[i].qr_width,
								that.list[i].qr_height) // 设置图片坐标及大小，括号里面的分别是（图片路径，x坐标，y坐标，width，height）
							// #endif
							// #ifdef H5
							ctx.drawImage(that.canvasQrPath, 320 * (that.list[i].x_axis_bl), that.list[i]
								.bg_height * (that.list[i].y_axis_bl), that.list[i].qr_width,
								that.list[i].qr_height) // 设置图片坐标及大小，括号里面的分别是（图片路径，x坐标，y坐标，width，height）
							// #endif

							ctx.save(); //保存
							ctx.draw() //绘制
						}
					})
				}

			},
			saveImgFile(base64) { //base64为base64图片值
				const bitmap = new plus.nativeObj.Bitmap("test");
				bitmap.loadBase64Data(base64, function() {
					const url = "_doc/" + new Date().getTime() + ".png"; // url为时间戳命名方式
					console.log('saveHeadImgFile', url)
					bitmap.save(url, {
						overwrite: true, // 是否覆盖
						// quality: 'quality'  // 图片清晰度
					}, (i) => {
						uni.saveImageToPhotosAlbum({
							filePath: url,
							success: function() {
								uni.showToast({
									title: '图片保存成功',
									icon: 'none'
								})
								bitmap.clear()
							}
						});
					}, (e) => {
						uni.showToast({
							title: '图片保存失败',
							icon: 'none'
						})
						bitmap.clear()
					});
				}, (e) => {
					uni.showToast({
						title: '图片保存失败',
						icon: 'none'
					})
					bitmap.clear()
				});
			},
			// 保存图片
			saveImgToLocal(id, index = 0) {
				let _this = this
				uni.canvasToTempFilePath({
					canvasId: id,
					fileType: 'png',
					quality: 1, //图片质量
					success: function(result) {
						var tempFilePath = result.tempFilePath;
						uni.downloadFile({
							url: tempFilePath,
							success: (res) => {
								uni.previewImage({
								            urls: [res.tempFilePath]//预览图片的地址，必须要数组形式，如果不是数组形式就转换成数组形式就可以
								          })
								console.log(res);
								//创建一个a标签
								// var link = document.createElement('a');
								// //把a标签的href属性赋值到生成好了的url
								// link.href = res.tempFilePath;

								// //通过a标签的download属性修改下载图片的名字
								// link.download = '一起来休息一下.png';
								// //让a标签的click函数，直接下载图片
								// link.click();
								// uni.saveImageToPhotosAlbum({
								//                     filePath: res.tempFilePath,
								//                     success: function() {
								//                         uni.showToast({
								//                             title: '已保存至相册',
								//                             icon: 'none'
								//                         })
								//                     }
								//                 });
							}
						})
					},
				});
			}
		}
	}
</script>

<style lang="scss">
	.img_box {
		position: fixed;
		bottom: 50rpx;
		width: 80%;
		display: flex;
		justify-content: space-around;
		z-index: 100;
		left: 0;
		right: 0;
		margin: auto;

		image {
			width: 100rpx;
			height: 100rpx;
		}
	}

	.swiper-3d {
		padding: 0upx 20upx;
		position: absolute;
		top: 0;
		left: 0;
		bottom: 0;
		right: 0;
		margin: auto;

		.s-container {
			height: 900upx;
			width: 100%;

			.swiper-item {
				// max-width: 630upx;
				height: 90%;
				padding: 0upx 20upx;
				box-sizing: border-box;
				position: relative;

			}

			.item-img {
				// position: absolute;
				margin-top: 30upx;
				width: 100%;
				height: 80%;
				// border-radius: 15upx;
				z-index: 5;
				// opacity: 0.7;
				// top: 7%;
				// transform: translateY(-50%);
				// box-shadow:0px 4upx 15upx 0px rgba(153,153,153,0.24);
			}

			.active {
				opacity: 1;
				z-index: 10;
				height: 90%;
				top: -3%;
				transition: all .1s ease-in 0s;
				transform: translateY(0);
			}
		}

		.swiper-dot {
			display: flex;
			justify-content: center;
			align-items: center;

			// padding-top: 10upx;
			.dot {
				margin: 0 10upx;
				width: 15upx;
				height: 15upx;
				border-radius: 50%;
				background: #bbb;

				&.on {
					background: #F4BD48;
				}
			}
		}
	}
</style>
