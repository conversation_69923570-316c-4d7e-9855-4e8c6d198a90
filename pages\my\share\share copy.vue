<template>
	<view class="swiper-3d" :style="{width: canvasWidth+'px',height:canvasHeight+'px'}">
		<swiper  :style="{width: '100%',height:canvasHeight+'px'}" :circular="true" >
			<swiper-item v-for="(item,index) in  imgbox" :key="index">
				<view class="swiper-item" :style="{width: canvasWidth+'px',height:canvasHeight+'px'}">
					<canvas :ref="`firstCanvas${index}`" :style="{width: canvasWidth+'px',height:canvasHeight+'px'}" 
						:canvas-id="`firstCanvas${index}`" @longpress="saveImgToLocal(`firstCanvas${index}`,0)"></canvas>
				</view>
			</swiper-item>
		</swiper>
	<uqrcode :id="'batchQRCode'" style="position: absolute;top:-1000px;left: 10000px;" ref="batchQRCode" :text="text" :size="256" :margin="10" background-color="#FFFFFF" foreground-color="#000000"></uqrcode>
	</view>
</template>
<script>
	export default {	
		data() {
			return {
				imgbox: [],
				imgSrc: '',
				canvasQrPath: "",
				text: '',
				size: 150,
				codeImg: "",
				colorDark: '#000000',
				colorLight: '#ffffff',
				list:[],
				canvasHeight:0,
				canvasWidth:0,
			};
		},
		onLoad() {
			// this.$api.inviteImg().then(res => {
			// 	let data = []
			// 	res.data.forEach((item) => {
			// 		data.push(this.$fun.imgUrl(item.image))
			// 	})
			// 	this.imgbox = data
			// })
			 const res = uni.getSystemInfoSync();
			    console.log(res);

			// config/invitation
			this.$fun.ajax.post('/config/invitation', {}).then(res => {
				if (res.status == 1) {
					let data = []
						res.data.forEach((item) => {
							data.push(this.$fun.imgUrl(item.image))
						})
						this.imgbox = data
						this.list = res.data
				}
			})
			// #ifdef H5
			this.getQrPath()
			// #endif
			// #ifdef MP-WEIXIN
			this.getaccesstoken()
			// #endif
		},
		methods: {
			// 获取token
			getaccesstoken() {
				let that = this
					that.$fun.ajax.post('/user/miniImage', {}).then(res => {
						uni.getImageInfo({
									src: that.$fun.imgUrl(res.data),
									success(res) {
										
										that.codeImg = res.path
										that.drawImage()
									}
								})
					})
			},
			/**
			 * @param {Object} shareToken
			 */
			getWxCode(shareToken) { //获取小程序码
				let that = this
				uni.showLoading({
					title: '加载中',
					mask: true
				})
				uni.request({
					url: `https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=${shareToken}`,
					method: "POST",
					data: {
						width: 300,
						page: 'pages/public/login',
						scene: uni.getStorageSync('userInfo').id,
					},
					responseType: 'arraybuffer',
					success: function(res) {
						console.log(res)
						uni.hideLoading();
						let src = uni.arrayBufferToBase64(res.data);
						that.codeImg = 'data:image/png;base64,' + src;

					}
				})
			},
			// 绘制二维码  H5
			getQrPath() {
				this.text =
					`${this.$fun.baseUrl()}#/pages/my/register/register?invitation=${uni.getStorageSync('userinfo').invitation}`
				var that = this;
				setTimeout(function() {
					that.$refs.batchQRCode.toTempFilePath({
					  success: res => {
						   that.canvasQrPath = res.tempFilePath
						   that.drawImage()
					  },
					  fail: err => {
					    uni.showToast({
					      icon: 'none',
					      title: JSON.stringify(err)
					    })
					  }
					})
				}, 800)
			},
			// 保存图片
			saveImgToLocal(index) {
				let that = this
				uni.showModal({
					title: '提示',
					content: '确定保存到相册吗',
					success: (res) => {
						if (res.confirm) {
							uni.canvasToTempFilePath({
								canvasId: `firstCanvas${index}`,
								success: function(res1) {
									uni.saveImageToPhotosAlbum({
										filePath: res1
											.tempFilePath,
										success: function() {
											uni.showToast({
												title: "保存成功",
												icon: "none"
											});
										},
										fail: function() {
											uni.showToast({
												title: "保存失败",
												icon: "none"
											});
										}
									});
			
								}
							}, this)
						} else if (res.cancel) {
			
						}
					}
				});
			},
			// 画图
			drawImage() {
				var that = this;
				for (let i = 0; i < this.imgbox.length; i++) {
					that.canvasHeight= uni.getSystemInfoSync().windowHeight
					that.canvasWidth= uni.getSystemInfoSync().windowWidth
					uni.getImageInfo({
						src: this.imgbox[i],
						success(res) {
							let ctx = uni.createCanvasContext(`firstCanvas${i}`) // 使用画布创建上下文 图片
							ctx.drawImage(res.path, 0, 0, that.canvasWidth,
								that.canvasHeight) // 设置图片坐标及大小，括号里面的分别是（图片路径，x坐标，y坐标，width，height）
							ctx.drawImage(res.path, 0, 0, that.canvasWidth,
								that.canvasHeight) // 设置图片坐标及大小，括号里面的分别是（图片路径，x坐标，y坐标，width，height）
								// x y
								let x = that.canvasWidth*(that.list[i].x_axis_bl);
								let y = that.canvasHeight*(that.list[i].y_axis_bl);
								let x1 = x-5;
								let y1 = that.canvasHeight*(that.list[i].y_axis_bl)+that.list[i].qr_height+5;
								// 起始点
								ctx.moveTo(x-5,y-5)
								            // 02 划线  坐标
								            ctx.lineTo(x1,y1)
								            ctx.lineTo(x+that.list[i].qr_width+5,y1)
								            ctx.lineTo(x+that.list[i].qr_width+5,y-5)
								            ctx.lineTo(x1,y-5)
								            // 以上两行代码只是一个路径，但还没有绘制
								            // 03 绘制
											ctx.fillStyle="#FFFFFF"
											ctx.fill();
								
							// #ifdef MP-WEIXIN
							ctx.drawImage(that.codeImg, that.canvasWidth*(that.list[i].x_axis_bl), that.list[i].bg_height*(that.list[i].y_axis_bl), that.list[i].qr_width,
								that.list[i].qr_height) // 设置图片坐标及大小，括号里面的分别是（图片路径，x坐标，y坐标，width，height）
							// #endif
							// #ifdef H5
							ctx.drawImage(that.canvasQrPath, that.canvasWidth*(that.list[i].x_axis_bl), that.canvasHeight*(that.list[i].y_axis_bl), that.list[i].qr_width,
								that.list[i].qr_height) // 设置图片坐标及大小，括号里面的分别是（图片路径，x坐标，y坐标，width，height）
							// #endif
													                
							ctx.save(); //保存
							ctx.draw() //绘制
						}
					})
				}

			},
			// 保存图片
			saveImgToLocal(id,index=0) {
				// this.$refs[id].toTempFilePath({
				//   success: res => {
				// 	  console.log(111111)
				//     // #ifdef H5
				//     this.isH5Save = true
				//     callback.success({
				//       msg: 'H5请长按图片保存'
				//     })
				//     callback.complete({
				//       msg: 'H5请长按图片保存'
				//     })
				//     // #endif
				
				//     // #ifndef H5
				//     uni.saveImageToPhotosAlbum({
				//       filePath: this.tempFilePath,
				//       success: res1 => {
				//         callback.success({
				//           msg: '保存成功'
				//         })
				//       },
				//       fail: err1 => {
				//         callback.fail(err1)
				//       },
				//       complete: res1 => {
				//         callback.complete(res1)
				//       }
				//     })
				//     // #endif
				//   },
				//   fail: err => {
				//     callback.fail(err)
				//   }
				// })
				// uni.canvasToTempFilePath({
				// 			fileType: "jpg",
				// 			canvasId: id,
				// 			success: function(res) {
				// 				console.log(res)
				// 						}
				// 								});
				// this.$refs[id].toTempFilePath({
				//   success: res => {
				//     console.log(res)
				//     uni.hideLoading()
				//     uni.showToast({
				//       icon: 'none',
				//       title: '文件临时路径：' + res.tempFilePath
				//     })
				//   },
				//   fail: err => {
				//     uni.hideLoading()
				//     uni.showToast({
				//       icon: 'none',
				//       title: JSON.stringify(err)
				//     })
				//   }
				// })
				// uni.previewImage({
				// 			current:index, //预览图片的下标
				// 			urls:arr //预览图片的地址，必须要数组形式
				// 		})
				// uni.showModal({
				// 	title: '提示',
				// 	content: '确定保存到相册吗',
				// 	success: (res) => {
				// 		if (res.confirm) {
							// that.$refs[`firstCanvas${index}`].save({
							//   success: res => {
							//     console.log(res)
							//     uni.hideLoading()
							//     uni.showToast({
							//       icon: 'none',
							//       title: res.msg
							//     })
							//   },
							//   fail: err => {
							//     uni.hideLoading()
							//     uni.showToast({
							//       icon: 'none',
							//       title: JSON.stringify(err)
							//     })
							//   }
							// })
							// uni.canvasToTempFilePath({
							// 	canvasId: `firstCanvas${index}`,
							// 	success: function(res1) {
							// 		uni.saveImageToPhotosAlbum({
							// 			filePath: res1
							// 				.tempFilePath,
							// 			success: function() {
							// 				uni.showToast({
							// 					title: "保存成功",
							// 					icon: "none"
							// 				});
							// 			},
							// 			fail: function() {
							// 				uni.showToast({
							// 					title: "保存失败",
							// 					icon: "none"
							// 				});
							// 			}
							// 		});

							// 	}
							// }, this)
						// } else if (res.cancel) {

						// }
					// }
				// });
			}
		}
	}
</script>

<style lang="scss">
	.img_box {
		position: fixed;
		bottom: 50rpx;
		width: 80%;
		display: flex;
		justify-content: space-around;
		z-index: 100;
		left: 0;
		right: 0;
		margin: auto;

		image {
			width: 100rpx;
			height: 100rpx;
		}
	}

	.swiper-3d {
		padding: 0upx ;
		position:absolute;
		top:0;
		left:0;
		bottom:0;
		right:0;
		margin:auto;
		.s-container {
			height: 900upx;
			width: 100%;

			.swiper-item {
				// max-width: 630upx;
				height: 90%;
				padding: 0upx 20upx;
				box-sizing: border-box;
				position: relative;

			}

			.item-img {
				// position: absolute;
				margin-top: 30upx;
				width: 100%;
				height: 80%;
				// border-radius: 15upx;
				z-index: 5;
				// opacity: 0.7;
				// top: 7%;
				// transform: translateY(-50%);
				// box-shadow:0px 4upx 15upx 0px rgba(153,153,153,0.24);
			}

			.active {
				opacity: 1;
				z-index: 10;
				height: 90%;
				top: -3%;
				transition: all .1s ease-in 0s;
				transform: translateY(0);
			}
		}

		.swiper-dot {
			display: flex;
			justify-content: center;
			align-items: center;

			// padding-top: 10upx;
			.dot {
				margin: 0 10upx;
				width: 15upx;
				height: 15upx;
				border-radius: 50%;
				background: #bbb;

				&.on {
					background: #F4BD48;
				}
			}
		}
	}
</style>
