<template>
	<view class="page">
		<view class="my-top">
			<image class="my_bg" :src="$fun.imgUrl(`/static/u-bg.png`)" style="width: 100%;height: 100%;" mode="">
			</image>
			<!-- head -->
			<!-- 用户信息 -->
			<!-- #ifdef H5  -->
			<view style="height: 100rpx;">

			</view>
			<!-- #endif -->
			<!-- #ifdef MP-WEIXIN -->
			<view style="height: 50rpx;">

			</view>
			<!-- #endif -->
			<!-- #ifdef APP-PLUS -->
			<view style="height: 1rpx;">

			</view>
			<!-- #endif -->
			<view class="user-info" v-if="isLogin">
				<view class="info">
					<view class="nickname">
						<text>{{userInfo.nickname}}</text>
					</view>
					<view style="display: flex;">
						<view class="rank" v-if="userInfo.invitation" @click="copy(userInfo.invitation)">
							<text>邀请码:({{userInfo.invitation}})</text>
						</view>
						<view class="rank" style="margin-left: 20rpx;" v-if="userInfo.id" @click="copy(userInfo.id)">
							<text>ID:({{userInfo.id}})</text>
						</view>
					</view>
				</view>
				<view class="portrait">
					<image :src="isLogin?$fun.imgUrl(userInfo.avatar):'/static/noneLogin.png'"></image>
				</view>
			</view>
			<view class="user-info" v-else @click="onUserInfo">
				<view class="portrait">
					<image :src="isLogin?$fun.imgUrl(userInfo.avatar):'/static/noneLogin.png'"></image>
				</view>
				<view class="info">
					<view class="nickname">
						<text>登录/注册</text>
					</view>
				</view>
			</view>
			<view class="wallet_top" v-if="balanceList.length>0">
				<view class="wallet_i" v-for="(item,index) in balanceList" :key="index"
					@click="$fun.jump(`./wallet/walletDetails?id=${item.id}&name=${item.name}&showMoney=1`)">
					<view class="l">
						<view class="name">
							{{item.name}}
						</view>
					</view>
					<view class="r">
						{{item.money}}
					</view>
				</view>
			</view>
			<view class="vip_box" v-if="isLogin&&userInfo.level_name">
				<image :src="$fun.imgUrl(`/static/vip.png`)" mode="widthFix"></image>
				<view class="vip_info">
					<view class="left">
						<view class="name">
							{{userInfo.level_name}}
						</view>
						<view class="info">
							{{userInfo.level1_name}}
						</view>
					</view>
					<!-- 		<view class="right">
						去升级
					</view> -->
				</view>
			</view>
		</view>
		<view style="height: 30rpx;">

		</view>
		<!-- 订单信息 -->
		<view class="my-service" style="border-radius: 0;">
			<view class="title">
				<text>我的订单</text>
			</view>
			<view class="order-info">
				<view class="list" @click="$fun.jump('/pages/my/myOrder/myOrder?type=all')">
					<view class="icon">
						<image style="width:53rpx;height: 53rpx;" :src="$fun.imgUrl('/static/icon/dfk.png')" mode="widthFix">
						</image>
						<u-badge :offset=[0,-20] type="error" v-if="orderNum[0]" :count="orderNum[0]">
						</u-badge>
					</view>
					<view class="title1">
						<text>待付款</text>
					</view>
				</view>
				<view class="list" @click="$fun.jump('/pages/my/myOrder/myOrder?type=1')">
					<view class="icon">
						<image style="width:53rpx;height: 53rpx;" :src="$fun.imgUrl('/static/icon/dfh.png')" mode="widthFix">
						</image>
						<u-badge :offset=[0,-20] type="error" v-if="orderNum[1]" :count="orderNum[1]">
						</u-badge>
					</view>
					<view class="title1">
						<text>待发货</text>
					</view>
				</view>
				<view class="list" @click="$fun.jump('/pages/my/myOrder/myOrder?type=2')">
					<view class="icon">
						<image style="width:53rpx;height: 53rpx;" :src="$fun.imgUrl('/static/icon/dsh.png')" mode="widthFix">
						</image>
						<u-badge :offset=[0,-20] type="error" v-if="orderNum[2]" :count="orderNum[2]">
						</u-badge>
					</view>
					<view class="title1">
						<text>待收货</text>
					</view>
				</view>
				<view class="list" @click="$fun.jump('/pages/my/myOrder/myOrder?type=3')">
					<view class="icon">
						<image style="width:53rpx;height: 53rpx;" :src="$fun.imgUrl('/static/icon/ywc.png')" mode="widthFix">
						</image>
						<u-badge :offset=[0,-20] type="error" v-if="orderNum[3]" :count="orderNum[3]">
						</u-badge>
					</view>
					<view class="title1">
						<text>已完成</text>
					</view>
				</view>
				<view class="list" @click="$fun.jump('/pages/my/myOrder/myOrder?type=4')">
					<view class="icon">
						<image style="width:53rpx;height: 53rpx;" :src="$fun.imgUrl('/static/icon/yqx.png')" mode="widthFix">
						</image>
						<u-badge :offset=[0,-20] type="error" v-if="orderNum[4]" :count="orderNum[4]">
						</u-badge>
					</view>
					<view class="title1">
						<text>已取消</text>
					</view>
				</view>
			</view>
		</view>
		<view class="banner1">
			<swiper class="screen-swiper square-dot" autoplay indicator-dots="true" circular="true" autoplay="true"
				interval="5000" duration="500">
				<swiper-item v-for="(item,index) in swiperList" :key="index">
					<image :src="$fun.imgUrl(item.image)">
					</image>
				</swiper-item>
			</swiper>
		</view>

		<!-- 我的服务 -->
		<view class="my-service">
			<view class="title">
				<text>我的服务</text>
			</view>
			<view class="service-list">
				<block v-for="(item,index) in serveList" :key="index">
					<!-- #ifndef MP-WEIXIN  -->
					<view class="list" @click="onJump(item)">
						<view class="thumb">
							<image :src="$fun.imgUrl(item.src)" mode="widthFix"></image>
						</view>
						<view class="name">
							<text>{{item.title}}</text>
						</view>
					</view>
					<!-- #endif -->
					<!-- #ifdef MP-WEIXIN  -->
					<view class="list" style="position: relative;" v-if="item.title.indexOf('客服')!=-1">
						<button style="background: transparent;border: none;opacity: 0;height: 100%;width: 100%;position: absolute;top: 0;left: 0;z-index:10;" open-type="contact"></button>
						<view class="thumb">
							<image :src="$fun.imgUrl(item.src)" mode="widthFix"></image>
						</view>
						<view class="name">
							<text>{{item.title}}</text>
						</view>
					</view>
					<view class="list" @click="onJump(item)" v-else>
						<view class="thumb">
							<image :src="$fun.imgUrl(item.src)" mode="widthFix"></image>
						</view>
						<view class="name">
							<text>{{item.title}}</text>
						</view>
					</view>
					<!-- #endif -->
				</block>

			</view>

		</view>
		<!-- 为你推荐 -->
		<z-modal :show="modalControl" :btnGroup="btnGroup" :contentType="2" :titleText="'绑定推荐码'" @cancle="cancle"
			@sure="sure"></z-modal>
		<!-- 提示框 -->
		<yz-qr ref="qrPath" :text="text" :size="size" :colorDark="colorDark" @getQrPath="getQrPath"
			:colorLight="colorLight"> </yz-qr>
		<!-- tabbar -->
		<TabBar></TabBar>
	</view>
</template>

<script>
	import TabBar from '@/components/TabBar/TabBar.vue';
	import zModal from '@/components/z-modal/z-modal.vue'
	import uniCopy from '@/components/xb-copy/uni-copy.js'
	import yzQr from "@/components/yz-qr/yz-qr1.vue"
	export default {
		components: {
			yzQr,
			TabBar,
			zModal
		},
		data() {
			return {
				scrollTop: 0,
				isHotline: false,
				goodsList1: [],
				swiperList: [],
				orderNum: [],
				//用户信息
				userInfo: {
					share: []
				},
				// 钱包列表
				balanceList: [],
				serveList: [],
				recommendList: [],
				modalControl: false,
				btnGroup: [
					// 	{
					// 	text: '取消',
					// 	color: '#FFFFFF',
					// 	bgColor: '#999999',
					// 	width: '150rpx',
					// 	height: '80rpx',
					// 	shape: 'fillet',
					// 	eventName: 'cancle'
					// }, 
					{
						text: '确定',
						color: '#FFFFFF',
						bgColor: '#007AFF',
						width: '150rpx',
						height: '80rpx',
						shape: 'fillet',
						eventName: 'sure'
					}
				],
				text: '',
				size: 150,
				colorDark: '#000000',
				colorLight: '#ffffff',
				show: false,
				imgCode: ''
			};
		},
		onShow() {
			uni.hideTabBar();
			this.init()
		},
		onPageScroll(e) {
			this.scrollTop = e.scrollTop;
		},
		computed: {
			isLogin() {
				return uni.getStorageSync('token') ? true : false
			}
		},
		methods: {
			/**
			 * 首页轮播图
			 */
			getIndexSwipter() {
				this.$fun.ajax.post('News/lists', {
					type: 'index'
				}).then(res => {
					if (res.status == 1) {
						this.swiperList = res.data
					}
				})
			},
			// 绘制二维码  H5
			getQrPath(tempFilePath, text) {
				var that = this;
				// console.log(tempFilePath)
				// this.lookImg(tempFilePath)
				this.imgCode = tempFilePath
				this.show = true
			},
			showImg(item) {
				console.log(this.$refs.qrPath.couponQrCode)
				this.$refs.qrPath.couponQrCode(item)
			},
			/**
			 * 初始化数据
			 */
			async init() {
				await this.getUserInfo()
				await this.getWallet()
				// await this.goodsList()
				await this.getimg()
				await this.getIndexSwipter()
			},
			getimg() {
				this.$fun.ajax.post('category/list', {
					type: 'recommend'
				}).then(res => {
					if (res.status == 1) {
						this.recommendList = res.data
					}
				})
			},
			async copy(text, num = 1) {
				if (num == 10086) {
					await this.getUserInfo();
					await this.showImg(this.userInfo.code);
					console.log(123)
					await uniCopy({
						content: this.userInfo.code,
						success: (res) => {
							uni.showToast({
								title: res,
								icon: 'none'
							})
						},
						error: (e) => {
							uni.showToast({
								title: e,
								icon: 'none',
								duration: 3000,
							})
						}
					})
					return
				}
				await uniCopy({
					content: text,
					success: (res) => {
						uni.showToast({
							title: res,
							icon: 'none'
						})
					},
					error: (e) => {
						uni.showToast({
							title: e,
							icon: 'none',
							duration: 3000,
						})
					}
				})

			},
			sure(e) {
				if (e.inputText == '') {
					this.$fun.msg('请输入邀请码')
					return;
				}
				this.$fun.ajax.post('user/setPid', {
					invitation: e.inputText
				}).then(res => {
					if (res.status == 1) {
						this.$fun.msg(res.msg);
						this.modalControl = false
						setTimeout(() => {
							this.init()
						}, 1200)
					}
				})
			},
			cancle(e) {
				this.modalControl = false
			},
			onGetPhoneNumber(e) {
				let _this = this;
				if (e.detail.errMsg == "getPhoneNumber:fail user deny") { //用户决绝授权  
					//拒绝授权后弹出一些提示  
					this.$fun.msg('授权失败无法获取手机号');
				} else {
					//允许授权  
					uni.login({
						provider: 'weixin',
						success: function(loginRes) {
							let prams = {
								code: loginRes.code,
								encryptedData: e.detail.encryptedData,
								iv: e.detail.iv,
								newcode: ''
							}
							_this.$fun.ajax.post('user/getMobile', prams).then(res => {
								if (res.status == 1) {
									_this.$fun.msg(res.msg);
									_this.getUserInfo()

								} else {
									_this.$fun.msg(res.msg);
								}
							})
						}
					});

				}
			},
			/**
			 * 获取推荐商品
			 */
			goodsList() {
				this.$fun.ajax.post('goods/list', {}).then(res => {
					if (res.status == 1) {
						this.goodsList1 = res.data
					}
				})
			},
			/**
			 * 获取用户信息
			 */
			async getUserInfo() {
				await this.$fun.ajax.post('user/index', {}).then(res => {
					if (res.status == 1) {
						this.userInfo = res.data
						this.userInfo.code = res.data.code
						this.serveList = res.data.serveList
						// if (!this.userInfo.mobile) {
						// 	this.$fun.msg('请先绑定手机号')
						// 	this.$fun.jump('/pages/my/login/loginbind?token=' + uni.getStorageSync(
						// 		'token'), 0, 1200)
						// } else {
							if (this.userInfo.pid == 0) {
								this.modalControl = true
							}
						// }
					}
				})
			},
			/**
			 * 获取钱包
			 */
			getWallet() {
				this.$fun.ajax.post('wallet/balance', {}).then(res => {
					if (res.status == 1) {
						this.balanceList = res.data
					}
				})
				this.$fun.ajax.post('order/getOrderIcon', {}).then(res => {
					if (res.status == 1) {
						this.orderNum = res.data
					}
				})
			},
			/**
			 * 关注跳转
			 */
			onCollect(type) {
				switch (type) {
					case 'goods':
						uni.navigateTo({
							url: '/pages/GoodsOn/GoodsOn'
						})
						break;
					case 'content':
						uni.navigateTo({
							url: '/pages/ContentCollection/ContentCollection'
						})
						break;
					case 'record':
						uni.navigateTo({
							url: '/pages/BrowsingHistory/BrowsingHistory'
						})
						break;
				}
			},
			/**
			 * 订单
			 */
			onSkipOrder(type) {
				// if (type === 5) {
				// 	uni.navigateTo({
				// 		url: '/pages/AfterSalesOrder/AfterSalesOrder',
				// 	})
				// 	return;
				// }
				uni.navigateTo({
					url: '/pages/my/MyOrderList/MyOrderList?type=' + type,
				})
			},
			/**
			 * 钱包跳转点击
			 */
			onWallet(type) {
				switch (type) {
					case 'integral':
						uni.navigateTo({
							url: '/pages/IntegralDetails/IntegralDetails',
						})
						break;
					case 'coupon':
						uni.navigateTo({
							url: '/pages/MyCoupon/MyCoupon',
						})
						break;
					case 'wallet':
						uni.navigateTo({
							url: '/pages/MyWallet/MyWallet',
						})
						break;
					case 'SignIn':
						uni.navigateTo({
							url: '/pages/SignIn/SignIn',
						})
						break;
					case 'payment':
						uni.navigateTo({
							url: '/pages/PaymentCode/PaymentCode',
						})
						break;
				}
			},
			/**
			 * 我的服务点击
			 */
			onJump(item) {
				console.log('8888', item);
				if (uni.getStorageSync('userinfo')) {
					if (item.title.indexOf('客服') != -1) {
						// #ifdef H5
						location.href = item.url
						// #endif
						// #ifdef APP-PLUS
						const platform = uni.getSystemInfoSync().platform
						const webview = plus.webview.create('', this.$fun.baseUrl()); // 创建一个webview
						switch (platform) {
							case 'android':
								webview.loadURL(item.url, {
									'Referer': this.$fun.baseUrl()
								});
								break;
							case 'ios':
								webview.loadURL(item.url, {
									'Referer': 'xiaoyutusc.com://'
								}); //解决ios支付完成后，打开safari浏览器的bug
								break;
							default:
								webview.loadURL(item.url, {
									'Referer': this.$fun.baseUrl()
								});
								break;
						}
						// #endif
					} else {
						if (item.url.indexOf('?') == -1) {
							this.$fun.jump(item.url + `?name=${item.title}`)
						} else {
							this.$fun.jump(item.url + `&name=${item.title}`)
						}
					}
				} else {
					this.$fun.msg('请先登录后操作!')
				}
			},
			/**
			 * 设置点击
			 */
			onSetting() {
				this.$fun.jump('./set/set')
			},
			// /**
			//  * 消息点击
			//  */
			onMessage() {
				uni.navigateTo({
					url: '/pages/Message/Message'
				})
			},
			/**
			 * 会员点击
			 */
			onMmeberVip() {
				uni.navigateTo({
					url: '/pages/MembersOpened/MembersOpened',
				})
			},
			/**
			 * 跳转点击
			 * @param {String} type 跳转类型
			 */
			onSkip(type) {
				switch (type) {
					case 'goods':
						uni.navigateTo({
							url: '/pages/GoodsDetails/GoodsDetails',
							animationType: 'zoom-fade-out',
							animationDuration: 200
						})
						break;
				}
			},
			/**
			 * 用户信息点击
			 * @param {Number} type
			 */
			onUserInfo() {
				uni.navigateTo({
					url: '/pages/my/login/login'
				})
			}
		}
	}
</script>

<style lang="scss">
	@import 'my.scss';

	// 绑定微信公众号
	.notice-box {
		width: 650rpx;
		margin: 0 auto;
		height: 70rpx;
		// background: rgba(253, 239, 216, 1);
		background: #FFFFFF;
		padding: 0 35rpx;

		.notice-detail {
			font-size: 24rpx;

			font-weight: 400;
			color: #000000;
		}

		.bindPhone {
			width: 135rpx;
			line-height: 52rpx;
			background: linear-gradient(90deg, rgba(233, 180, 97, 1), rgba(238, 204, 137, 1));
			border-radius: 26rpx;
			padding: 0;
			font-size: 26rpx;

			font-weight: 500;
			// color: rgba(255, 255, 255, 1);
			color: #000000;
		}
	}

	::v-deep .u-column {
		width: 50%;

		.list {
			width: 94% !important;
			margin-left: 3%;
			height: auto !important;
		}
	}
</style>