<template>
	<view class="page">
		<view class="order-tab">
			<view class="tab" :class="{ 'action': OrderType == 1 }" @click="changeType(1)">
				<text>快递</text>
				<text class="line"></text>
			</view>
			<view class="tab" :class="{ 'action': OrderType == 2 }" v-if="orderInfoConfig.atype == 1"
				@click="changeType(2)">
				<text>寄售</text>
				<text class="line"></text>
			</view>
		</view>
		
		<!-- 地址 -->
		<block v-if="OrderType == 1">
			<view class="address-data" v-if="orderInfoConfig.address.pro">
				<view class="address-list" @click="$fun.jump(`/pages/my/addressOperation/addressList?type=1`)">
					<view class="address-list-b">
						<view class="l">
							<view class="list">
								<text>{{ orderInfoConfig.address.pro }}{{ orderInfoConfig.address.city }}{{
									orderInfoConfig.address.area }}</text>
							</view>
							<view class="list">
								<text class="address">{{ orderInfoConfig.address.address }}</text>
							</view>
							<view class="list">
								<text>{{ orderInfoConfig.address.name }}</text>
								<text>{{ orderInfoConfig.address.mobile }}</text>
							</view>
						</view>
						<view class="r">
							<view class="more-content">
								<text class="content"></text>
								<text class="iconfont icon-more more"></text>
							</view>
						</view>
					</view>
					<view class="list">
						<text class="tips">(如果快递不方便接收，您可以选择暂时寄存服务)</text>
					</view>
				</view>
				<view class="bar">

				</view>
			</view>
			<view class="address-data" v-else>
				<view class="address-list" style="display: flex;flex-direction: column;align-items: center;"
					@click="$fun.jump(`/pages/my/addressOperation/addressList?type=1`)">

					<view class="address-list-b">
						<view class="l">
							<view class="list">
								选择地址
							</view>
						</view>
						<view class="r">
							<view class="more-content">
								<text class="content"></text>
								<text class="iconfont icon-more more"></text>
							</view>
						</view>
					</view>
					<view class="list">
						<text class="tips">(如果快递不方便接收，您可以选择暂时寄存服务)</text>
					</view>
				</view>
			</view>
		</block>
		<block v-else>
			<picker class="settess" :range="splitArr('bank', 'bankcode', withdrawList)" @change="withdrawListChange">
				<view class="address-data" v-if="withdrawInfo.bank">
					<view class="address-list">
						<view class="address-list-b">
							<view class="l">
								<view class="list">
									<text>{{ withdrawInfo.bank }}</text>
								</view>
								<view class="list">
									<text class="address">{{ withdrawInfo.bankcode }}</text>
								</view>
								<view class="list">
									<text>{{ withdrawInfo.mobile }}</text>
								</view>
							</view>
							<view class="r">
								<view class="more-content">
									<text class="content"></text>
									<text class="iconfont icon-more more"></text>
								</view>
							</view>
						</view>
						<view class="list">
							<text class="tips">(请合理安排，您的寄售方式)</text>
						</view>
					</view>
					<view class="bar">

					</view>
				</view>
				<view class="address-data" v-else>
					<view class="address-list" style="display: flex;flex-direction: column;align-items: center;">

						<view class="address-list-b">
							<view class="l">
								<view class="list">
									选择寄售方式
								</view>
							</view>
							<view class="r">
								<view class="more-content">
									<text class="content"></text>
									<text class="iconfont icon-more more"></text>
								</view>
							</view>
						</view>
						<view class="list">
							<text class="tips">(如果快递不方便接收，您可以选择暂时寄存服务)</text>
						</view>
					</view>
				</view>

			</picker>
		</block>
		<!-- 商品 -->
		<view class="goods-data">
			<view class="goods-title">
				<text>商品信息</text>
			</view>
			<view class="goods-list">
				<view class="list" v-for="(item, index) in orderInfoConfig.goods" :key="index">
					<view class="thumb">
						<image :src="$fun.imgUrl(item.image)" mode=""></image>
					</view>
					<view class="item">
						<view class="title">
							<text class="name one-omit">{{ item.name }}</text>
							<text class="attr">{{ item.sign }}</text>
						</view>
						<view class="price-number">
							<view class="price">
								<text class="min">￥</text>
								<text class="max">{{ item.money }}</text>
								<text class="max" v-if="item.dikou != 0"> +</text>
								<text class="max" v-if="item.dikou != 0">
									{{ item.dikou }}</text>
								<!-- <text class="min">.00</text> -->
							</view>
							<view class="number">
								<text>x {{ item.num }}</text>
							</view>
						</view>
						<view class="title" v-if="item.freight * 1 > 0">
							<text class="attr">运费:{{ item.freight }}</text>
						</view>
					</view>
				</view>
			</view>
			<view class="delivery" v-if="orderInfoConfig.tid">
				<div class="list">
					<view class="title">接点人</view>
					<view class="content">
						<input type="text" v-model="params.tid" placeholder="请填写接点人">
					</view>
				</div>
			</view>
			<view class="delivery">
				<div class="list">
					<view class="title">留言</view>
					<view class="content">
						<input type="text" v-model="params.memo" placeholder="选填,建议先和商家沟通确认">
					</view>
				</div>
			</view>
		</view>
		<!-- 优惠 -->
		<!-- <view class="discounts-data">
			<view class="discounts">
				<div class="list" @click="$refs['InvoiceInfo'].show()">
					<view class="title">发票</view>
					<view class="content">
						<text>不开发票</text>
						<text class="iconfont icon-more"></text>
					</view>
				</div>
				<div class="list" @click="$refs['UseCoupon'].show()">
					<view class="title">优惠券</view>
					<view class="content">
						<text>无可用</text>
						<text class="iconfont icon-more"></text>
					</view>
				</div>
				<div class="list">
					<view class="title">积分</view>
					<view class="content">
						<text>共300，满1000可用</text>
					</view>
				</div>
			</view>
		</view>
		
		 --><!-- 订单金额 -->
		<view class="order-price">
			<view class="price-list">
				<view class="list">
					<view class="title">
						<text>商品金额</text>
					</view>
					<view class="price">
						<text>￥{{ orderInfoConfig.money }} <text style="margin-left: 10rpx;"
								v-if="orderInfoConfig.dikou != 0"> + {{ orderInfoConfig.dikou }}</text> </text>
					</view>
				</view>
				<!-- 	<view class="list">
					<view class="title">
						<text>会员折扣</text>
					</view>
					<view class="price">
						<text>-￥19.00</text>
					</view>
				</view> -->
				<view class="list" v-for="(item, index) in orderInfoConfig.freight_arr">
					<view class="title">
						<text>{{ item.name }}</text>
					</view>
					<view class="price">
						<text class="highlight">{{ item.value }}</text>
					</view>
				</view>
				<view class="list">
					<view class="title">
						<text>运费</text>
					</view>
					<view class="price">
						<text class="highlight">{{ orderInfoConfig.freight }}</text>
					</view>
				</view>
				<!-- <view class="list">
					<view class="title">
						<text>运费险</text>
					</view>
					<view class="price">
						<text class="highlight">+￥0.00</text>
					</view>
				</view> -->
			</view>
		</view>
		<!-- 地址提示 -->
		<!-- 		<view class="address-tips" :style="scrollTop >= 100 ? '':'display:none'">
			<text>黑龙江哈尔滨市道里区爱建路1333号</text>
		</view> -->
		<!-- 底部合计提交 -->
		<view class="footer-submit">
			<view class="price">
				<text class="min">￥</text>
				<text class="max">{{ orderInfoConfig.total }} <text style="margin-left: 10rpx;"
						v-if="orderInfoConfig.dikou != 0"> + {{ orderInfoConfig.dikou }}</text></text>
				<!-- <text class="min">.00</text> -->
			</view>
			<view class="submit" @click="onSubmit">
				<text>提交订单</text>
			</view>
		</view>
		<!-- 发票 -->
		<!-- <invoice-info ref="InvoiceInfo"></invoice-info> -->
		<!-- 优惠券 -->
		<!-- <use-coupon ref="UseCoupon"></use-coupon> -->
	</view>
</template>

<script>
// import InvoiceInfo from '@/components/InvoiceInfo/InvoiceInfo.vue';
// import UseCoupon from '@/components/UseCoupon/UseCoupon.vue'
export default {
	// components:{
	// 	// 发票
	// 	InvoiceInfo,
	// 	// 优惠券
	// 	UseCoupon,
	// },
	data() {
		return {
			scrollTop: 0,
			params: {
				memo: '',
				name: '',
				phone: '',
				tid: ''
			},
			orderInfoConfig: {
				atype: 0,
				address: {}
			},
			OrderType: 1,
			withdrawList: [],
			withdrawInfo: {},
			isJump: false,
			pin_status: 0,
			tuanId: 0
		};
	},

	onLoad(option) {
		this.params = option
		if (option.isJump) {
			this.isJump = option.isJump
		}
		if (option.pin_status) {
			this.pin_status = option.pin_status;
		}
		if (option.tuanId) {
			this.tuanId = option.tuanId
		}
		this.params.memo = ''
		this.params.tid = ''

	},
	onPageScroll(e) {
		this.scrollTop = e.scrollTop;
	},
	onShow() {
		this.init(this.params)
	},
	methods: {
		/**
		 * 初始化数据
		 */
		init(option) {
			this.$fun.ajax.post('/bank/lists', {}).then(res => {
				if (res.status == 1) {
					this.withdrawList = res.data
				}
			})
			let data = {}
			if (uni.getStorageSync('shopPay')) {
				data = JSON.parse(uni.getStorageSync('shopPay'))
			}
			this.params.sign = data.sign
			if (uni.getStorageSync('address')) {
				data.aid = JSON.parse(uni.getStorageSync('address')).id
			}
			console.log(data)
			if (option.type == 1) {
				this.$fun.ajax.post('order/getgoods', {
					...data
				})
					.then(res => {
						if (res.status == 1) {
							this.orderInfoConfig = res.data
							if (uni.getStorageSync('address')) {
								this.orderInfoConfig.address = JSON.parse(uni.getStorageSync('address'))
								uni.removeStorageSync('address')
							}
						}
					});
			} else {
				this.getcartsure(option)
			}

		},
		changeType(type) {
			if (this.OrderType == type) {
				return
			}
			this.params.name = ''
			this.params.phone = ''
			this.OrderType = type
		},
		splitArr(str, str1, arr) {
			let strArr = []
			for (var i = 0; i < arr.length; i++) {
				strArr.push(`${arr[i][str]}`)
			}
			console.log(strArr)
			return strArr
		},
		withdrawListChange(e) {
			this.withdrawInfo = this.withdrawList[e.detail.value];
		},
		/**
		 * 提交订单
		 */
		onSubmit() {
			if (this.orderInfoConfig.address == '' && this.OrderType == 1) {
				return this.$fun.msg('请先添加收货地址');
			}
			if (this.OrderType == 2) {
				if (!this.withdrawInfo.mobile) {
					this.$fun.msg('请选择寄售方式');
					return
				}
				// if (!this.params.phone || this.params.phone.length < 11) {
				// 	this.$fun.msg('请输入正确的手机号');
				// 	return
				// }
			}
			this.orderInfo();
		},
		getcartsure(data) {
			this.$fun.ajax.post('order/getgoods', {
				cid: data.id,
			})
				.then(res => {
					if (res.status == 1) {
						this.orderInfoConfig = res.data;
						if (uni.getStorageSync('address')) {
							this.orderInfoConfig.address = JSON.parse(uni.getStorageSync('address'))
							uni.removeStorageSync('address')
						}
					}
				});
		},
		orderInfo(status = 0) {
			let _this = this;
			let dataType;
			if (this.params.type == 1) {
				dataType = {
					gid: this.params.id,
					num: this.params.num,
					sign: this.params.sign,
					memo: this.params.memo,
					pin_status: this.pin_status,
					tuanId: this.tuanId
				};
			} else {
				dataType = {

					cid: this.params.id,
				};
			}
			if (this.OrderType == 1) {
				dataType.aid = this.orderInfoConfig.address.id
			} else {
				dataType.aid = this.withdrawInfo.id;
			}
			if (this.orderInfoConfig.atype == 1 && this.OrderType == 2) {
				if (!uni.getStorageSync('singImg')) {
					let prams = ""
					for (let key in _this.params) {
						prams += `${key}=${_this.params[key]}&`
					}
					this.$fun.jump(`./sing/contract?${prams}`)
					return
				}
				dataType.contract = uni.getStorageSync('singImg');
			}

			dataType.type = this.OrderType - 1;
			dataType.status = status
			if (this.orderInfoConfig.tid) {
				dataType.tid = this.params.tid
				if (!dataType.tid) {
					this.$fun.msg('请输入接点人')
					return
				}
			}

			this.$fun.ajax.post('order/create', {
				...dataType
			})
				.then(res => {
					if (res.status == 1) {
						if (res.data == 'status') {

							uni.showModal({
								title: '提示',
								content: res.msg,
								success: function (res) {
									if (res.confirm) {
										console.log('用户点击确定');
										_this.orderInfo(1)
									} else if (res.cancel) {
										console.log('用户点击取消');
									}
								}
							})
							return
						}
						uni.removeStorageSync('shopPay')
						uni.removeStorageSync('singImg')
						// #ifdef H5
						var ua = navigator.userAgent.toLowerCase();
						var isWeixin = ua.indexOf('micromessenger') !== -1;
						let type = 0;
						let jumpUrl =
							`./orderPay?oid=${res.data}&gid=${this.params.id}&isJump=${this.isJump}`;
						if (!isWeixin) {
							type = 0
						} else {
							type = 1
						}
						// #endif
						// #ifdef APP-PLUS || MP-WEIXIN
						let type = 0;
						let jumpUrl =
							`./orderPay?oid=${res.data}&gid=${this.params.id}&isJump=${this.isJump}`;
						// #endif


						this.$fun.jump(jumpUrl += `&isWeixin=${type}`, 2)
					} else {
						this.$fun.msg(res.msg);
					}
				});
		},
		/**
		 * 跳转点击
		 * @param {String} type 跳转类型
		 */
		onSkip(type) {
			switch (type) {
				case 'address':
					uni.navigateTo({
						url: '/pages/AddressList/AddressList',
					})
					break;
			}
		}
	}
}
</script>

<style scoped lang="scss">
.page {
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	// height: 100%;
	background-color: #f6f6f6;
	padding-bottom: 180rpx;
}

/* 地址 */
.address-data {
	position: relative;
	padding: 10rpx 4%;
	background-color: #FFFFFF;
	border-radius: 0 0 20rpx 20rpx;
	overflow: hidden;

	.bar {
		position: absolute;
		left: 0;
		bottom: 0;
		width: 100%;
		height: 10rpx;
		background-color: #CCCCCC;
		background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
		background-size: 72rpx 72rpx;
	}

	.address-list {
		width: 100%;

		.address-list-b {
			width: 100%;
			display: flex;
			justify-content: space-between;
			align-items: center;
		}

		.list {
			display: flex;
			align-items: center;
			width: 100%;
			height: 60rpx;

			text {
				font-size: 24rpx;
				color: #555555;
				margin-right: 10rpx;
			}

			.address {
				font-size: 32rpx;
				color: #222222;
			}

			.tips {
				color: $base;
			}
		}
	}
}

/* 商品 */
.goods-data {
	padding: 10rpx 4%;
	background-color: #FFFFFF;
	border-radius: 20rpx;
	margin: 20rpx auto;

	.goods-title {
		display: flex;
		align-items: center;
		width: 100%;
		height: 80rpx;

		text {
			font-size: 26rpx;
			color: #222222;
		}
	}

	.goods-list {
		width: 100%;

		.list {
			display: flex;
			align-items: center;
			width: 100%;
			height: 200rpx;

			.thumb {
				display: flex;
				align-items: center;
				width: 30%;
				height: 100%;

				image {
					width: 160rpx;
					height: 160rpx;
					border-radius: 10rpx;
				}
			}

			.item {
				width: 70%;
				height: 160rpx;

				.title {
					display: flex;
					flex-direction: column;
					// justify-content: center;
					width: 100%;
					height: 80rpx;

					.name {
						font-size: 28rpx;
						color: #222222;
					}

					.attr {
						font-size: 24rpx;
						color: #C0C0C0;
					}
				}

				.price-number {
					display: flex;
					align-items: center;
					justify-content: space-between;
					width: 100%;
					height: 60rpx;

					.price {
						display: flex;
						align-items: center;

						text {
							color: $price-clor;
							font-weight: bold;
						}

						.min {
							font-size: 26rpx;
						}

						.max {
							font-size: 32rpx;
						}
					}

					.number {
						display: flex;
						align-items: center;

						text {
							font-size: 26rpx;
							color: #222222;
						}
					}
				}

				.tag {
					display: flex;
					align-items: center;
					width: 100%;
					height: 40rpx;

					text {
						padding: 2rpx 12rpx;
						color: $base;
						border: 2rpx solid $base;
						border-radius: 40rpx;
						font-size: 24rpx;
					}
				}
			}
		}
	}

	.delivery {
		width: 100%;

		.list {
			display: flex;
			align-items: center;
			justify-content: space-between;
			width: 100%;
			height: 80rpx;

			.title {
				font-size: 26rpx;
				color: #555555;
			}

			.content {
				display: flex;
				align-items: center;
				height: 40rpx;

				text {
					font-size: 26rpx;
					color: #222222;
				}

				.iconfont {
					// font-size: 24rpx;
					margin-top: 6rpx;
					margin-left: 10rpx;
				}

				.icon-check {
					font-size: 34rpx;
				}

				input {
					height: 80%;
					font-size: 26rpx;
					color: #222222;
					text-align: right;
				}
			}
		}
	}
}

/* 优惠 */
.discounts-data {
	width: 100%;
	margin: 20rpx auto;
	background-color: #FFFFFF;
	border-radius: 20rpx;

	.discounts {
		padding: 0 4%;

		.list {
			display: flex;
			align-items: center;
			justify-content: space-between;
			width: 100%;
			height: 80rpx;

			.title {
				font-size: 26rpx;
				color: #555555;
			}

			.content {
				display: flex;
				align-items: center;
				height: 40rpx;

				text {
					font-size: 26rpx;
					color: #222222;
				}

				.iconfont {
					// font-size: 24rpx;
					margin-top: 6rpx;
					margin-left: 10rpx;
				}

				.icon-check {
					font-size: 34rpx;
				}

				input {
					height: 80%;
					font-size: 26rpx;
					color: #222222;
					text-align: right;
				}
			}
		}
	}
}

/**
	 * 地址提示
	 */
.address-tips {
	position: fixed;
	left: 0;
	bottom: 100rpx;
	z-index: 100;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;
	height: 60rpx;
	padding: 0 4%;
	background-color: #fef2ce;

	text {
		font-size: 26rpx;
		color: #fbbd08;
	}
}

/* 订单金额 */
.order-price {
	width: 100%;
	margin: 20rpx auto;
	background-color: #FFFFFF;
	border-radius: 20rpx;

	.price-list {
		padding: 0 4%;

		.list {
			display: flex;
			align-items: center;
			justify-content: space-between;
			width: 100%;
			height: 80rpx;

			.title {
				display: flex;
				align-items: center;

				text {
					font-size: 26rpx;
					color: #555555;
				}
			}

			.price {
				display: flex;
				align-items: center;

				text {
					font-size: 26rpx;
					font-weight: bold;
					color: $price-clor;
				}

				.highlight {
					color: $base;
				}
			}
		}
	}
}

/* 顶部合计提交 */
.footer-submit {
	position: fixed;
	left: 0;
	bottom: 0;
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 100%;
	height: 100rpx;
	background-color: #FFFFFF;
	padding: 0 4%;

	.price {
		display: flex;
		align-items: flex-end;

		text {
			font-weight: bold;
			color: $price-clor;
		}

		.min {
			font-size: 32rpx;
		}

		.max {
			font-size: 48rpx;
		}
	}

	.submit {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 200rpx;
		height: 70rpx;
		background-color: $base;
		border-radius: 70rpx;

		text {
			font-size: 26rpx;
			color: #FFFFFF;
		}
	}
}

/* 订单tab */
.order-tab {
	// position: fixed;
	left: 0;
	top: 0rpx;
	/* #ifdef APP-PLUS */
	top: 0;
	/* #endif */
	z-index: 10;
	display: flex;
	align-items: center;
	justify-content: space-around;
	width: 100%;
	height: 100rpx;
	background-color: #FFFFFF;

	.tab {
		position: relative;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 20%;
		height: 80%;

		text {
			font-size: 26rpx;
			color: #959595;
		}
	}

	.action {
		text {
			color: #222222;
		}

		.line {
			position: absolute;
			left: 50%;
			bottom: 0;
			width: 60rpx;
			height: 6rpx;
			background: linear-gradient(to right, $base, #f6f6f6);
			transform: translate(-50%, 0);
		}
	}
}
</style>