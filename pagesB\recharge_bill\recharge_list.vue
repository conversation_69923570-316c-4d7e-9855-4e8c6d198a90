<template>
	<view>
		<!-- 钱包记录 -->
		<view class="recharge-list u-flex" v-for="item in rechargeList" :key="item.id">
			<!-- <image class="head-img u-m-r-20" :src="$IMG_URL+item.avatar" mode=""></image> -->
			<view class="list-content">
				<view class="title-box u-flex u-row-between" style="margin-bottom:10rpx">
					<text class="title u-ellipsis-1">{{ item.order_sn }}</text>
					<view class="money" v-if="type==0">
						<text  class="minus font-OPPOSANS" style="color:#c0c0c0;font-weight:normal">{{ item.status==0?'待支付':item.status==1?'待到账':item.status==2?'已完成':item.status==3?'已取消':'' }}</text>
					</view>
					<view class="money" v-else>
						<text  class="minus font-OPPOSANS" style="color:#c0c0c0;font-weight:normal">{{ item.status==0?'待支付':item.status==1?'已生效':item.status==2?'已结束':item.status==3?'已取消':'' }}</text>
					</view>
				</view>
				<view class="title-box u-flex u-row-between" style="margin-bottom:10rpx">
					<text class="title u-ellipsis-1">{{ item.name }}</text>
					<view class="money">
						<text  class="minus font-OPPOSANS">￥{{ item.money }}</text>
					</view>
				</view>
				<view class="time" style="text-align:right">{{ $u.timeFormat(item.createtime, 'yyyy-mm-dd hh:MM') }}</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				rechargeList:[],
				type:0
			}
		},
		onLoad(option){
			if(option.type){
				this.type=option.type
				uni.setNavigationBarTitle({
					title:option.name
				})
			}
			this.getRecharge_list()
		},
		methods: {
			//充值记录列表
			getRecharge_list(){
				if(this.type==0){
					this.$fun.ajax.post('bill/getList',{}).then(res=>{
						this.rechargeList = res.data
					})
				}else{
					this.$fun.ajax.post('project/getList', {}).then(res => {
						if (res.status == 1) {
							this.rechargeList = res.data
						}
					})
				}
				
			}
		}
	}
</script>

<style lang="scss">
	page{
		background:#FFFFFF
	}
	// 钱包记录
	.recharge-list {
		width: 750rpx;
		padding: 30rpx;
		background-color: #ffff;
		border-bottom: 1rpx solid #eeeeee;

		.head-img {
			width: 70rpx;
			height: 70rpx;
			border-radius: 50%;
			background: #ccc;
		}

		.list-content {
			justify-content: space-between;
			align-items: flex-start;
			flex: 1;

			.title {
				font-size: 28rpx;
				color: #333;
				width: 400rpx;
			}

			.time {
				color: #c0c0c0;
				font-size: 22rpx;
			}
		}

		.money {
			font-size: 28rpx;
			font-weight: bold;

			.add {
				color: #7063d2;
			}

			.minus {
				color: #333333;
			}
		}
	}

</style>
