<template>
	<view class="page">
		<!-- 文章数据 -->
		<mescroll-body ref="mescrollRef" @init="mescrollInit" @down="downCallback" @up="upCallback" :down="downOption"
			:up="upOption" :top="0">
			<view class="store_info store1">
				<view class="classify_store_top">
					<image :src="$fun.imgUrl(info.logo)"></image>
					<view class="info">
						<view class="title">
							{{info.buname}}
						</view>
						<view class="text">
							共有{{info.num}}商品
						</view>
					</view>
				</view>
				<view class="store_btn" @click="$fun.jump(`/pages/classify/classify_store?id=${info.id}&name=${info.buname}`)">
					进入店铺
				</view>
				<view class="store_address">
					{{info.province}}{{info.city}}{{info.area}}
				</view>
			</view>
			<view class="store_info" style="padding: 32rpx;">
				<view class="info_item">
					<view class="t">{{info.buname}} </view>
				</view>
				<view class="info_item">
					<view class="t">{{info.address}} </view>
				</view>
				<!-- <view class="info_item">
					<view class="t">
						微信号:{{info.wechat}}
					</view>
				</view>
				<view class="info_item" @click="callPhone(info.mobile)">
					<view class="t">
						手机号:{{info.mobile}}({{info.username}})
					</view>
				</view> -->
				<view class="info_item flex">
					<view class="i">商家介绍</view>
					<view class="info_content" >
						{{ info.content }}
					</view>
					<view class="mt_box">
						<image @click="$fun.lookImg($fun.imgUrl(itme))" v-for="(itme,index) in images_arr" :key="index"
							:src="$fun.imgUrl(itme)" mode="widthFix"></image>
					</view>
				</view>
			</view>
			<view style="display: flex;justify-content: space-between;width: 95%;margin: 0 auto;">
				<view class="submit-btn" style="width: 90%;" @click="goNearby()">
					<text>去导航</text>
				</view>
			</view>
		</mescroll-body>
		<!-- <view class="fb_flex1" @click="$fun.clickKf(`https://work.weixin.qq.com/kfid/kfc2bd7767080c3a196`)">
			<text>平台</text>
			<text>客服</text>
		</view> -->
		<passkeyborad ref="passkeyborad" @configPay="configPay" :show="show" @close="onSubmit" :payTitle="payTitle"
			:payMoney="orderInfo.money+'' || 0+''"></passkeyborad>
	</view>
</template>

<script>
	// #ifdef H5
	var wx = require('@/util/jweixin.js')
	// #endif
	// 引入mescroll-mixins.js
	import MescrollMixin from "@/components/mescroll-uni/mescroll-mixins.js";
	export default {
		mixins: [MescrollMixin], // 使用mixin
		data() {
			return {
				mescroll: null, // mescroll实例对象 (此行可删,mixins已默认)
				// 下拉刷新的配置(可选, 绝大部分情况无需配置)
				downOption: {},
				// 上拉加载的配置(可选, 绝大部分情况无需配置)
				upOption: {},
				swiperList: [],
				categoryList: [],
				goodsList: [],
				id: '',
				info: {},
				buyType: 1,
				orderInfo: {},
				images_arr: [],
				payTitle: "",
				show: false
			};
		},
		onLoad(option) {
			this.id = option.id
			this.info.buname = option.name
			uni.setNavigationBarTitle({
				title: option.name ? option.name : '商家详情'
			})
			// this.getConfig();
		},
		methods: {
			getConfig() {
				let _this = this;
				this.$fun.ajax.post('config/wxconfig', {
					url: location.href.split('#')[0]
				}).then(res => {
					if (res.status == 1) {
						let prams = res.data
						wx.config({
							debug: false,
							appId: prams.appId, // 必填，公众号的唯一标识
							timestamp: prams.timestamp, // 必填，生成签名的时间戳
							nonceStr: prams.nonceStr, // 必填，生成签名的随机串
							signature: prams.signature, // 必填，签名
							jsApiList: ["getLocation", "openLocation"] //根据需要看需要哪些SDK的功能
						})
						wx.error(function(err) {
							console.error('微信JS-SDK初始化失败', err);
						});
					}
				})
			},
			callPhone(phone) {

			},

			/**
			 * 初始化
			 */
			async init() {
				await this.getSwiper()
				await this.getCategory()
			},

			/**
			 * 获取轮播图
			 */
			getSwiper() {
				this.$fun.ajax.post('news/lists', {
					type: 'nearby'
				}).then(res => {
					console.log(res)
					if (res.status == 1) {
						this.swiperList = res.data
					}
				})
			},
			/**
			 * 去导航
			 */
			goNearby() {
				uni.openLocation({
					latitude: this.info.lat * 1, //维度
					longitude: this.info.lng * 1, //经度
					name: this.info.name, //目的地定位名称
					scale: 15, //缩放比例
					address: this.info.area + this.info.address //导航详细地址
				})
			},
			/**
			 * 获取分类
			 */
			getCategory() {
				this.$fun.ajax.post('category/list', {
					type: 'nearby'
				}).then(res => {
					console.log(res)
					if (res.status == 1) {
						this.categoryList = res.data
					}
				})
			},
			/*下拉刷新的回调, 有三种处理方式:*/
			downCallback() {
				this.mescroll.endSuccess();
			},
			/*上拉加载的回调*/
			async upCallback(e) {
				this.mescroll.removeEmpty();
				const data = {
					bid: this.id
				};
				this.$fun.ajax.post('business/content', data).then(res => {
					if (res.status == 1) {
						const curList = res.data;
						this.swiperList = res.data.mt;
						this.images_arr = res.data.images_arr;
						this.info = curList; //追加新数据
						this.mescroll.endSuccess(1); //结束加载状态
					}
				})
			},
			gobuy() {
				this.$fun.msg('开发中...')
				return
				if (this.buyType == 1) {
					// this.
				}
			},
			/**
			 * 文章点击
			 */
			onArticle() {
				uni.navigateTo({
					url: '/pages/ArticleDetails/ArticleDetails',
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	@import 'discoverDetails.scss';

	.fb_flex1 {
		position: fixed;
		right: 32rpx;
		bottom: 350rpx;
		width: 110rpx;
		height: 110rpx;
		background: #310FFF;
		border-radius: 50%;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;

		text {
			font-weight: 400;
			font-size: 24rpx;
			color: #FFFFFF;
		}
	}
</style>