{
	"easycom": {
		"^u-(.*)": "uview-ui/components/u-$1/u-$1.vue"
	},
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/home/<USER>",
			"style": {
				"navigationStyle": "custom"
				
			}
		},
		{
			"path": "pages/home/<USER>",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/home/<USER>",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		{
			"path": "pages/classify/classify",
			"style": {
				"navigationBarBackgroundColor": "#310FFF",
				"navigationBarTitleText": ""
			}
		}, {
			"path": "pages/discover/discover",
			"style": {
				"navigationBarBackgroundColor": "#FFFFFF",
				"navigationBarTitleText": "附近代理"
			}
		},
		{
			"path": "pages/cart/cart",
			"style": {
				"navigationBarBackgroundColor": "#310FFF",
				"navigationBarTitleText": ""
			}
		},
		{
			"path": "pages/my/my",
			"style": {
				"navigationStyle": "custom"
			}
		}, {
			"path": "pages/discover/discoverList",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/discover/discoverDetails",
			"style": {
				"navigationBarTitleText": "商家详情",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/my/login/login",
			"style": {
				"navigationBarTitleText": "登录",
				"enablePullDownRefresh": false,
				"navigationStyle": "custom",
				"navigationBarBackgroundColor": "#FFFFFF"
			}

		}, {
			"path": "pages/my/login/loginbind",
			"style": {
				"navigationBarTitleText": "绑定手机号",
				"enablePullDownRefresh": false,
				"navigationBarBackgroundColor": "#FFFFFF"
			}

		}, {
			"path": "pages/my/share/share",
			"style": {
				"navigationBarTitleText": "分享",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/my/addressOperation/addressList",
			"style": {
				"navigationBarTitleText": "收货地址",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/my/addressOperation/addressEdit",
			"style": {
				"navigationBarTitleText": "编辑地址",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/my/signin/signin",
			"style": {
				"navigationBarTitleText": "签到",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/my/set/set",
			"style": {
				"navigationBarTitleText": "设置",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/my/myTeam/myTeam",
			"style": {
				"navigationBarTitleText": "我的社区",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/my/myTeam/myTeam1",
			"style": {
				"navigationBarTitleText": "子账号",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/my/myTeam/myTeam2",
			"style": {
				"navigationBarTitleText": "社区详情",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/my/wallet/walletDetails",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/my/wallet/withdraw",
			"style": {
				"navigationBarTitleText": "提现",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/my/wallet/transfer",
			"style": {
				"navigationBarTitleText": "转账",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/my/wallet/inverted",
			"style": {
				"navigationBarTitleText": "划转",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/my/wallet/recharge",
			"style": {
				"navigationBarTitleText": "充值",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/my/wallet/withdrawList",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/home/<USER>",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/home/<USER>",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/home/<USER>",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/home/<USER>",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/my/set/setPayPwd",
			"style": {
				"navigationBarTitleText": "设置支付密码",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/my/set/setPwd",
			"style": {
				"navigationBarTitleText": "修改登录密码",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/my/set/payment",
			"style": {
				"navigationBarTitleText": "收款方式",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/home/<USER>",
			"style": {
				"navigationBarTitleText": "确认订单",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/home/<USER>",
			"style": {
				"navigationBarTitleText": "支付",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/home/<USER>",
			"style": {
				"navigationBarTitleText": "支付",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/my/myOrder/myOrder",
			"style": {
				"navigationBarTitleText": "我的订单",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/my/myOrder/orderDetails",
			"style": {
				"navigationBarTitleText": "订单详情",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/my/register/register",
			"style": {
				"navigationBarTitleText": "注册",
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}

		},
		{
			"path": "pages/my/forget/forget",
			"style": {
				"navigationBarTitleText": "找回密码",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/my/set/bindMobile",
			"style": {
				"navigationBarTitleText": "绑定手机号",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/my/myfriend/myfriend",
			"style": {
				"navigationBarTitleText": "我的好友",
				"enablePullDownRefresh": false
			}

		}

		, {
			"path": "pages/my/set/setUserInfo",
			"style": {
				"navigationBarTitleText": "个人信息",
				"enablePullDownRefresh": false
			}

		}

		, {
			"path": "pages/home/<USER>",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/home/<USER>",
			"style": {
				"navigationBarTitleText": "详情",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/home/<USER>",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/my/webHtml/webHtml",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/my/myOrder/otherOrder",
			"style": {
				"navigationBarTitleText": "订单",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/home/<USER>",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/home/<USER>/materialList",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}
		// #ifndef MP-WEIXIN
		, {
			"path": "pages/home/<USER>/materialVideo",
			"style": {
				"navigationBarTitleText": "视频素材",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/home/<USER>/unionList",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/home/<USER>/unionDetails",
			"style": {
				"navigationBarTitleText": "公司详情",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/my/store/store",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/my/store/fbList",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}
		// #endif
		, {
			"path": "pages/home/<USER>/union",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/my/myOrder/logistics",
			"style": {
				"navigationBarTitleText": "物流",
				"enablePullDownRefresh": false
			}

		},
		{
			"path": "pages/my/hx/hx",
			"style": {
				"navigationBarTitleText": "核销",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/my/userVerif/userVerif",
			"style": {
				"navigationBarTitleText": "核销",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/home/<USER>/sing",
			"style": {
				"navigationBarTitleText": "签名"
			}
		},
		{
			"path": "pages/home/<USER>/contract",
			"style": {
				"navigationBarTitleText": "合同"
			}
		},
		{
			"path": "pages/home/<USER>",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		{
			"path": "pages/home/<USER>/contract1",
			"style": {
				"navigationBarTitleText": "合同"
			}
		},
		{
			"path": "pages/classify/classify_store",
			"style": {
				"navigationBarTitleText": ""
			}
		}
	],
	"subPackages": [{
		"root": "pagesB",
		"pages": [
			{
				"path": "barter/apply",
				"style": {
					"navigationBarTitleText": "申请店铺",
					"navigationBarBackgroundColor": "#310FFF",
					"navigationBarTextStyle": "white"
				}
			},
			{
				"path": "barter/shop_list",
				"style": {
					"navigationBarTitleText": "商品管理",
					"navigationBarBackgroundColor": "#310FFF",
					"navigationBarTextStyle": "white"
				}
			},
			{
				"path": "barter/push_shop/push_shop",
				"style": {
					"navigationBarTitleText": "发布商品",
					"navigationBarBackgroundColor": "#310FFF",
					"navigationBarTextStyle": "white"
				}
			},
			{
				"path": "barter/order/order",
				"style": {
					"navigationBarTitleText": "商家订单"
				}
			},
			{
				"path": "barter/order/orderDetails",
				"style": {
					"navigationBarTitleText": "订单详情"
				}
			},
			{
				"path": "videCcircle/videCcircle",
				"style": {
					"navigationBarTitleText": "",
					"navigationStyle": "custom"
				}
			},
			{
				"path": "videCcircle/videoFb",
				"style": {
					"navigationBarTitleText": "发布圈子",
					"navigationStyle": "custom"
				}
			},
			{
				"path": "videCcircle/video_user",
				"style": {
					"navigationBarTitleText": ""
				}
			},
			{
				"path": "videCcircle/list",
				"style": {
					"navigationBarTitleText": "口碑圈"
				}
			},{
				"path": "shop_p/shop_p",
				"style": {
					"navigationBarTitleText": "拼团",
					"navigationBarBackgroundColor": "#FFE7BB"
				}
			}
		]
	}],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": " 太和永康",
		// #ifdef H5
		// "navigationStyle": "custom",
		// #endif
		"navigationBarBackgroundColor": "#FFFFFF",
		"backgroundColor": "#FFFFFF",
		"backgroundColorTop": "#FFFFFF"
	}
	// ,
	// "tabBar": {
	// 	"custom": true,
	// 	"selectedColor": "#f23030",
	// 	"backgroundColor": "#ffffff",
	// 	"list": [{
	// 			"iconPath": "/static/tab_01.png",
	// 			"selectedIconPath": "/static/tab_02.png",
	// 			"pagePath": "pages/home/<USER>",
	// 			"text": ""
	// 		},
	// 		{
	// 			"iconPath": "/static/tab_03.png",
	// 			"selectedIconPath": "/static/tab_04.png",
	// 			"pagePath": "pages/classify/classify",
	// 			"text": ""
	// 		},
	// 		{
	// 			"iconPath": "/static/tab_05.png",
	// 			"selectedIconPath": "/static/tab_06.png",
	// 			"pagePath": "pages/discover/discover",
	// 			"text": ""
	// 		},
	// 		{
	// 			"iconPath": "/static/tab_07.png",
	// 			"selectedIconPath": "/static/tab_08.png",
	// 			"pagePath": "pages/cart/cart",
	// 			"text": ""
	// 		}
	// 		// ,
	// 		// {
	// 		// 	"iconPath": "/static/tab_09.png",
	// 		// 	"selectedIconPath": "/static/tab_10.png",
	// 		// 	"pagePath": "./pages/my/my",
	// 		// 	"text": ""
	// 		// }
	// 	]
	// }
}