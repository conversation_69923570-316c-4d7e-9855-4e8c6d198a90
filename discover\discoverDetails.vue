<template>
	<view class="page">
		<!-- 文章数据 -->
		<mescroll-body ref="mescrollRef" @init="mescrollInit"  @down="downCallback" @up="upCallback" :down="downOption" :up="upOption"
			:top="0">
			<view class="banner">
				<swiper class="screen-swiper square-dot" indicator-dots="true" circular="true" autoplay="true"
					interval="5000" duration="500">
					<swiper-item v-for="(item,index) in swiperList" :key="index">
						<image :src="$fun.imgUrl(item)" mode="aspectFill"></image>
						<!-- <video src="{{item.url}}" autoplay loop muted show-play-btn="{{false}}" controls="{{false}}" objectFit="cover" wx:if="{{item.type=='video'}}"></video> -->
					</swiper-item>
				</swiper>
			</view>
			<view class="store_info">
				<view class="info_item" style="margin: 0;">
					<view class="t">{{info.name}} </view>
				</view>
				<view class="info_item">
					<view class="t" style="color: gray;font-size: 28rpx;font-weight: normal;">{{info.title}} </view>
				</view>
				<view class="info_item">
					<view class="t">{{info.address}} </view>
				</view>
				<view class="info_item" >
					微信号:{{info.wechat}}
				</view>
				<view class="info_item" @click="callPhone(info.mobile)">
					{{info.mobile}}({{info.username}})
				</view>
				<view class="info_item" style="flex-direction: column;justify-content: flex-start;text-align: left;">
					<view class="i" style="width: 100%;">商家介绍：</view>
					<view style="width: 100%;" v-html="info.content"></view>
					<view style="margin-top: 20rpx;">
						<image v-for="(itme,index) in images_arr" :key="index" :src="$fun.imgUrl(itme)" style="width: calc(100vw - 22px)" mode="widthFix"></image>
					</view>
				</view>
			</view>
			<view style="display: flex;justify-content: space-between;width: 95%;margin: 0 auto;">
				<view class="submit-btn" style="width: 45%;"  @click="goNearby()">
					<text >去导航</text>
				</view>
				<view class="submit-btn" style="width: 45%;"  @click="gobuy()">
					<text >去支付</text>
				</view>
			</view>
		</mescroll-body>
		<passkeyborad ref="passkeyborad" @configPay="configPay" :show="show" @close="onSubmit" :payTitle="payTitle"
			:payMoney="orderInfo.money+'' || 0+''"></passkeyborad>
	</view>
</template>

<script>
	// #ifdef H5
	var jweixin = require('@/util/jweixin.js');
	// #endif
	// 引入mescroll-mixins.js
	import MescrollMixin from "@/components/mescroll-uni/mescroll-mixins.js";
	export default {
		mixins: [MescrollMixin], // 使用mixin
		data() {
			return {
				mescroll: null, // mescroll实例对象 (此行可删,mixins已默认)
				// 下拉刷新的配置(可选, 绝大部分情况无需配置)
				downOption: {},
				// 上拉加载的配置(可选, 绝大部分情况无需配置)
				upOption: {},
				swiperList: [],
				categoryList:[],
				goodsList:[],
				id:'',
				info:{},
				buyType:1,
				orderInfo:{},
				images_arr:[],
				payTitle:"",
				show:false
			};
		},
		onLoad(option) {
			this.id  = option.id
			 // #ifdef H5
			this.getConfig()
			// #endif
		},
		methods: {
			/**
			 * 初始化
			 */
			async init(){
				await this.getSwiper()
				await this.getCategory()
			},
			getConfig(){
				this.$fun.ajax.post('config/wxconfig', {url: location.href.split('#')[0]}).then(res => {
					if (res.status == 1) {
						let prams = res.data
							jweixin.config({
								debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
								appId: prams.appId, // 必填，公众号的唯一标识
								timestamp: prams.timestamp, // 必填，生成签名的时间戳
								nonceStr: prams.nonceStr, // 必填，生成签名的随机串
								signature: prams.signature, // 必填，签名
								jsApiList: ["getLocation", "openLocation"] //根据需要看需要哪些SDK的功能
							})
					}
				})
			},
			/**
			 * 获取轮播图
			 */
			getSwiper(){
				this.$fun.ajax.post('news/lists', {type:'nearby'}).then(res => {
					console.log(res)
					if(res.status==1){
						this.swiperList = res.data
					}
				})
			},
			/**
			 * 去导航
			 */
			goNearby() {
				console.log(11111)
				uni.openLocation({
					latitude: this.info.lat * 1, //维度
					longitude: this.info.lng * 1, //经度
					name: this.info.name, //目的地定位名称
					scale: 15, //缩放比例
					address: this.info.area + this.info.address //导航详细地址
				})
			},
			/**
			 * 获取分类
			 */
			getCategory(){
				this.$fun.ajax.post('category/list', {type:'nearby'}).then(res => {
					console.log(res)
					if(res.status==1){
						this.categoryList = res.data
					}
				})
			},
			/*下拉刷新的回调, 有三种处理方式:*/
			downCallback() {
				this.mescroll.endSuccess();
			},
			/*上拉加载的回调*/
			async upCallback(e) {
				this.mescroll.removeEmpty();
				const data = {
					nid:this.id
				};
				this.$fun.ajax.post('nearby/content', data).then(res => {
					if (res.status == 1) {
						const curList = res.data;
						this.swiperList = res.data.mt;
						this.images_arr = res.data.images_arr;
						this.info = curList; //追加新数据
						this.mescroll.endSuccess(1); //结束加载状态
					}
				})
			},
			gobuy(){
				this.$fun.msg('开发中...')
				return
				if(this.buyType == 1){
					// this.
				}
			},
			/**
			 * 文章点击
			 */
			onArticle() {
				uni.navigateTo({
					url: '/pages/ArticleDetails/ArticleDetails',
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	@import 'discoverDetails.scss';
</style>
