<template>
	<view>
		<!-- <u-swipe-action :show="show" @click="clickAction" :options="options"> -->
		<view class="chat_item" @click="clickItem(list)" v-if="type==1">
			<view class="chat_l">
				<view class="l_avatar">
					<u-badge v-if="list.icon" :offset="[0,5]" type="error" :count="list.icon"></u-badge>
					<u-avatar size="80" :src="$fun.imgUrl(list.avatar)" mode="circle"></u-avatar>
				</view>
				<view class="chat_info">
					<view class="chat_title">
						{{list.username}}
					</view>
					<view class="chat_msg">
						{{list.content}}
					</view>
				</view>
			</view>
			<view class="chat_r" :style="{borderBottom:b?'1px solid #eeeeee':'none' }">
				<view class="time">
					{{list.time}}
				</view>
				<view class="tip">
					<u-icon v-if="list.isOff" name="volume-off" size="35"></u-icon>
					<u-icon v-if="list.isTop" name="pushpin" size="35"></u-icon>
				</view>
			</view>
		</view>
		<!-- </u-swipe-action> -->

		<view class="chat_item" @click="clickItem(list)" v-if="type==2">
			<view class="chat_l">
				<view class="l_avatar">
					<u-avatar :src="$fun.imgUrl(list.avatar)" mode="circle"></u-avatar>
				</view>
				<view class="chat_info" :style="{borderBottom:b?'1px solid #eeeeee':'none' }">
					<view class="chat_title">
						{{list.username}}
					</view>

				</view>
			</view>
			<view class="chat_r" :style="{borderBottom:b?'1px solid #eeeeee':'none' }">

			</view>
		</view>
		<view class="chat_item" v-if="type==3">
			<view class="chat_l">
				<view class="l_avatar">
					<u-avatar src="/static/11.png" mode="circle"></u-avatar>
				</view>
				<view class="chat_info" :style="{borderBottom:b?'1px solid #eeeeee':'none' }">
					<view class="chat_title">
						{{list.username}}
					</view>
					<view class="chat_msg">
						{{list.memo}}
					</view>

				</view>
			</view>
			<view class="chat_r" :style="{borderBottom:b?'1px solid #eeeeee':'none' }">
				<block v-if="list.status==0">
					<view class="btn" v-if="list.addstatus==1"
						@click="$fun.jump(`/pagesB/chat/add_friend/add_confirm?type=2&account=${list.mobile}&id=${list.id}`)">
						查看
					</view>
					<view class="time" v-if="list.addstatus==0">
						待验证
					</view>
				</block>

				<view class="time" style="color: #666666;" v-if="list.status==1">
					已添加
				</view>
				<view class="time" style="color: #E4393c;" v-if="list.status==2">
					已拒绝
				</view>
			</view>

		</view>

		<view class="chat_item" v-if="type==4">
			<view class="chat_l">
				<view class="l_avatar">
					<u-avatar :src="$fun.imgUrl(list.avatar)" mode="circle"></u-avatar>
				</view>
				<view class="chat_info" :style="{borderBottom:b?'1px solid #eeeeee':'none' }">
					<view class="chat_title">
						{{list.username}}
					</view>
					<view class="chat_msg">
						账号:{{list.mobile}}
					</view>

				</view>
			</view>
		</view>
		<view class="chat_item" v-if="type==5">
			<view class="chat_l">
				<view class="l_avatar">
					<u-avatar :src="$fun.imgUrl(list.avatar)" mode="circle"></u-avatar>
				</view>
				<view class="chat_info" :style="{borderBottom:b?'1px solid #eeeeee':'none' }">
					<view class="chat_title">
						{{list.username}}
					</view>

				</view>
			</view>
			<view class="chat_r" :style="{borderBottom:b?'1px solid #eeeeee':'none' }">

			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			list: {
				type: Object,
				default: {}
			},
			b: {
				type: Boolean,
				default: true
			},
			type: {
				type: Number,
				default: 1
			}
		},
		data() {
			return {
				show: false,
				options: [{
						text: '置顶',
						style: {
							backgroundColor: '#007aff'
						}
					},
					{
						text: '勿扰',
						style: {
							backgroundColor: 'rgb(96, 98, 102)'
						}
					},
					{
						text: '删除',
						style: {
							backgroundColor: '#dd524d'
						}
					}
				],
				userInfo: {}
			};
		},
		methods: {
			clickAction(e, index) {
				console.log('clickAction', e, index);
				let prams = {
					id: this.list.id,
					isTop: 0,
					src: ''
				};
				if (index == 0) {
					prams
				}
			},
			clickItem(item, type) {
				if (this.type == 2) {
					if (item.type == 1) {
						this.$fun.jump(`/pagesB/chat/chat_msg?id=${item.id}`)
					} else {
						this.$fun.jump(`/pagesB/chat/add_friend/add_confirm?account=${item.mobile}&type=1`)
					}
				} else {
					this.$fun.jump(`/pagesB/chat/chat_msg?id=${item.id}`)
				}
				this.$emit('clear');
			}
		}
	}
</script>

<style lang="scss">
	.chat_item {
		width: 95vw;
		display: flex;

		.chat_l {
			width: calc(100% - 120rpx);
			display: flex;
			align-items: center;

			.l_avatar {
				position: relative;
			}

			.chat_info {
				padding: 30rpx 0;
				width: 100%;
				margin-left: 24rpx;
				display: flex;
				flex-direction: column;
				justify-content: center;
				border-bottom: 2rpx solid #eeeeee;

				.chat_title {
					font-family: Microsoft YaHei, Microsoft YaHei;
					font-weight: 400;
					font-size: 32rpx;
					color: #333333;
					line-height: 38rpx;
					font-style: normal;
					// font-weight: bold;
					text-transform: none;
				}

				.chat_msg {
					margin-top: 10rpx;
					width: calc(100% - 140rpx);
					font-family: Microsoft YaHei, Microsoft YaHei;
					font-weight: 400;
					font-size: 24rpx;
					color: #666666;
					line-height: 28rpx;
					// text-align: center;
					font-style: normal;
					text-transform: none;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}

			}
		}

		.chat_r {
			// padding: 30rpx 0;
			width: 100rpx;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;

			.time {
				font-family: Microsoft YaHei, Microsoft YaHei;
				font-weight: 400;
				font-size: 24rpx;
				color: #333333;
				line-height: 28rpx;
				text-align: center;
				font-style: normal;
				text-transform: none;
			}

			.btn {
				width: 104rpx;
				height: 64rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				background: #310FFF;
				border-radius: 32rpx 32rpx 32rpx 32rpx;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				font-size: 28rpx;
				color: #FFFFFF;
				line-height: 33rpx;
			}

			.tip {
				margin-top: 15rpx;
				position: relative;
				font-family: Microsoft YaHei, Microsoft YaHei;
				font-weight: 400;
				font-size: 24rpx;
				color: #333333;
				line-height: 28rpx;
				text-align: center;
				font-style: normal;
				text-transform: none;
			}

			.none_tip,
			.top,
			.tip {
				margin-top: 15rpx;
			}
		}
	}
</style>