<template>
	<view class="page">
		<image :src="imgSrc" mode="" class="bg"></image>
		<mescroll-body ref="mescrollRef" @init="mescrollInit" @down="downCallback" @up="upCallback" :down="downOption"
			:up="upOption" :top="520">
			<view class="goods-list">
				<view class="list" v-for="(item,index) in cartList" :key="index" @click="$fun.jump(`/pages/home/<USER>" >
					<view class="thumb">
						<image :src="$fun.imgUrl(item.image)"></image>
					</view>
					<view class="item">
						<view class="title">
							<text class="one-omit">{{item.name}}</text>
						</view>
						<view class="price-data">
							<view class="price">
								<text>￥{{item.money}}</text>
								<text class="lineation ">￥{{item.price}}</text>
							</view>
							<view class="data">
								<view class="btn">
									<text>立即抢购</text>
								</view>
								<!-- <view class="schedule">
									<view class="num">已售50%</view>
									<view class="bar">
										<text></text>
									</view>
								</view> -->
							</view>
						</view>
					</view>
				</view>
			</view>
		</mescroll-body>
	</view>
</template>

<script>
	// 引入mescroll-mixins.js
	import MescrollMixin from "@/components/mescroll-uni/mescroll-mixins.js";
	export default {
		mixins: [MescrollMixin], // 使用mixin
		data() {
			return {
				cartList: [],
				OrderType: 'all',
				id: '',
				mescroll: null, // mescroll实例对象 (此行可删,mixins已默认)
				// 下拉刷新的配置(可选, 绝大部分情况无需配置)
				downOption: {},
				// 上拉加载的配置(可选, 绝大部分情况无需配置)
				upOption: {},
				// 列表视图切换
				isList: true,
				// 筛选弹窗
				isScreen: false,
				// 筛选切换
				screenShow: 'weigh',
				// 抽屉
				isDrawer: false,
				keyword: '',
				goodsList: [],
				imgSrc: ''
			}
		},
		watch: {
			keyword(val) {
				this.cartList = []
				this.mescroll.resetUpScroll(false);
			}
		},
		onLoad(params) {
			this.keyword = decodeURIComponent(params.keyword || '');
			this.id = params.id;
			if(params.id==317){
				this.imgSrc = this.$fun.imgUrl(`/uploads/dp.png`)
			}else{
				this.imgSrc = this.$fun.imgUrl(`/uploads/hz.png`)
			}
		},
		methods: {
			onBack() {
				uni.navigateBack();
			},
			/*下拉刷新的回调, 有三种处理方式:*/
			downCallback() {
				this.cartList = []
				this.mescroll.resetUpScroll(false);
			},
			/*上拉加载的回调*/
			upCallback(e) {
				this.mescroll.removeEmpty();
				const data = {
					page: e.num,
					pagenum: e.size,
					cid: this.id,
					sort: this.screenShow,
					search: this.keyword
				};
				this.$fun.ajax.post('goods/list', data).then(res => {
					if (res.status == 1) {
						const curList = res.data;
						if (e.num === 1) {
							this.cartList = [];
							//第一页清空数据重载
							if (curList.length > 0) {
								uni.pageScrollTo({
									scrollTop: 0,
									duration: 200
								});
							}
						}
						this.cartList = this.cartList.concat(curList); //追加新数据
						this.mescroll.endSuccess(curList.length); //结束加载状态
						console.log(curList.length)
					}
				})
			},
			chnageTab(type, t = 1) {
				if (this.screenShow == type) {
					return
				}
				if (t == 1) {
					this.screenShow = type;
				} else {
					if (this.screenShow == 'desc') {
						this.screenShow = 'asc'
					} else {
						this.screenShow = 'desc'
					}
				}
				this.goodsList = []
				this.mescroll.resetUpScroll(false);
				// this.isScreen = !this.isScreen;
			},
		}
	}
</script>


<style scoped lang="scss">
	.page {
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		.bg {
			position: fixed;
			left: 0;
			top: 0;
			width: 100vw;
			height: 100vh;
			z-index: -1;
		}
	}
	
	.head-back {
		position: relative;
		position: fixed;
		left: 0;
		top: 0;
		z-index: 100;
		width: 100%;
		height: 100rpx;
		/* #ifdef APP-PLUS */
		height: calc(120rpx + var(--status-bar-height));
		padding-top: var(--status-bar-height);
		/* #endif */
		/* #ifdef MP */
		height: 150rpx;
		padding-top: var(--status-bar-height);
		/* #endif */
		background: linear-gradient(to right, $base, $change-clor);

		.back {
			position: absolute;
			left: 0;
			top: 0;
			/* #ifdef APP-PLUS */
			top: var(--status-bar-height);
			/* #endif */
			/* #ifdef MP */
			top: var(--status-bar-height);
			/* #endif */
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100rpx;
			height: 100rpx;

			text {
				display: flex;
				width: 20rpx;
				height: 20rpx;
				border-left: 2rpx solid #FFFFFF;
				border-bottom: 2rpx solid #FFFFFF;
				transform: rotate(45deg);
			}
		}

		.title {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100%;
			height: 100rpx;

			image {
				width: 200rpx;
				height: 50rpx;
			}
		}
	}

	.head-bg {
		width: 100%;
		height: calc(300rpx + var(--status-bar-height));
		background: linear-gradient(to right, $base, $change-clor);
		border-radius: 0 0 20% 20%;
		padding-top: 100rpx;
		/* #ifdef APP-PLUS */
		padding-top: calc(150rpx + var(--status-bar-height));
		/* #endif */
		/* #ifdef MP */
		padding-top: 150rpx;

		/* #endif */
		.session {
			display: flex;
			align-items: center;
			width: 100%;
			height: 100rpx;

			.list {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				width: 20%;
				height: 100%;

				.time {
					font-size: 32rpx;
					font-weight: bold;
					color: #ff9aa9;
				}

				.status {
					font-size: 24rpx;
					color: #ff9aa9;
				}
			}

			.action {
				.time {
					color: #FFFFFF;
				}

				.status {
					color: #FFFFFF
				}
			}
		}

		.activity {
			position: fixed;
			left: 0;
			top: 100rpx;
			/* #ifdef APP-PLUS */
			top: calc(100rpx + var(--status-bar-height));
			/* #endif */
			/* #ifdef MP */
			top: 150rpx;
			/* #endif */
			z-index: 100;
			background-color: #FFFFFF;

			.list {
				.time {
					font-size: 32rpx;
					font-weight: bold;
					color: #222222;
				}

				.status {
					font-size: 24rpx;
					color: #222222;
				}
			}

			.action {
				.time {
					color: $base;
				}

				.status {
					color: $base;
				}
			}
		}
	}

	.goods-list {
		padding: 0 4%;
		margin: 20rpx auto;

		.list {
			display: flex;
			align-items: center;
			padding: 0 4%;
			height: 200rpx;
			background-color: #FFFFFF;
			border-radius: 20rpx;
			margin-bottom: 20rpx;

			.thumb {
				display: flex;
				align-items: center;
				width: 30%;
				height: 100%;

				image {
					width: 160rpx;
					height: 160rpx;
					border-radius: 10rpx;
				}
			}

			.item {
				width: 70%;
				height: 100%;

				.title {
					display: flex;
					align-items: center;
					width: 100%;
					height: 60rpx;

					text {
						font-size: 26rpx;
						color: #222222;
					}
				}

				.price-data {
					display: flex;
					align-items: center;
					width: 100%;
					height: 140rpx;

					.price {
						display: flex;
						flex-direction: column;
						justify-content: center;
						width: 40%;
						height: 100%;

						text {
							color: $base;
							font-size: 32rpx;
							font-weight: bold;
						}

						.lineation {
							font-size: 24rpx;
							color: #959595;
							font-weight: normal;
							text-decoration: line-through;
						}
					}

					.data {
						display: flex;
						flex-direction: column;
						justify-content: center;
						align-items: flex-end;
						width: 60%;
						height: 100%;

						.btn {
							display: flex;
							align-items: center;
							justify-content: center;
							width: 160rpx;
							height: 60rpx;
							background: linear-gradient(to right, $base, $change-clor);
							border-radius: 70rpx;
							font-size: 26rpx;
							color: #FFFFFF;
						}

						.schedule {
							display: flex;
							align-items: center;
							width: 100%;
							height: 40rpx;

							.num {
								font-size: 24rpx;
								color: #959595;
							}

							.bar {
								width: 160rpx;
								height: 12rpx;
								border: 2rpx solid $rgba-05;
								border-radius: 10rpx;
								margin-left: 10rpx;

								text {
									display: flex;
									width: 50%;
									height: 100%;
									background-color: $rgba-05;
									animation: bar 1s linear;
								}

								@keyframes bar {
									0% {
										width: 0;
									}

									100% {
										width: 50%;
									}
								}
							}
						}
					}
				}
			}
		}
	}
</style>