<template>
	<view>
		<view class="poker-com">
			<block v-for="(item, index) in cardData" :key="index">
				<view :data-index="index" :data-item="item" @click="openCard" :class="'project item' + index + ' ' + (item.is_move ? 'ani' : '') + ' ' + (item.status==1 ? 'flip' : '')">
					<image class="front" src="/static/xtcj-com/poker/card.png"></image>
					<view class="back">
						<image :src="item.reward_img" @load="loadImageEnd"></image>
					</view>
				</view>
			</block>
		</view>
		<view class="reward_instruction">
			<text>{{rewardInstruction}}</text>
		</view>
	</view>
</template>

<script>
	function runAsync(time) {
		return new Promise(function(resolve, reject) {
			const timer = setTimeout(function() {
				resolve();
				clearTimeout(timer);
			}, time);
		});
	}
	export default {
		name: "PokerCard",
		data() {
			return {
				cardData: this.card,//数据
				clickDisable:true,//是否禁止点击
				stopRewardFlag:false,//是否停止抽奖
				extractNumInfo:this.extractNum,
				imageLoadNum:0,
			};
		},
		props: {
			// 九宫格卡片
			card: {
				type: Array,
				default: new Array()
			},
			rewardInstruction: {
				type: String,
				default: ""
			},
			extractNum:{
				type:Number,
				default:1
			}
		},
		watch: {
			card: {
				deep: true,
				handler(v) {
					this.cardData = v
				}
			},
			rewardInstruction(v) {
				this.rewardInstruction = v
			},
			extractNum(v) {
				this.extractNum = v
			}
		},
		methods: {
			start(cardData,callback) {
				runAsync(500).then(() => {
					// 延迟100毫秒翻转第一排牌面
					for (let i = 0; i < 3; i++) {
						cardData[i].status = 1;
					}
					this.cardData = cardData;
					return runAsync(200);
				}).then(() => {
					// 延迟200毫秒翻转第二排牌面
					for (let i = 3; i < 6; i++) {
						cardData[i].status = 1;
					}
					this.cardData = cardData;
					return runAsync(200);
				}).then(() => {
					// 延迟200毫秒翻转第三排牌面
					for (let i = 6; i <= 8; i++) {
						cardData[i].status = 1;
					}

					this.cardData = cardData;
					return runAsync(800);
				}).then(() => {
					// 将所有背面朝上
					for (let i = 0; i < 9; i++) {
						cardData[i].status = 0;
					}

					this.cardData = cardData;
					return runAsync(1000);
				}).then(() => {
					// 洗牌动画
					for (let i = 0; i < 9; i++) {
						runAsync(i * 40).then(() => {
							cardData[i].is_move = true;
							this.cardData = cardData;
							return runAsync(i * 40 + 1200);
						}).then(() => {
							cardData[i].is_move = false;
							this.cardData = cardData;
							return runAsync(1600);
						}).then(() => {
							this.clickDisable=false;
							// 结束后回调
							if (typeof callback === 'function') {
								callback();
							}
						});
					}
				});
			},
			// 点击打开单个卡片，开奖
			openCard(event) {
				if(this.extractNumInfo<=0){
					uni.showToast({
						title:"抽奖次数已用完，谢谢",
						duration:2000,
					});
					return false;
				}
				if(this.stopRewardFlag){
					return false;
				}
				if(this.clickDisable){
					return false;
				}
				this.extractNumInfo--;
				const {
					item,
					index
				} = event.currentTarget.dataset; // 触发父组件方法
				this.$emit('goPlayReward', {
					detail: {
						item,
						index
					}
				});
			},
			openReward(index,originalIndex,cardArrRes){
				if(this.stopRewardFlag){
					return false;
				}
				let that=this;
				that.cardData=cardArrRes;
				setTimeout(function(){
					that.cardData[originalIndex]['status']=1;
					let nowCardData=that.cardData[originalIndex];
					let cardDataArr=that.cardData;
					runAsync(200).then(() => {
						// 延迟200毫秒翻转第一排牌面
						for (let i = 0; i < 9; i++) {
							if(i!=originalIndex){
								cardDataArr[i].status = 1;
							}
						}
						that.cardData = cardDataArr;
						return runAsync(500);
					}).then(()=>{
						//获奖提示
						uni.showModal({
							title: '抽奖结果',
							content: nowCardData.is_no_prize ? nowCardData.reward_name : '恭喜，获得了' + (
								nowCardData.reward_name),
							showCancel: false, //去掉取消按钮
							success: function(res) {
								if (res.confirm) {
									//that.isRunning = false;
								}
							}
						});
						return runAsync(200);
					})
				},500);
			},
			stopReward(){
				this.stopRewardFlag=true;
			},
			loadImageEnd(data) {
			    this.imageLoadNum++;
			}
		}
	};
</script>
<style>
	.poker-com {
		position: relative;
		overflow: hidden;
		margin: 0 auto;
		width: 600rpx;
		height: 780rpx
	}

	.poker-com .project {
		position: absolute;
		float: left;
		width: 200rpx;
		height: 260rpx;
		display: -ms-flexbox;
		display: flex;
		-ms-flex-pack: center;
		justify-content: center;
		-ms-flex-align: center;
		align-items: center;
		transition: all 1s cubic-bezier(.68, -.22, .265, 1.22)
	}

	.poker-com .project image {
		width: 180rpx;
		height: 240rpx
	}

	.poker-com .project .back,
	.poker-com .project .front {
		-webkit-backface-visibility: hidden;
		backface-visibility: hidden;
		transform-style: preserve-3d;
		transition: all 1s cubic-bezier(.68, -.22, .265, 1.22);
		position: absolute;
		left: 10rpx;
		top: 10rpx;
		width: 180rpx;
		height: 240rpx
	}

	.poker-com .project .front {
		transform: rotateY(0)
	}

	.poker-com .project .back {
		transform: rotateY(180deg)
	}

	.poker-com .project.flip .back {
		transform: rotateY(0)
	}

	.poker-com .project.flip .front {
		transform: rotateY(180deg)
	}

	.poker-com .ani {
		top: 260rpx !important;
		left: 200rpx !important
	}

	.poker-com .item0 {
		top: 0;
		left: 0
	}

	.poker-com .item1 {
		top: 0;
		left: 200rpx
	}

	.poker-com .item2 {
		top: 0;
		left: 400rpx
	}

	.poker-com .item3 {
		top: 260rpx;
		left: 0
	}

	.poker-com .item4 {
		top: 260rpx;
		left: 200rpx
	}

	.poker-com .item5 {
		top: 260rpx;
		left: 400rpx
	}

	.poker-com .item6 {
		top: 520rpx;
		left: 0
	}

	.poker-com .item7 {
		top: 520rpx;
		left: 200rpx
	}

	.poker-com .item8 {
		top: 520rpx;
		left: 400rpx
	}
	
	.reward_instruction{
		margin:20rpx auto;
		width:80%;
	}
</style>
