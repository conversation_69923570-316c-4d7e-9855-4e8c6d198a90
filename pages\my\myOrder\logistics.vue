<template>
	<view style="font-size: 12px;">
		<mwg-logistics :info="info.data"></mwg-logistics>
	</view>
</template>

<script>
	import mwgLogistics from '@/components/mwg-logistics/mwg-logistics.vue'
	export default {
		components: {
			mwgLogistics
		},
		data() {
			return {
				info: {}
			}
		},
		onLoad(option) {
			this.$fun.ajax.post('common/getExpress', {
				express_code: option.express_code,
				express_idcode:option.express_idcode
			}).then(res => {
				if (res.status == 1) {
					this.info = res
				} else {
					this.$fun.msg(res.msg);
				}
			})
		},
		methods: {

		}
	}
</script>

<style>
page{
	background: #FFFFFF;
}
</style>
