<template>
	<view class="chat">
<!-- 		<view class="chat_search">
			<u-search placeholder="搜索" v-model="keyword" :show-action="false" input-align="center"></u-search>
		</view> -->
		<view class="chat_list">
			<chatItem v-for="(item,index) in list" :key="index" :list="item" :type="3"></chatItem>
		</view>
	</view>
</template>

<script>
	import chatItem from '../../components/chat-item/chat-item.vue';
	export default {
		components: {
			chatItem
		},
		data() {
			return {
				keyword: '',
				show: false,
				list: []
			};
		},
		onShow() {
			this.getList();
		},
		methods: {
			getList() {
				this.$fun.ajax.post(`chat/addList`, {}).then(res => {
					if (res.status == 1) {
						this.list = res.data
					}
				})
			},
		}
	}
</script>

<style lang="scss">
	page {
		background: #FFFFFF;

		.chat_search {
			margin: 32rpx;
		}

		.chat_list {
			padding: 18rpx 32rpx;
		}

		.chat_san {
			width: 50rpx;
			height: 50rpx;
			position: fixed;
			top: 15rpx;
			right: 30rpx;
			background: #FFFFFF;
			transform: rotate(45deg);
		}



		// 添加好友
		.chat_add_box {
			position: fixed;
			top: 20rpx;
			right: 20rpx;
			display: flex;
			flex-direction: column;
			padding: 46rpx 50rpx;
			background: #FFFFFF;
			border-radius: 12rpx 12rpx 12rpx 12rpx;

			.add_item {
				display: flex;
				margin-bottom: 40rpx;
				align-items: center;

				.add_title {
					margin-left: 10rpx;
					font-weight: 400;
					font-size: 28rpx;
					color: #333333;
					line-height: 33rpx;
					text-align: center;
					font-style: normal;
					text-transform: none;
				}
			}

			.add_item:nth-child(3) {
				margin-bottom: 0rpx;
			}
		}
	}
</style>