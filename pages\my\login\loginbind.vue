<template>
	<view class="container">
		<view class="left-bottom-sign"></view>
		<view class="back-btn yticon icon-zuojiantou-up" @click="navBack"></view>
		<view class="right-top-sign"></view>
		<!-- 设置白色背景防止软键盘把下部绝对定位元素顶上来盖住输入框等 -->

		<view class="wrapper">
			<view class="left-top-sign">PHONE</view>
			<view class="welcome">
				绑定手机号！
			</view>
			<view class="input-content">
				<view class="input-item">
					<text class="tit">手机号码</text>
					<input type="text" v-model="form.phone" placeholder="请输入手机号码/账号" />
				</view>

				<view class="input-item">
					<text class="tit">验证码</text>
					<view class="code_box">
						<input v-model="form.code" value="" placeholder="请输入验证码" placeholder-class="input-empty"
							maxlength="6" />
						<view class="sendCode btn-color" v-if="showTime">{{time}}s后重新获取</view>
						<view class="sendCode btn-color" @click="getCode()" hover-class="text-hover" v-else>获取验证码</view>
					</view>
				</view>
			</view>
		</view>

		<button class="confirm-btn" :style="isLogin?'opacity:1':'opacity:0.4'" @click="isLogin?onLogin():''">绑定</button>
	</view>

</template>

<script>
	export default {
		data() {
			return {
				isLogin: false,
				isLoginWay: false,
				isPassword: false,
				// 表单
				form: {
					phone: '',
					code: '',
					password: '',

				},
				userConfig: {
					login: []
				},
				confogInfo: {
					login: []
				},
				checked: false,
				tip: '发送验证码',
				showTime: false, //时间与获取验证码切换
				isClick: true, //时间与获取验证码切换
				timer: null,
				time: 60,
				token: null,

			};
		},
		// #ifdef MP-WEIXIN || H5 || APP-PLUS
		onLoad(option) {
			if (option.token) {
				this.token = option.token
			}
		},
		// #endif
		onShow() {
			// this.getLoginConfig()
		},
		methods: {
			// 获取验证码
			getCode() {
				if (this.isClick) {
					console.log(this.form.phone)
					if (!this.form.phone) {
						return this.$fun.msg('手机格式不正确')
					}
					if (this.form.phone.length != 11) {
						return this.$fun.msg('手机格式不正确')
					}
					// this.$loading()
					this.$fun.ajax.post('sms/send', {
						mobile: this.form.phone,
						event: 'changemobile'
					}).then(res => {
						if (res.status == 1) {
							this.showTime = true;
							this.$fun.msg(res.msg)
							this.timer = setInterval(() => {
								this.isClick = false;
								this.time = this.time - 1;
								if (this.time <= 0) {
									this.isClick = true;
									this.time = 60;
									this.showTime = false;
									clearInterval(this.timer);
									this.timer = null;
								}
							}, 1000)
						}

					})
				}
			},
			checkboxGroupChange() {
				console.log(this.checked)
			},
			getLogin() {
				if (!this.$fun.getUrlCode().code) {
					location.href = this.$fun.wxLogin()
				}
			},
			// 第三方注册 微信
			wxLogin() {
				if (!this.checked) {
					this.$fun.msg('请先阅读隐私政策和注册协议');
					return
				}
				uni.showLoading({
					title: '加载中...'
				})
				let _that = this
				// #ifdef H5
				location.href = this.$fun.wxLogin()
				// #endif
				// #ifdef MP-WEIXIN
				uni.getUserProfile({
					desc: 'weixin',
					success: function(infoRes) {
						console.log(infoRes)
						uni.login({
							provider: 'weixin',
							success: function(loginRes) {
								let prams = {
									platform: 'wechatmini',
									code: loginRes.code,
									...infoRes.userInfo,
									scene: uni.getStorageSync('saveScene') ? uni
										.getStorageSync('saveScene') : ''
								}
								_that.$fun.ajax.post('user/login', prams).then(res => {
									uni.hideLoading()
									if (res.status == 1) {
										_that.$fun.msg('登录成功')
										_that.$store.commit('loginStatus', true)
										uni.setStorageSync('token', res.data.token);
										uni.setStorageSync('userinfo', res.data.userinfo);
										if (!res.data.userinfo.isup) {
											_that.$fun.jump(
												'/pages/my/set/setUserInfo?type==2',
												3, 0)
										} else {
											_that.$fun.jump(
												'/pages/my/my', 3, 0)
										}

									}
								})
							},
							fail: function() {

							}
						})
					},
					fail: function() {
						_this.btnLoading = false;
					}
				})
				// #endif
			},
			/**
			 * 获取登录信息  
			 */
			getLoginConfig() {
				this.$fun.ajax.post('config/index', {}).then(res => {
					if (res.status == 1) {
						this.confogInfo = res.data
						if (res.data.login.indexOf('wechat_login') != -1) {
							// #ifdef H5
							this.getLogin()
							// #endif
						}
					}
				})
			},
			onRegister() {
				uni.navigateTo({
					url: '/pages/register/register'
				})
			},
			/**
			 * 登录切换
			 */
			onLoginCut() {
				this.isLoginWay = !this.isLoginWay;
				// 验证码
				if (this.isLoginWay) {
					this.isLogin = this.form.code && this.form.phone ? true : false;
				}
				// 账号密码
				if (!this.isLoginWay) {
					this.isLogin = this.form.password && this.form.phone ? true : false;
				}
			},
			/**
			 * 登录点击
			 */
			onLogin() {
				this.$fun.ajax.post('user/changemobile', {
					mobile: this.form.phone,
					captcha: this.form.code,
					token: this.token
				}).then(res => {
					if (res.status == 1) {
						this.$fun.msg(res.msg);
						uni.navigateBack();
						// uni.setStorageSync('token', uni.getStorageSync('token1'));
						// uni.setStorageSync('userinfo', uni.getStorageSync('userinfo1'));
						// // #ifdef H5
						// uni.setStorageSync('openid', uni.getStorageSync('openid1'));
						// // #endif
						// uni.removeStorageSync('token1');
						// uni.removeStorageSync('userinfo1');
						// uni.removeStorageSync('openid1');
						// location.href = this.$fun.imgUrl(`#/`);
						// this.$fun.jump('/pages/index/index', 3, 0)
					}
				})
			}
		},
		// 页面卸载
		onUnload() {
			this.isClick = true;
			this.time = 60;
			// 清空定时器
			clearInterval(this.timer);
			this.timer = null;
		},
		watch: {
			form: {
				handler(newValue, oldValue) {
					// 验证码

					this.isLogin = newValue.code && newValue.phone ? true : false;
					// 账号密码
					// if (!this.confogInfo.sms_status) {
					// 	this.isLogin = newValue.password && newValue.phone ? true : false;
					// }
				},
				deep: true
			}
		}

	}
</script>

<style scoped lang="scss">
	@import 'login.scss';

	.zc {
		display: flex;
		font-size: 28rpx;
		align-items: center;
		color: #000000;


		.t {
			display: inline-block;
			margin: 0 10rpx;
			color: #310FFF;
		}
	}

	page {
		background: #fff;
	}

	.login_wx {
		font-size: 13px;
		font-family: PingFang SC;
		font-weight: 400;
		color: #c8963d;
		margin-top: 40px;
		background: none;
		display: block;
		display: flex;
		justify-content: center;
		flex-direction: column;
		text-align: center;

		image {
			width: 40px;
			height: 40px;
			overflow: hidden;
			margin-bottom: 5px;
			margin: 0 auto;
		}
	}

	.container {
		padding-top: 115px;
		position: relative;
		width: 100vw;
		height: 100vh;
		overflow: hidden;
		background: #fff;
	}

	.wrapper {
		position: relative;
		z-index: 90;
		background: #fff;
		padding-bottom: 0;
	}

	.back-btn {
		position: absolute;
		left: 40upx;
		z-index: 9999;
		padding-top: var(--status-bar-height);
		top: 40upx;
		font-size: 40upx;
		color: $font-color-dark;
	}

	.left-top-sign {
		font-size: 120upx;
		color: $page-color-base;
		position: relative;
		left: -16upx;
	}

	.right-top-sign {
		position: absolute;
		top: 80upx;
		right: -30upx;
		z-index: 95;

		&:before,
		&:after {
			display: block;
			content: "";
			width: 400upx;
			height: 80upx;
			background: #310FFF;
		}

		&:before {
			transform: rotate(50deg);
			border-radius: 0 50px 0 0;
		}

		&:after {
			position: absolute;
			right: -198upx;
			top: 0;
			transform: rotate(-50deg);
			border-radius: 50px 0 0 0;
			/* background: pink; */
		}
	}

	.left-bottom-sign {
		position: absolute;
		left: -270upx;
		bottom: -320upx;
		border: 100upx solid #310FFF;
		border-radius: 50%;
		padding: 180upx;
	}

	.welcome {
		position: relative;
		left: 50upx;
		top: -90upx;
		font-size: 46upx;
		color: #555;
		text-shadow: 1px 0px 1px rgba(0, 0, 0, .3);
	}

	.input-content {
		padding: 0 60upx;
	}

	.input-item {
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		justify-content: center;
		padding: 0 30upx;
		background: $page-color-light;
		height: 120upx;
		border-radius: 4px;
		margin-bottom: 20upx;

		&:last-child {
			margin-bottom: 0;
		}

		.tit {
			height: 50upx;
			line-height: 56upx;
			font-size: $font-sm+2upx;
			color: $font-color-base;
		}

		.code_box {
			width: 95%;
			display: flex;
			align-items: center;

			.sendCode {
				width: 200rpx;
				margin-top: -48rpx;
				font-size: 24rpx;
				color: #310FFF;
			}
		}

		input {
			height: 60upx;
			font-size: $font-base + 2upx;
			color: $font-color-dark;
			width: 100%;
		}
	}

	.confirm-btn {
		width: 630upx;
		height: 76upx;
		line-height: 76upx;
		border-radius: 50px;
		margin-top: 70upx;
		background: #310FFF;
		color: #fff;
		font-size: $font-lg;

		&:after {
			border-radius: 100px;
		}
	}

	.forget-section {
		font-size: $font-sm+2upx;
		color: $font-color-spec;
		text-align: center;
		margin-top: 40upx;
	}

	.register-section {
		position: absolute;
		left: 0;
		bottom: 50upx;
		width: 100%;
		font-size: $font-sm+2upx;
		color: $font-color-base;
		text-align: center;

		text {
			color: $font-color-spec;
			margin-left: 10upx;
		}
	}
</style>