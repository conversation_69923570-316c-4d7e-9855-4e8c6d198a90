<template>
	<view>
		<view v-html="innerHtml" style="padding: 20rpx;">
			
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				name:'',
				innerHtml:''
			}
		},
		onLoad(option) {
			uni.setNavigationBarTitle({
				title:option.title
			})
			this.name = option.name
			this.$fun.ajax.post('config/getassist',{name:option.name}).then(res=>{
				this.innerHtml = res.data
			})
		},
		methods: {
			
		}
	}
</script>

<style>
page{
	background: #FFFFFF;
}
</style>
