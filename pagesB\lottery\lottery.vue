<template>
	<view class="bg_lottery" :style="{width:wdwidth,height:wdheight,minheight:wdheight,backgroundSize:All}">
		<image src="/static/temp/bi.jpg"  :style="{width:wdwidth,height:wdheight,zIndex:0,position:'absolute',top:0}" mode=""></image>
	<!-- 	<view class="lottery_top">
			<image class="top" src="../temp/zi-2.png" mode=""></image>
			<view class="qd" @click="jump('./qd')">
				签到记录
			</view>
		</view> -->
		<view class="mrqd">
			<image :src="$fun.imgUrl('/static/temp/zi-1.png')" mode=""></image>
		</view>
		<view class="qd_">
		<!-- 	<image src="../temp/qiandao.png" @click="jump('./qd')" mode=""></image>
			<image src="../temp/s.gif" @click="jump('./qd')" mode=""></image> -->
		</view>
		<view class="lottery_box">
			<view class="box" :animation="animationData">
				<view class="lottery_item" v-for="(item,index) in list"
					:style="{background:'url('+$fun.imgUrl(item.image)+') no-repeat',backgroundSize:' 102rpx 110rpx'}">
					<text>
						{{item.name}}
					</text>
				</view>
			</view>
			<view class="lottery_zz">

			</view>
			<view class="cj" @click="lottery_add()">
				<image :src="$fun.imgUrl('/static/temp/anniu.png')" mode=""></image>
			</view>
		</view>
		<view class="uni-swiper-msg" v-if="noticeList.length>0">
			<!-- <view class="uni-swiper-msg-icon"></view> -->
			<swiper vertical="true" autoplay="true" circular="true" interval="3000">
				<swiper-item v-for="(item, index) in noticeList" :key="index" s>
					<view class="font clamp">{{ item }}</view>
				</swiper-item>
			</swiper>
		</view>
	</view>
</template>

<script>
	import TurntableReward from '../components/xtcj-com/circle/turntable-reward.vue'
	export default {
		components: {
			TurntableReward
		},
		data() {
			return {
				wdwidth: '',
				wdheight: '812px',
				All: '',
				list: [],
				animationData: {}, //动画
				cjShow: true,
				zjInfo: {},
				oldId: null,
				num: 2,
				noticeList: []
			};
		},
		onLoad() {
			let _this = this
			uni.getSystemInfo({
				success: function(res) {
					console.log(res);
					_this.wdwidth = res.windowWidth + "px"
					// console.log(res.windowHeight);
					_this.wdheight = res.windowHeight > 812 ? res.windowHeight+'px' : 812 + "px"
					_this.All = res.windowWidth + "px" + '  ' + _this.wdheight+'px'
				}
			});
			this.luck_index()
			this.getList()
		},
		methods: {
			goPlayReward() {
				this.resultIndex = 6
			},
			getList() {
				this.$fun.ajax.post('luck/list', {}).then(res => {
					if (res.status == 1) {
						this.noticeList = res.data
					}
				})
			},
			luck_index() {
				this.$fun.ajax.post('luck/index', {}).then(res => {
					if (res.status == 1) {
						this.list = res.data
					}
				})
			},
			jump(url) {
				uni.navigateTo({
					url
				})
			},
			lottery_add() {
				if (!this.cjShow) {
					return;
				}
				this.cjShow = false
				this.$fun.ajax.post('luck/add', {}).then(res => {
					if (res.status == 1) {
						this.circle(res.data.id, res)
					}else{
						this.cjShow = true
					}
				})
			},
			circle(id, res) {
				let animationRun = uni.createAnimation({
					duration: 3000,
					timingFunction: 'ease'
				})
				let runDeg = 0;
				if (this.oldId >= id) {
					this.num += 1;
				}
				this.num += 4;
				runDeg = this.num * -360 - id * 60
				this.oldId = id
				animationRun.rotate(runDeg + 40).step()
				this.animationData = animationRun.export()
				setTimeout(() => {
					this.cjShow = true
					this.$fun.msg(res.msg)
				}, 3000)
			}
		}
	}
</script>

<style lang="scss" scoped>
	/* 滚动公告*/
	.uni-swiper-msg {
		width: 530rpx;
		height: 70rpx;
		background: rgba(0, 145, 106, 0.5);
		border-radius: 35rpx;
		padding: 16rpx;
		margin: 80rpx auto;
		flex-wrap: nowrap;
		display: flex;
		align-items: center;
		justify-content: space-between;
		position: relative;
		z-index: 99;
		padding: 0 40rpx;

		.uni-swiper-msg-icon {
			margin-right: 24rpx;
			width: 57rpx;
			font-size: 28rpx;
			font-weight: bold;
			font-style: italic;
			color: #F69D28;
		}

		swiper {
			width: 500rpx;
			height: 31rpx;
			flex: 1;
			font-size: 22rpx;
		}

		swiper-item {
			line-height: 31rpx;

			.font {
				font-size: 26rpx;
				font-weight: 500;
				color: #FFFFFF;

			}
		}

		// .more {
		// 	height: 32rpx;
		// 	line-height: 32rpx;
		// 	margin-left: 30rpx;
		// 	font-size: 24rpx;
		// 	color: #999999;

		// 	.icon-you {
		// 		font-size: 22rpx;
		// 		color: #999999;
		// 	}
		// }
	}

	.bg_lottery {
		// background: url('/pagesC/temp/bi.jpg') no-repeat;
		overflow: hidden;

		.lottery_top {
			text-align: center;
			margin-top: 45rpx;
			position: relative;

			.top {
				width: 480rpx;
				height: 25rpx;
				margin: 0 auto;
			}

			.qd {
				width: 120rpx;
				height: 40rpx;
				line-height: 40rpx;
				background: rgba(0, 145, 106, 0.5);
				border-radius: 20rpx;
				position: absolute;
				right: 15rpx;
				top: -8rpx;
				color: #FFFFFF;
				font-size: 20rpx !important;
			}
		}

		.mrqd {
			margin-top: 44rpx;
			display: flex;
			justify-content: center;

			image {
				width: 417rpx;
				height: 79rpx;
			}
		}

		.qd_ {
			display: flex;
			justify-content: center;
			position: relative;
			width: 186rpx;
			margin: 0 auto;
			margin-top: 30rpx;

			image:nth-child(1) {
				width: 186rpx;
				height: 43rpx;
			}

			image:nth-child(2) {
				position: absolute;
				width: 150rpx;
				height: 150rpx;
				top: -15rpx;
				left: 25rpx;
			}
		}

		.lottery_box {
			margin: 0 auto;
			margin-top: 230rpx;
			width: 718rpx;
			height: 723rpx;
			position: relative;

			.box {
				width: 718rpx;
				height: 723rpx;
				position: relative;
				background: url('../temp/pan.png') no-repeat;
				background-size: 718rpx 723rpx;

			}

			.lottery_zz {
				width: 83rpx;
				height: 112rpx;
				background: url('../temp/jiantou.png') no-repeat;
				background-size: 83rpx 112rpx;
				position: absolute;
				top: 0;
				left: calc(50% - 20rpx);
				top: -36rpx;
			}

			.lottery_item {
				width: 103rpx;
				height: 110rpx;
				background: url('../temp/hongbao.png') no-repeat;
				background-size: 103rpx 110rpx;
				position: relative;

				text {
					font-size: 16rpx;
					color: #000000;
					position: absolute;
					top: -60rpx;
				}
			}

			.lottery_item:nth-child(1) {
				position: absolute;
				top: 120rpx;
				left: 360rpx;
				transform: rotate(30deg);
			}

			.lottery_item:nth-child(2) {
				position: absolute;
				top: 250rpx;
				left: 500rpx;
				transform: rotate(70deg);
			}

			.lottery_item:nth-child(3) {
				position: absolute;
				top: 430rpx;
				left: 450rpx;
				transform: rotate(130deg);
			}

			.lottery_item:nth-child(4) {
				position: absolute;
				top: 500rpx;
				left: 250rpx;
				transform: rotate(200deg);
			}

			.lottery_item:nth-child(5) {
				position: absolute;
				top: 350rpx;
				left: 120rpx;
				transform: rotate(250deg);
			}

			.lottery_item:nth-child(6) {
				position: absolute;
				top: 160rpx;
				left: 180rpx;
				transform: rotate(-50deg);
			}

			.cj {
				border-radius: 50%;
				position: absolute;
				top: calc(50% - 108rpx);
				left: calc(50% - 107rpx);

				image {
					width: 214rpx;
					height: 216rpx;
				}
			}
		}
	}

	.model {
		width: 100%;
		height: 100%;
		position: fixed;
		top: 0;
		background: rgba(84, 19, 15, .8);
		z-index: 99999999;
	}

	.center_box {
		width: 644rpx;
		height: 645rpx;
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 340rpx;
		margin: auto;
	}

	.center_box>.close {
		width: 100%;
		text-align: center;
	}

	.center_box>.close>image {
		width: 65rpx;
		height: 65rpx;
	}

	.center {
		width: 671rpx;
		height: 717rpx;
		position: relative;
	}

	.center>image {
		width: 671rpx;
		height: 717rpx;
	}

	.center .info {
		position: absolute;
		top: 210rpx;
		width: 95%;
		text-align: center;
	}

	.center .info .text {
		color: #B28036;
		font-size: 30rpx;
	}

	.center .info>image {
		margin-top: 37rpx;
		width: 96rpx;
		height: 96rpx;
	}

	.button {
		margin-top: 115rpx;
		display: flex;
		justify-content: space-around;
		padding: 0 80rpx;
	}

	.button>view {
		position: relative;
	}

	.button .l {
		width: 100%;
		height: 100%;
		text-align: center;
	}

	.l text {
		color: #995C19;
		font-size: 30rpx;
		position: absolute;
		top: 40rpx;
		left: 0;
		right: 0;
		bottom: 0;
		margin: auto;
	}

	.button .l image {
		width: 425rpx;
		height: 161rpx;
	}

	.c image {
		width: 255rpx;
		height: 161rpx;
	}

	.c text {
		color: #995C19;
		font-size: 30rpx;
		position: absolute;
		top: 40rpx;
		left: 0;
		right: 0;
		bottom: 0;
		margin: auto;
	}

	.button .c {
		width: 100%;
		height: 100%;
		text-align: center;
	}
</style>
