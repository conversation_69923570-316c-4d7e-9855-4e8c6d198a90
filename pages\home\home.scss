.page{
	// position: absolute;
	// left: 0;
	// top: 0;
	// width: 100%;
	// height: 100%;
	// overflow-x: hidden;
	// overflow-y: auto;
	padding-bottom: 100rpx;
	// background-color: #FFFFFF;
}
.head-info{
	position: fixed;
	left: 0;
	top: 0;
	z-index: 10;
	width: 100%;
	/* #ifdef APP-PLUS||H5 */
	padding: calc(20rpx + var(--status-bar-height)) 25rpx 0;
	height: calc(200rpx + var(--status-bar-height));
	/* #endif */
	/* #ifdef MP */
	padding:0 25rpx;
	height: 170rpx; 
	/* #endif */ 
	background: url(/static/icon/i.png) no-repeat;
	background-size: 100%  calc(200rpx + var(--status-bar-height));
	background-color: #FFFFFF;
	overflow: hidden;
	.head-search{
		display: flex;
		align-items: center;
		justify-content: space-between;
		.icon-info{
			display: flex;
			align-items: center;
			height: 100%;
			text{
				font-size: 52rpx;
				color: #f6f6f6;
			}
			image{
				width: 42rpx;
				height: 43rpx;
			}
		}
		.search{
			display: flex;
			align-items: center;
			width: 100%;
			padding: 0 20rpx;
			height: 65rpx;
			background-color: rgba(255,255,255,0.3);
			border-radius: 10rpx;
			.icon{
				display: flex;
				align-items: center;
				margin-right: 20rpx;
				image{
					width: 27rpx;
					height: 29rpx;
				}
			}
			.hint{
				display: flex;
				align-items: center;
				.max{
					font-size: 30rpx;
					font-weight: bold;
					color: #FFFFFF;
				}
				.min{
					font-size: 24rpx;
					color: #F6f6f6;
					margin-left: 10rpx;
				}
			}
		}
	}
	.classify-list{
		white-space:nowrap;
		width: 100%;
		height: 100rpx;
		overflow-x: auto;
		overflow-y: hidden;
		.list{
			position: relative;
			display:inline-block;
			width: 20%;
			height: 100%;
			line-height: 100rpx;
			text-align: center;
			text{
				font-size: 28rpx;
				color: #FFFFFF;
				opacity: 0.8;
			}
			.line{
				position: absolute;
				left: 50%;
				bottom: 20rpx;
				width: 60%;
				height: 8rpx;
				background: linear-gradient(to right,#f8f893,#f8f893);
				border-radius: 10rpx;
				transform: translate(-50%,0);
			}
		}
		.action{
			text{
				font-size: 32rpx;
				opacity: 1;
			}
		}
	}
}
.main{
	/* #ifdef APP-PLUS||H5 */
	margin-top: calc(200rpx + var(--status-bar-height));
	/* #endif */
	/* #ifdef MP */
	margin-top: 170rpx;
	/* #endif */
	padding-top: 20rpx;
	// background-color: #FFFFFF;
}
/* banner */
	.banner{
		padding: 0 24rpx;
		height: 200rpx;
		border-radius: 10rpx;
		overflow: hidden;
		.screen-swiper{
			height: 200rpx;
			min-height: 100% !important;
			image{
				height: 200rpx;
				border-radius: 10rpx;
			}
		}
	}
/* banner */
	.banner1{
		padding: 0 25rpx;
		height: 980rpx;
		// margin-bottom: 30rpx;
		// margin: -200rpx auto 20rpx;
		border-radius: 10rpx;
		overflow: hidden;
		.screen-swiper{
			height: 980rpx;
			min-height: 100% !important;
			image{
				width: 100%;
				height: 980rpx;
				border-radius: 10rpx;
			}
		}
	}
/* 菜单导航 */
.menu-nav{
	position: relative;
	width: 100%;
	margin:30rpx auto;
	.nav-list{
		white-space: nowrap; 
		width: 100%;
		.nav{
			display: inline-block;
			display: flex;
			flex-direction: inherit !important;
			flex-wrap: wrap;
			justify-content: flex-start;
			width: 100vw;
		}
		.list{
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			// width: calc(100vw / 5);
			height: 130rpx;
			margin-bottom: 20rpx;
			image{
				width: 75rpx;
				height: 75rpx;
				// border-radius: 100%;
			}
			text{
				font-size: 26rpx;
				color: #363636;
				margin-top: 10rpx;
			}
		}
	}
	.indicator{
		position: absolute;
		left: 0;
		bottom: 0;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		height: 30rpx;
		.plan{
			position: relative;
			width: 100rpx;
			height: 8rpx;
			border-radius: 8rpx;
			background-color: #e1e1e1;
			.bar{
				position: absolute;
				width: 50%;
				height: 100%;
				border-radius: 6rpx;
				background-color: $base;
			}
		}
	}
}
/* 通知 */
.inform{
	padding: 0 25rpx;
	height: 130rpx;
	// margin: 30rpx auto;
	border-bottom: 16rpx solid #f9f9f9;
	.inform-info{
		display: flex;
		padding: 0 20rpx;
		height: 70rpx;
		background-color: #f7f7f7;
		border-radius: 10rpx;
		.picture{
			width: 20%;
			height: 100%;
			image{
				width: 93rpx;
				height: 84rpx;
				margin-top: -20rpx;
			}
		}
		.info{
			width: 80%;
			height: 100%;
			.swiper{
				width: 100%;
				height: 100%;
				.swiper-item{
					display: flex;
					align-items: center;
					width: 100%;
					height: 100%;
					text{
						font-size: 28rpx;
						color: #848281;
					}
				}
			}
		}
	}
}	
/* 为你推荐 */
.recommend-info{
	width: 100%;
	background-color: #f2f2f2;
	.recommend-title{
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 100rpx;
		.title{
			display: flex;
			align-items: center;
			image{
				width: 416rpx;
				height: 40rpx;
			}
		}
	}
	.goods-list{
		// display: flex;
		// flex-wrap: wrap;
		// justify-content: space-between;
		// padding: 0 30rpx;
		.list{
			width: 49%;
			height: 540rpx;
			margin-bottom: 20rpx;
			background-color: #FFFFFF;
			border-radius: 10rpx;
			overflow: hidden;
			.pictrue{
				display: flex;
				justify-content: center;
				width: 100%;
				image{
					height: 350rpx;
				}
			}
			.title-tag{
				// display: flex;
				height: 100rpx;
				padding: 20rpx;
				.tag{
					float: left;
					margin-right: 10rpx;
					overflow: hidden;
					text-overflow: ellipsis;
					display: -webkit-box;
					-webkit-line-clamp: 2;
					-webkit-box-orient: vertical;
					white-space: normal;
					font-size: 26rpx;
					line-height: 40rpx;
					text{
						font-size: 24rpx;
						color: #FFFFFF;
						padding: 4rpx 16rpx;
						background: linear-gradient(to right,$base,$change-clor);
						border-radius: 6rpx;
						margin-right: 10rpx;
					}
				}
			}
			.price-info{
				display: flex;
				flex-wrap: wrap;
				align-items: center;
				justify-content: space-between;
				padding: 0 20rpx;
				height: 80rpx;
				.user-price{
					display: flex;
					align-items: center;
					margin-right: 10rpx;
					text{
						color: $price-clor;
					}
					.min{
						font-size: 24rpx;
					}
					.max{
						font-size: 32rpx;
					}				}
				.vip-price{
					display: flex;
					align-items: center;
					color: #888888;
					text-decoration:line-through;
					image{
						width: 26rpx;
						height: 26rpx;
						margin-right: 10rpx;
					}
					text{
						color: #888888;
						font-size: 24rpx;
					}
				}
			}
		}
	}
}