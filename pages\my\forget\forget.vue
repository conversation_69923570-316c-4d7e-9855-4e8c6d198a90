/**
* @Description:  太和永康 



* 
*/
<template>
	<view class="page">
		<form @submit="login" class="form">
			<view class="formContent">
				<view class="formItem">
					<input type="text" class="input" name="phone" placeholder="请输入手机号" v-model="phone">
				</view>
				<view class="formItem">
					<input type="text" class="input" name="code" placeholder="请输入验证码" v-model="code" maxlength="6">
					<view class="sendCode btn-color" v-if="showTime">{{time}}s后重新获取</view>
					<view class="sendCode btn-color" @click="getCode()" hover-class="text-hover" v-else>获取验证码</view>
				</view>
				<view class="formItem">
					<input type="password" class="input" name="pwd" placeholder="请输入新密码" v-model="pwd">
				</view>
				<view class="formButton">
					<!-- <button style="background:linear-gradient(to right, #310FFF, 26D0AB);color: #FFFFFF;" formType="submit" class="button" hover-class="text-hover">重置密码</button> -->
					<button class="confirm-btn" style="background:#310FFF;color: #FFFFFF;"
						 formType="submit" hover-class="text-hover">重置密码</button>
				</view>
			</view>
		</form>
	</view>

</template>
<script>
	// import graceChecker from "../../api/graceChecker.js"
	export default {
		data() {
			return {
				timer: null, //定时器
				isClick: true, //避免用户重复点击
				time: 60, //60s后重置
				showTime: false, //时间与获取验证码切换
				phone: '',
				code: '',
				pwd: '',
			}
		},
		onLoad() {

		},
		methods: {
			// 修改
			login(e) {
				let prams = {
					mobile:this.phone,
					code:this.code,
					pwd:this.pwd,
				}
				if (!prams.mobile) {
					this.$fun.msg('请输入手机号')
					return false;
				}
				if (!prams.code) {
					this.$fun.msg('请输入验证码')
					return false;
				}
				if (!prams.pwd) {
					this.$fun.msg('请输入重置密码')
					return false;
				}
				this.$fun.ajax.post('user/resetpwd',{...prams}).then(res=>{
					if (res.status == 1) {
						this.$fun.msg(res.msg);
						setTimeout(() => {
							uni.navigateBack({
								
							})
						}, 1500);
					}
				
				})
			},
			// 获取验证码
			getCode() {
				if (this.isClick) {
					if (this.phone.length != 11) {
						return this.$fun.msg('手机格式不正确')
					}
					// this.$loading()
					this.$fun.ajax.post('sms/send',{mobile:this.phone,event:'resetpwd'}).then(res=>{
						if (res.status == 1) {
							this.showTime = true;
							this.$fun.msg(res.msg)
							this.timer = setInterval(() => {
								this.isClick = false;
								this.time = this.time - 1;
								if (this.time <= 0) {
									this.isClick = true;
									this.time = 60;
									this.showTime = false;
									clearInterval(this.timer);
									this.timer = null;
								}
							}, 1000)
						}
					
					})
				}
			},
		},
		// 页面卸载
		onUnload() {
			this.isClick = true;
			this.time = 60;
			// 清空定时器
			clearInterval(this.timer);
			this.timer = null;
		}
	}
</script>
<style lang="scss" scoped>
	page{
		background-color: #ffffff;
	}
	.page {
		padding-bottom: 20upx;
		background-color: #ffffff;
		height: 100%;

		.form {
			width: 100%;

			.formContent {
				padding-top: 100upx;
				padding-left: 40upx;
				padding-right: 24upx;

				.formItem {
					height: 104upx;
					display: flex;
					border-bottom: 1upx solid rgba(238, 238, 238, 1);
					align-items: center;

					input {
						flex: 1;
						height: 100%;
						font-size: 30upx;
						font-weight: 400;

						&::-webkit-input-placeholder {
							color: rgba(204, 204, 204, 1);
						}
					}

					.sendCode {
						background: transparent;
						color: #310FFF;
					}
				}

				.formButton {
					margin-top: 60upx;
					height: 100upx;
					position: relative;

					button {
						height: 80rpx;
						line-height: 80rpx;
						border-radius: 80rpx;
					}
				}
			}
		}
	}
</style>
