{"id": "uni-id", "displayName": "uni-id", "version": "3.3.26", "description": "简单、统一、可扩展的用户中心", "keywords": ["uniid", "uni-id", "用户管理", "用户中心", "短信验证码"], "repository": "https://gitee.com/dcloud/uni-id.git", "engines": {"HBuilderX": "^3.1.0"}, "dcloudext": {"category": ["uniCloud", "云函数模板"], "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": ""}, "uni_modules": {"dependencies": ["uni-config-center"], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y"}, "client": {"App": {"app-vue": "u", "app-nvue": "u"}, "H5-mobile": {"Safari": "u", "Android Browser": "u", "微信浏览器(Android)": "u", "QQ浏览器(Android)": "u"}, "H5-pc": {"Chrome": "u", "IE": "u", "Edge": "u", "Firefox": "u", "Safari": "u"}, "小程序": {"微信": "u", "阿里": "u", "百度": "u", "字节跳动": "u", "QQ": "u", "钉钉": "u", "快手": "u", "飞书": "u", "京东": "u"}, "快应用": {"华为": "u", "联盟": "u"}, "Vue": {"vue2": "y", "vue3": "u"}}}}}