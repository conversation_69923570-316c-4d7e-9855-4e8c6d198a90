<template>
	<view class="add_confirm">
		<view class="chat_list">
			<chatItem :list="list" :type="4" :b="false"></chatItem>
			<view class="add_confirm_info" v-if="type==2&&list.memo">
				{{list.username}}：{{list.memo}}
			</view>
			<u-cell-item @click="nextClick" style="padding-left: 10rpx;padding-right:10rpx" :border-bottom="false" title="备注"
				:value="list.isFriend?list.username:''">
			</u-cell-item>
		</view>
		<u-modal v-model="show" :show-cancel-button="true" @confirm="confirm" title="备注">
			<view class="slot-content" style="padding: 30rpx;">
				<u-form-item :border-bottom="false" label="备注"><u-input v-model="list.frommemo"
						placeholder="请输入备注" /></u-form-item>
			</view>
		</u-modal>
		<block v-if="type==1">
			<view class="confirm_btn" @click="$fun.jump(`./add_confirm1?type=1&account=${account}`,2)"
				v-if="list.isFriend!=1">
				添加好友
			</view>
			<view class="confirm_btn" @click="$fun.jump(`../chat_msg?id=${list.chatId}`)" v-if="list.isFriend==1">
				发送消息
			</view>
		</block>
		<block v-if="type==2">
			<view class="confirm_btn" @click="$fun.jump(`./add_confirm1?type=2&account=${account}&id=${id}`,2)"
				v-if="list.isFriend!=1">
				前往通过
			</view>
			<view class="confirm_btn" @click="$fun.jump(`../chat_msg?id=${list.chatId}`)" v-if="list.isFriend==1">
				发送消息
			</view>
		</block>
	</view>
</template>

<script>
	import chatItem from '../../components/chat-item/chat-item.vue';
	export default {
		components: {
			chatItem
		},
		data() {
			return {
				keyword: '',
				memo: '',
				memo1: '',
				show: false,
				list: {
					isFriend: null
				},
				type: 1,
				account: '',
				id: null
			};
		},
		onLoad(option) {
			this.type = option.type;
			this.id = option.id ? option.id : '';
			this.account = option.account;
			this.getuser()
		},
		methods: {
			nextClick(){
				if(this.list.isFriend){
					this.$fun.jump(`./add_confirm1?type=3&id=${this.list.chatId}&frommemo=${this.list.username}`,2)
				}else{
					this.$fun.jump(`./add_confirm1?type=1&account=${this.account}`,2)
				}
			},
			addFirend() {
				this.$fun.ajax.post('chat/addUser', {
					account: this.account
				}).then(res => {
					if (res.status == 1) {
						this.list = [res.data]
					}
				})
			},
			confirm() {
				this.memo = this.memo1
			},
			getuser() {
				this.$fun.ajax.post('chat/getUser', {
					account: this.account
				}).then(res => {
					if (res.status == 1) {
						this.list = res.data
					}
				})
			}
		},
		onNavigationBarButtonTap(e) {
			if (e.text == '+') {
				if (!this.show) {
					this.show = true;
					this.$refs.popup.open('top');
				} else {
					this.show = false;
					this.$refs.popup.close()
				}
			}
		},
	}
</script>

<style lang="scss">
	page {
		background: #F6F6F6;

		.chat_search {
			margin: 32rpx;
		}

		.chat_list {
			padding: 18rpx 32rpx;
			background: #FFFFFF;

			.add_confirm_info {
				margin-top: 38rpx;
				margin-bottom: 10rpx;
				padding: 28rpx;
				background: #F2F2F2;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				font-size: 28rpx;
				color: #666666;
				line-height: 40rpx;
				text-align: left;
				font-style: normal;
				text-transform: none;
				border-radius: 16rpx 16rpx 16rpx 16rpx;
			}
		}

		.confirm_btn {
			margin-top: 32rpx;
			background: #FFFFFF;
			border-radius: 0rpx 0rpx 0rpx 0rpx;
			padding: 24rpx 0;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 500;
			font-size: 32rpx;
			color: #310FFF;
			line-height: 38rpx;
			text-align: left;
			font-style: normal;
			text-transform: none;
			display: flex;
			justify-content: center;
		}

		.chat_san {
			width: 50rpx;
			height: 50rpx;
			position: fixed;
			top: 15rpx;
			right: 30rpx;
			background: #FFFFFF;
			transform: rotate(45deg);
		}



		// 添加好友
		.chat_add_box {
			position: fixed;
			top: 20rpx;
			right: 20rpx;
			display: flex;
			flex-direction: column;
			padding: 46rpx 50rpx;
			background: #FFFFFF;
			border-radius: 12rpx 12rpx 12rpx 12rpx;

			.add_item {
				display: flex;
				margin-bottom: 40rpx;
				align-items: center;

				.add_title {
					margin-left: 10rpx;
					font-weight: 400;
					font-size: 28rpx;
					color: #333333;
					line-height: 33rpx;
					text-align: center;
					font-style: normal;
					text-transform: none;
				}
			}

			.add_item:nth-child(3) {
				margin-bottom: 0rpx;
			}
		}

		.icon_l {
			width: 70rpx;
			height: 70rpx;
			border-radius: 11rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			background: #ff9900;
			margin-right: 20rpx;
		}

		.u-icon_r {
			color: #909399;
		}
	}
</style>