/**
 * @name ajax请求
 * @param { String } url 请求地址
 * @param { Object } data 请求参数
 * @param { Boolean } loading loading提示
 */
// 域名
import env from "../env.js";
let baseUrl;

if (process.env.NODE_ENV === 'development') {
	// 线上环境
	baseUrl = env.dev.apiURl;
} else {
	// 开发环境
	baseUrl = env.prod.apiURl;
}

class ajax {
	constructor() {
		this.get = (url, data) => {
			return this.request('GET', url, data);
		};

		this.post = (url, data) => {
			return this.request('POST', url, data);
		};

		this.put = (url, data) => {
			return this.request('PUT', url, data);
		};

		this.del = (url, data) => {
			return this.request('DELETE', url, data);
		};
	}

	async request(method, url, data) {
		return new Promise((resolve, reject) => {
			if (data && data.loading) {
				uni.showLoading({
					title: '加载中...',
					mask: true
				});
			} // url  判断


			if (url.slice(0, 4) != 'http') {
				url = baseUrl + '/api/' + url;
			} // token  判断  添加

			let token = uni.getStorageSync('token');
			let header = {
				'Content-type': 'application/x-www-form-urlencoded'
			};

			if (token) {
				header.token = uni.getStorageSync('token');
			} // #ifdef H5
			var ua = navigator.userAgent.toLowerCase();
			var isWeixin = ua.indexOf('micromessenger') !== -1;
			if (!isWeixin) {
				header.type = "H5_jsApi";
			} else {
				// header.type = "H5";
				header.type = "H5";
			}

			// #endif
			// #ifdef APP-PLUS
			plus.runtime.getProperty(plus.runtime.appid, (info) => {
				header.versionCode = info.versionCode
			})
			header.type = "App"; // #endif
			// #ifdef MP-WEIXIN

			header.type = "wechatmini"; // #endif

			uni.request({
				method,
				url,
				data,
				header,
				success: res => {
					if (res.data.status === 1) {
						resolve(res.data);

						if (data && data.loading) {
							uni.hideLoading();
						}
					} else if (res.data.status === 401) {
						resolve(res.data);
						uni.removeStorageSync('token');
						uni.removeStorageSync('userinfo');
						uni.reLaunch({
							url: '/pages/my/login/login'
						});

						if (data && data.loading) {
							uni.hideLoading();
						}
					}else if (res.data.status == "project_vip") {
						resolve(res.data);
						uni.navigateTo({
							url: '/pagesB/upgrade/upgrade'
						});
						if (data && data.loading) {
							uni.hideLoading();
						}
					} else if (res.data.status === 101) {
						resolve(res.data);
						msg(res.data.data.msg)
						jump(res.data.data.url, res.data.data.type, res.data.data.time)
						if (data && data.loading) {
							uni.hideLoading();
						}
					}else if (res.data.status =='go' || res.data.status =='402') {
						resolve(res.data);
						msg(res.data.msg)
						jump(res.data.data.url, res.data.data.type, res.data.data.time)
						if (data && data.loading) {
							uni.hideLoading();
						}
					} else {
						uni.showToast({
							title: res.data.msg,
							icon: 'none'
						});
						resolve(res.data);
					}
				},
				// fail: err => {
				// 	reject(err);
				// 	let msg = err.errMsg;

				// 	if (msg.indexOf('timeout') > -1) {
				// 		msg = '连接超时';
				// 	}

				// 	if (msg.indexOf('abort') > -1) {
				// 		msg = '连接终止';
				// 	}

				// 	uni.showToast({
				// 		title: msg || '网络错误！',
				// 		icon: 'none'
				// 	});
				// }
			});
		});
	}

}
const msg = (title, duration = 1500, mask = false, icon = 'none') => {
	if (Boolean(title) === false) {
		return;
	}

	uni.showToast({
		title,
		duration,
		mask,
		icon
	});
}; // 提示信息

function jump(url, type = 0, time) {
	// type 0 正常跳转
	// type 1 跳转底部
	if (url == '' && type == 0) {
		msg('开发中...');
		return;
	}
	if (type == 0) {
		setTimeout(() => {
			uni.navigateTo({
				url
			});
		}, time);
	} else if (type == 1) {
		setTimeout(() => {
			uni.switchTab({
				url
			});
		}, time);
	} else if (type == 2) {
		setTimeout(() => {
			uni.redirectTo({
				url
			});
		}, time);
	} else if (type == 3) {
		setTimeout(() => {
			uni.reLaunch({
				url
			});
		}, time);
	} else if (type == 4) {
		if (time == 0) {
			time = 1500;
		}

		setTimeout(() => {
			uni.navigateBack();
		}, time);
	}
} 
export default new ajax();
