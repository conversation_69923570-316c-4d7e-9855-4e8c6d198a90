<template>
	<div>
		<u-popup v-model="payModel" border-radius="24" mode="bottom" :mask-close-able="false" :closeable="false" class="shareWrap" safe-area-inset-bottom>
			<div class="pay-popup">
				<p class="title">{{AppName}}收银台</p>
				<div class="body u-p-t-20">
					<view class="u-m-40">
						<span class="u-font-xs" :style="{color:'#310FFF'}">¥</span>
						<span class="ausPrice">{{ totalPrice}}</span>
					</view>
					
					<div class="pay-item__wrap">
						<!--  #ifndef MP-WEIXIN -->
						<div class="pay-item flex-between" @click="changePayType(1)">
							<div class="pay-item__left ">
								<u-icon name="zhifubao" color="#02a9f1" size="48"></u-icon>
								<span class="payTypeName">支付宝支付</span>
							</div>
							<u-icon name="checkmark-circle-fill" color="#310FFF" size="42" v-if="payType == 1"></u-icon>
							<span class="unselectCircle" v-else></span>
						</div>
						<!--  #endif -->
						<!-- #ifndef H5 -->
						<div class="pay-item flex-between" @click="changePayType(2)">
							<div class="pay-item__left">
								<u-icon name="weixin-fill" color="#09bb07" size="48"></u-icon>
								<span class="payTypeName ">微信支付</span>
							</div>
						
							<u-icon name="checkmark-circle-fill" color="#310FFF" size="42" v-if="payType == 2"></u-icon>
							<span class="unselectCircle" v-else></span>
						</div>
						<!-- #endif -->
						
						<div class="pay-item flex-between" @click="changePayType(3)" v-if="scene!=='recharge' ">
							<div class="pay-item__left">
								<u-icon name="red-packet-fill" color="#a04ad7" size="48"></u-icon>
								<span class="payTypeName">当前余额 : {{remainingSum}}</span>
								<span class="u-text-left  u-m-l-20 u-font-xs " :style="{color:$u.color['error']}"
									v-if="(remainingSum-totalPrice)<=0">当前余额不足 </span>
							</div>

							<u-icon name="checkmark-circle-fill" color="#310FFF" size="42" v-if="payType ==3"></u-icon>
							<span class="unselectCircle" v-else></span>
						</div>


					</div>
					<p class="u-font-sm u-m-t-20" v-if="false">
						<u-icon name="info-circle" color="#999" size="28"> </u-icon>
						通过第三方支付需支付0.6%的手续费
					</p>
					<!-- 确认支付button slot-->
					<slot>
						<view class="pay-btn" @click="$u.throttle(crosspay,1500)">确认支付</view>
					</slot>

				</div>
			</div>
			<view @click="closePayPopUp()" class="u-close u-close--top-right">
				<u-icon name="close" color="#909399" size="30"></u-icon>
			</view>
		</u-popup>

		<u-popup v-model="bankModel" border-radius="12" mode="bottom">
			<div class="bankModelClass">
				<h3 class="u-text-center">请填写并提交您的支付信息</h3>
				<u-field class="u-m-t-20" v-model="cardName" v-if="false" label="银行" :clearable="false"
					placeholder="请填写您的所属银行">
				</u-field>
				<u-field v-model="cardNumber" label="银行卡号" placeholder="请填写您的银行卡号">
				</u-field>
				<u-field v-model="realName" label="姓名" placeholder="请填写持卡人姓名">
				</u-field>
				<u-field v-model="orderNumber" label="订单号" disabled :placeholder="orderNumber"
					placeholder-style="color:#333">
				</u-field>
				<u-button class="u-m-t-20" type="warning" @click="bankPay">提交</u-button>
			</div>

		</u-popup>
		
		<!-- 余额支付 键盘 -->
		<u-keyboard default="" ref="uKeyboard" mode="number" :mask="true" :mask-close-able="false" :dot-enabled="false"
			v-model="yuePayShow" :safe-area-inset-bottom="true" :tooltip="false" @change="onChange"
			@backspace="onBackspace">
			<view>
				<view class="u-text-center u-padding-20 money">
					<text>{{ totalPrice}}</text>
					<text class="u-font-20 u-padding-left-10">元</text>
					<view class="u-padding-10 close" data-flag="false" @click="showYuePay(false)">
						<u-icon name="close" color="#333333" size="28"></u-icon>
					</view>
				</view>
				<view class="u-flex u-row-center">
					<u-message-input mode="box" :maxlength="6" active-color="#310FFF" :dot-fill="true"
						v-model="password" :disabled-keyboard="true" @finish="finish"></u-message-input>
				</view>
				<view class="u-text-center u-padding-top-10 u-padding-bottom-20 tips">请输入支付密码</view>
			</view>
		</u-keyboard>
		<!-- #ifdef H5 -->
		<view v-html="payForm" class="pay-form"></view>
		<!-- #endif -->
		
		<u-toast ref="uToast" />
		<u-modal v-model="payTipShow" :show-cancel-button="true" content="请先设置支付密码" confirm-color="#310FFF" cancel-text="取消" confirm-text="去设置" :show-title="false" @confirm="$u.route('pagesB/setting/setPayPwd')" ref="uModal" ></u-modal>
		<u-modal v-model="cancelPayTipShow" :show-cancel-button="true" content="确定要取消支付吗？" confirm-color="#310FFF" cancel-text="继续支付" confirm-text="残忍取消" :show-title="false" @confirm="cancelPay()"></u-modal>
	</div>
</template>

<script>
	const time = 500
	import APPNAME from '@/config/index'

	export default {
		data() {
			return {
				payType: 1, //1 支付宝，2 微信
				payModel: false,
				clickPay: false,
				checked: false,
				bankModel: false,
				payinfos: {},
				AppName: '',
				orderNumber: '',
				cardName: '',
				cardNumber: '',
				realName: '',
				yuePayShow: false,
				password: '',
				remainingSum: 0,
				payTipShow: false,
				cancelPayTipShow: false,
				payForm: '',
			}
		},
		props: {

			totalPrice: {
				default: '0.00'
			},
			//   订单号
			orderNum: {
				default: 0
			},
			//   人民币数额
			totalPriceRmb: {
				default: 0
			},
			payMoneyNum: {
				default: 0
			},
			//场景        会员充值 ：'recharge'      订单支付：'orderPay'
			scene: {
				default: 'orderPay'
			},
			//   选择的会员卡类型
			vipType: {
				type: String,
				default: 'year'
			},
			//是否 在操作完成后 跳转到其他页面
			isLinkTo: {
				type: Boolean,
				default: false
			},
			// 操作完成后跳转到其他页面路径
			callbackUrl: {
				type: String,
				default: ''
			}
		},
		mounted() {
			this.AppName = APPNAME
			this.getUserMoney()
			// #ifdef MP-WEIXIN
			if(this.scene == 'recharge'){
				this.payType = 2
			}
			// #endif
		},
		onLoad() {
			
		},
		methods: {
			getUserMoney(){
				// this.$http.getWalletMoney().then((res) => {
				// 	this.remainingSum = res.data
					
				// })
			},
			initDetail() {
				var data = {
					order_num: this.orderNum
				}
				this.$http.orderDetail(data).then((res) => {
					this.payMoneyNum = res.data.orderinfo.order_num
				})
			},
			// 切换支付方式
			changePayType(serial) {
				this.payType = serial;
				this.$emit('update:payType', this.payType)
			},
			//   关闭收银台弹层时触发事件
			closePayPopUp() {
				this.cancelPayTipShow = true
			},
			cancelPay(){
				if(this.isLinkTo){
					this.$u.route({
						url: '/pagesC/order/allOrder',
						param: {index: 1},
						type: 'redirect'
					})
				}
				this.payModel = false;
			},
			uToast(mess = '', type = '', url = '') {
				this.$refs.uToast.show({
					title: mess,
					type: type,
					url: url,
				})
			},

			crosspay() {
				// #ifdef H5
				this.payForm = this.payinfos
				this.$loading()
				// setTimeout(()=>{
					this.$hideLoading()
					document.forms['alipaysubmit'].submit();
				},200)
				
				return false;
				// #endif

				const list = ['alipay', 'wxpay']
				uni.requestPayment({
					provider: list[this.payType - 1],
					// #ifdef APP-PLUS
					orderInfo: this.payinfos,
					// #endif
					// #ifdef MP-WEIXIN
					...this.payinfos,
					// #endif
					success: res => {
						this.uToast('支付成功', 'success')
						this.payModel = false
					},
					fail: err => {
						console.log("-> err", err);
						this.uToast('支付失败', 'error', '')
						this.payModel = false
					},
					complete: () => {
						if (this.callbackUrl) {
							this.$u.route({
								url: this.callbackUrl,
								type: 'redirect'
							})
						}
					}
				});


			},
			bankPay() {

				if (!this.orderNumber) {
					return this.$refs.uToast.show({
						title: '请填写您的银行卡号',
						type: 'error',
					})
				}
				if (!this.realName) {
					return this.$refs.uToast.show({
						title: '请填写持卡人姓名',
						type: 'error',
					})
				}
				const data = {
					zf_type: '' + this.payType,
					order_number: this.orderNumber,
					card_name: this.realName,
					card_number: this.cardNumber
				}
				this.$http.getzhifu(data).then(res => {

					if (res.status === 200) {

						this.bankModel = false
						this.$refs.uToast.show({
							title: res.mess,
							url: this.callbackUrl,
							type: 'success',
						})
					} else {
						this.bankModel = false
						this.$refs.uToast.show({
							title: '订单失败',
							type: 'error',
							back: true
						})

					}
				})
			},
			hasPayPwd(){
				this.$http.hasPayPwd().then(res => {
					if(res.status == 200){
						this.yuePayShow = true
					}else{
						this.payTipShow = true
					}
				});
			},
			yuePay() {
				/**
				 * 确认支付
				 */
				// this.$loading();
				this.$http.getzhifu({
						order_number: this.orderNumber,
						zf_type: 3,
						pay_password: this.password
					}).then(res => {
						console.log(res)
						if (res.status == 200) {
							this.$refs.uToast.show({
								title: res.mess,
								type: 'success'
							})
							this.yuePayShow = false
							this.password = ''
							setTimeout(() => {
								// this.valShow = index.backHied
								if (res.data.leixing == 1) {
									this.$u.route('/pagesC/order/orderDetails',{
										order_num: res.data.order_num,
										backindex:2
									})
								} else if (res.data.leixing == 2) {
									this.getnav(
										`/pagesC/goods/assemble?order_num=${res.data.order_num}`
									);
									this.$u.route('/pagesC/goods/assemble',{
										order_num: res.data.order_num
									})
								} else {
									this.$u.route('/pagesC/order/allOrder',{
										index: 2,
										backindex:2
									})
								}
							}, 1000);
						} else {
							
						}
					});
				 
			},
			showYuePay(flag = true) {
				this.password = '';
				this.yuePayShow = flag;
			},
			hidePopup() {
				this.$refs.popupCopun.close(); // 关闭
			},

			onChange(val) {
				if (this.password.length < 6) {
					this.password += val;
				}

				if (this.password.length >= 6) {
					this.yuePay();
				}
			},
			onBackspace(e) {
				if (this.password.length > 0) {
					this.password = this.password.substring(0, this.password.length - 1);
				}
			},

			finish(e) {
				console.log('finish')
			},

		}

	}
</script>

<style lang="scss">
	@import '~uview-ui/index.scss';

	.pay-popup {
		text-align: center;
		padding: 30upx 40upx;
	}

	.unselectCircle {
		width: 28upx;
		height: 28upx;
		margin: 4upx;
		border-radius: 200px;
		border: 2upx solid #d0d0d0;
		display: inline-block;
	}

	.shareWrap {
		.wrap {
			overflow-x: scroll;

			image {
				width: 100upx;
				height: 100upx;
			}

			.item {
				width: 160upx;
				float: left;
				flex-direction: column;
				text-align: center;

				.linkBtn {
					background: #e4e7e8;
					width: 80upx;
					height: 80upx;
					border-radius: 100%;

				}
			}
		}
	}

	.pay-popup {
		background: #fff;

		.iconWallet {
			background: #ff9900;
			width: 46upx;
			height: 46upx;
			border-radius: 10upx;
			display: inline-flex;
			justify-content: center;
			align-items: center;
			margin-right: 12upx;

		}

		.pay-btn {

			margin-top: 80upx;
			text-align: center;
			color: #fff;
			padding: 25upx;
			border-radius: 200px;
			background-image: linear-gradient(to left, #ffa468, #310FFF);
		}

		.pay-item__wrap {
			.pay-item {
				box-sizing: border-box;
				height: auto;
				display: flex;
				align-items: center;
				border-bottom: 4upx dashed #f5f5f5;
				height: 100upx;

			}
		}

		.body {

			.ausPrice {
				margin: 0 10upx;
				color: #310FFF;
				font-size: 62upx;
				font-weight: bold;
			}

			p {
				color: #999;
			}
		}

		.pay-item {
			&>image {
				width: 37upx;
				height: 37upx;
			}
		}

		.pay-item__left {
			display: flex;
			align-items: center;

			.payTypeName {
				font-size: 28upx;
				margin-left: 8upx;
			}

			image {
				width: 49upx;
				height: 49upx;
			}
		}
	}

	.money {
		font-size: 70rpx;
		color: #310FFF;
		position: relative;

		.close {
			position: absolute;
			top: 20rpx;
			right: 20rpx;
			line-height: 28rpx;
			font-size: 28rpx;
		}
	}

	.tips {
		color: $u-tips-color;
	}
	.u-close{
	    position: absolute;
	    z-index: 3;
	}
	.u-close--top-right {
	    top: 15px;
	    right: 15px;
	}
</style>
