<template>
	<view class="push_shop_box">
		<view class="top_step">
			<u-steps :list="numList" @clickStep="setStep" active-color="#310FFF" mode="number"
				:current="current"></u-steps>
		</view>
		<view class="shop_box">
			<view class="ship_tip">
				请选择商品的所属类别，商品发布审核通过后，您的商品将会出现在您所选择的分类当中。
			</view>


			<u-collapse :item-style="itemStyle" v-if="current==0">
				<u-collapse-item :title="item.name" v-for="(item, index) in itemList" :key="index"
					:open="item.id==prams.one.category_id">
					<view class="class_item" v-for="(item1,index1) in item.child" :key="index1"
						
						@click="changeClass(item1.id)">
						<view class="class_title">
							{{item1.name}}
						</view>
						<view class="radius">
							<image v-if="current1!=item1.id" :src="$fun.imgUrl('/static/apply/c.png')" mode="widthFix"></image>
							<image v-else :src="$fun.imgUrl('/static/apply/c1.png')" mode="widthFix"></image>
						</view>
					</view>
				</u-collapse-item>
			</u-collapse>
			<view class="shop_speci" v-else-if="current==1">
				<view class="shop_speci_item" v-for="(item, index) in shop_speciList" :key="index">
					<view class="shop_speci_title">
						<view class="t">
							{{item.title}}
						</view>
						<view class="del" @click="del_speci(1,index)">
							<image :src="$fun.imgUrl('/static/apply/del.png')" mode="widthFix"></image>
						</view>
					</view>
					<view class="shop_speci_list">
						<view class="shop_speci_item1" v-for="(item1, index1) in item.list" :key="index1">
							{{item1}}
							<image @click="del_speci(2,index,index1)" :src="$fun.imgUrl('/static/apply/del.png')" mode="widthFix">
							</image>
						</view>
						<view class="shop_speci_item1_add" @click="add_speci('添加规格','show',2,index)">
							<image :src="$fun.imgUrl('/static/apply/add1.png')" mode="widthFix"></image>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="shop_info" v-if="current==2">
			<view class="info_title" style="margin-top: 20rpx;">
				<view class="t tb">
					<view>
						商品封面图片 <text class="txt">（该图片用于商品列表页展示）</text>
					</view>
					<!-- <view class="num">
						0/1{{prams.three.image}}
					</view> -->
				</view>
				<view class="up_list">

					<view class="up_item" v-if="prams.three.image">
						<image @click="uploadImage('three','image')" :src="$fun.imgUrl(prams.three.image)">
						</image>
					</view>
					<view class="up_item" v-else>
						<image @click="uploadImage('three','image')" :src="$fun.imgUrl('/static/apply/upload.png')"></image>
					</view>
				</view>
			</view>
			<view class="info_title" style="margin-top: 20rpx;">
				<view class="t tb">
					<view>
						商品轮播图片
					</view>
					<!-- <view class="num">
						0/3
					</view> -->
				</view>
				<view class="up_list">
					<block v-if="prams.three.images.length>0">
						<view class="up_item" v-for="(item,index) in  prams.three.images" :key="index">
							<image @click="uploadImage('three','images',index)" :src="$fun.imgUrl(item)">
							</image>
						</view>
					</block>
					<view class="up_item" v-if="prams.three.images.length<5">
						<image @click="uploadImage('three','images','1008611')" :src="$fun.imgUrl('/static/apply/upload.png')"></image>
					</view>
				</view>
			</view>
			<view class="box2_item">
				<view class="title tb">
					<view>
						商品名称
					</view>
				</view>
				<view class="radio">
					<input type="text" v-model="prams.three.name" placeholder="请输入您的商品名称" />

				</view>
			</view>
			<view class="box2_item">
				<view class="title tb">
					<view>
						商品价格
					</view>
				</view>
				<view class="radio">
					<input type="digit" v-model="prams.three.money" placeholder="请输入您的商品价格" />
				</view>
			</view>
			<view class="box2_item">
				<view class="title tb">
					<view>
						市场价格
					</view>
				</view>
				<view class="radio">
					<input type="digit" v-model="prams.three.price" placeholder="请输入您的市场价格" />
				</view>
			</view>
			<view class="box2_item">
				<view class="title tb">
					<view>
						库存数量
					</view>
				</view>
				<view class="radio">
					<input type="number" v-model="prams.three.stock" placeholder="请输入您的库存数量" />
				</view>
			</view>
			<view class="box2_item">
				<view class="title tb">
					<view>
						限购数量
					</view>
				</view>
				<view class="radio">
					<input type="number" v-model="prams.three.maxnum" placeholder="请输入您的限购数量" />
				</view>
			</view>
			<view class="box2_item">
				<view class="title tb">
					<view>
						是否自提
					</view>
				</view>
				<view class="radio">
					<text :style="{color:prams.three.take?'#666666':'#310FFF'}">关闭</text>
					<switch @change="switchChange($event,'take',1)" color="#310FFF !important"
						:checked="prams.three.take==1"></switch>
						<text :style="{color:!prams.three.take?'#666666':'#310FFF'}">开启</text>
				</view>
			</view>
			<view class="box2_item">
				<view class="title tb">
					<view>
						是否包邮
					</view>
				</view>
				<view class="radio">
					<text :style="{color:isbao?'':'#310FFF'}">关闭</text>
					<switch @change="switchChange($event,'freight',0)" color="#310FFF !important" :checked="isbao">
					</switch>
					<text :style="{color:isbao?'#310FFF':''}">开启</text>
				</view>
			</view>
			<view class="box2_item" v-if="!isbao">
				<view class="title tb">
					<view>
						几件包邮
					</view>
				</view>
				<view class="radio">
					<input type="number" v-model="prams.three.freight_num" placeholder="请输入您的包邮数量" />
				</view>
			</view>
			<view class="box2_item" v-if="!isbao">
				<view class="title tb">
					<view>
						商品邮费
					</view>
				</view>
				<view class="radio">
					<input type="digit" v-model="prams.three.freight" placeholder="请输入您的商品邮费" />

				</view>
			</view>
			<view class="box2_item "
				style="flex-direction: column;justify-content: flex-start;align-items: flex-start;border: none;">
				<view class="title" style="margin-bottom: 20rpx;">
					商品详情
				</view>
				<view class="radio">
					<textarea name="" v-model="prams.three.content" placeholder="请输入您的商品详情" id="" cols="30"
						rows="10"></textarea>
				</view>
				<view class="r">
				</view>
			</view>
			<view class="box2_item" style="flex-wrap: wrap; border: none;padding-bottom: 0;"
				v-if="shop_speciList_price.length">
				<view class="title " style="width: 100%;">
					<view>
						商品规格
					</view>
				</view>
			</view>
			<view class="box2_item" style="flex-wrap: wrap;" v-for="(item,index) in shop_speciList_price" :key="index">
				<view class="title tb" style="width: 100%;">
					<view>
						{{item.key}}
					</view>
				</view>
				<view class="title tb" style="margin-top: 20rpx;">
					<view style="color: #333333;font-weight: normal;">
						价格
					</view>
				</view>
				<view class="radio" style="margin-top: 20rpx;">
					<input type="digit" v-model="item.value" placeholder="请输入您的商品价格" />

				</view>
			</view>
		</view>
		<u-modal ref="uModal" v-model="show" @confirm="confirm" :async-close="true" :title="title"
			:cancel-color="'#333333'" :confirm-color="'#FF2F00'" :show-cancel-button="true">
			<view class="box_input">
				<input type="text" v-model="addGg" placeholder="请输入规格名称" />
			</view>
		</u-modal>
		<view style="height: 300rpx;">

		</view>
		<view class="net_btn1" v-if="current==1" @click="add_speci('添加规格','show')">
			添加规格
		</view>
		<view class="net_btn" @click="nextBtn()">
			{{current==2?'提交':'下一步'}}
		</view>
		<view class="fb_flex1" v-if="shop_id==null" @click="saveJson">
			<text>暂存</text>
			<text>数据</text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				itemStyle: {
					marginTop: '20px',
					background: '#FFFFFF',
					padding: '18rpx 24rpx',
					borderRadius: ' 8rpx 8rpx 8rpx 8rpx',
				},
				numList: [{
					name: '选择类别'
				}, {
					name: '选择规格'
				}, {
					name: '录入商品'
				}],
				itemList: [],
				current: 0,
				current1: 0,
				show: false,
				title: '',
				addGg: '',
				isOne: true,
				oneIndex: null,
				shop_speciList: [],
				shop_speciList_price: [],
				prams: {
					one: {
						category_id: '',
					},
					two: {
						specifications: []
					},
					three: {
						name: '', //  产品名称
						image: '', //  主图
						images: [], // 轮播图
						money: '', //展示金额
						price: '', //市场价格
						stock: '', //库存数量
						freight: '', //运费
						// json: '', //规格
						content: '', //产品详情
						take: 0, //自提
						maxnum: '', //限购
						freight_num: 1, //几件包邮
					}
				},
				isbao: false,
				shop_id: null,
			}
		},
		onLoad(option) {
			if (option.id) {
				this.shop_id = option.id
				this.getShop(this.shop_id);
			} else {
				this.getShop(this.shop_id, 'common/cache?key=goods');
			}
			this.getCategory();
		},
		methods: {
			switchChange(e, str, type) {
				if (type == 0) {
					this.isbao = e.detail.value;
					this.prams.three[str] = e.detail.value ? 0 : '';
					this.$forceUpdate()
					return;
				}
				this.prams.three[str] = e.detail.value ? 1 : 0;
				this.$forceUpdate()
			},
			setStep(e) {
				if (e != 0) {
					if (!this.prams.one.category_id) {
						return
					}
				}
				this.current = e;
			},
			getShop(id, str = '') {
				let url = 'business/getGoods';
				let prams = {
					id: id
				};
				if (str) {
					url = str;
					prams = {}
				}

				this.$fun.ajax.post(url,prams).then(res => {
					if (res.status == 1) {
						if(res.data){
							this.prams.one.category_id = res.data.category_id;
							this.current1 = res.data.category_id;
							let {
								name,
								image,
								images,
								money,
								price,
								stock,
								freight,
								content,
								take,
								maxnum
							} = res.data;
							this.prams.three = {
								name,
								image,
								images,
								money,
								price,
								stock,
								freight,
								content,
								take,
								maxnum,
								freight
							}
							this.isbao = freight==0?true:false;
							this.prams.three.images = this.prams.three.images.split(',');
							this.shop_speciList_price = JSON.parse(res.data.mjson);
							console.log(res.data.mjson)
							let json = JSON.parse(res.data.json);
							let arr = []
							for (var key in json) {
								arr.push({
									title: key,
									list: json[key].split('-')
								})
							}
							this.shop_speciList = arr;
						}
						
					}
				})
			},
			submit(type = 0) {
				let prams = JSON.parse(JSON.stringify(this.prams.three))

				let arr = {};
				let arr1 = this.shop_speciList;
				for (var i = 0; i < arr1.length; i++) {
					arr[arr1[i].title] = arr1[i].list.join('-')
				}
				prams.json = JSON.stringify(arr);
				prams.images = prams.images.join(',');
				prams.mjson = JSON.stringify(this.shop_speciList_price);
				prams.category_id = this.prams.one.category_id;
				if (this.shop_id) {
					prams.id = this.shop_id
				}
				if (type == 0) {
					this.$fun.ajax.post('business/addGoods', prams).then(res => {
						if (res.status == 1) {
							this.$fun.msg('添加成功')
							setTimeout(() => {
								uni.navigateBack()
							}, 1000);
						}
					})
				} else {
					// prams.key = 'goods'
					this.$fun.ajax.post('common/cache?key=goods', prams).then(res => {
						if (res.status == 1) {
							this.$fun.msg('暂存成功')
						}
					})
				}
			},
			changeClass(id) {
				this.current1 = id;
				this.prams.one.category_id = id;
			},
			getCategory() {
				this.$fun.ajax.post('business/getCategory', {}).then(res => {
					if (res.status == 1) {
						this.itemList = res.data
					}
				})
			},
			getIsok(prams, prams1 = null, type = 1) {
				let isok = true;
				Object.getOwnPropertyNames(prams).forEach((key, item) => {
					if (prams[key] === "") {
						console.log(key)
						isok = false
						return
					}
				})
				if (prams1 && type != 1) {
					console.log(123)
					prams1.forEach(item => {
						Object.getOwnPropertyNames(item).forEach((key) => {
							if (item[key] === "") {
								console.log(item)
								isok = false
								return
							}
						})
					})
				}
				return isok
			},
			nextBtn() {
				console.log(this.current)
				if (this.current == 0) {
					if (this.getIsok(this.prams.one)) {
						this.current += 1
					} else {
						this.$fun.msg('请选择类别')
					}
				} else if (this.current == 1) {
					// let isok = this.getgg();
					// console.log(isok)
					// if (isok) {
					this.setJson()
					// return
					this.current += 1
					// console.log()
					// }
				} else if (this.current == 2) {
					if (this.getIsok(this.prams.three, this.shop_speciList_price, 2)) {
						this.submit();
					} else {
						this.$fun.msg('请上传完整资料')
					}
				}
			},
			getgg() {
				let arr = this.shop_speciList;
				if (arr.length == 0) {
					this.$fun.msg('请填写完整规格')
					return false
				}
				let isok = true
				for (var i = 0; i < arr.length; i++) {
					if (arr[i].list.length == 0) {
						this.$fun.msg('请填写完整规格')
						isok = false
						return
					}
				}
				return isok
			},
			setJson() {

				this.shop_speciList_price = this.generateCombinations();
				console.log(this.shop_speciList_price)
				// return JSON.stringify(arr)
			},
			// generateCombinations() {
			// 	const result = [];
			// 	const generate = (current, index) => {
			// 		if (index === this.shop_speciList.length) {
			// 			result.push({
			// 				key: current.slice(1),
			// 				value: "",
			// 			}); // 去掉开头的 '-'
			// 			return;
			// 		}
			// 		const spec = this.shop_speciList[index];
			// 		for (const item of spec.list) {
			// 			generate(`${current}-${spec.title}:${item}`, index + 1);
			// 		}
			// 	};
			// 	generate('', 0);
			// 	console.log(result)
			// 	return result;
			// },
			generateCombinations() {
				const combine = (lists) => {
					if (lists.length === 0) return [
						[]
					];
					const first = lists[0];
					const rest = combine(lists.slice(1));
					return first.flatMap(f => rest.map(r => [f, ...r]));
				};

				const lists = this.shop_speciList.map(spec => spec.list);
				const allCombinations = combine(lists);

				let arr = allCombinations.map(combination => {
					const key = combination.map((value, index) => `${this.shop_speciList[index].title}:${value}`)
						.join('-');
					const valueObj = this.shop_speciList_price.find(v => v.key === key);
					return {
						key,
						value: valueObj ? valueObj.value : ''
					};
				});
				if (arr.length == 1) {
					if (arr[0].key == '') {
						arr = []
					}
				}
				return arr;
			},
			add_speci(title, show, type = 1, index) {
				this.addGg = ''
				this.title = title;
				this[show] = true;
				this.isOne = true;
				this.oneIndex = null;
				if (type != 1) {
					this.isOne = false;
					this.oneIndex = index
					console.log(index)
				}
			},
			del_speci(type = 1, index, index1) {
				uni.showModal({
					title: '提示',
					content: '确定删除吗？',
					success: (res) => {
						if (res.confirm) {
							if (type == 1) {
								this.shop_speciList.splice(index, 1)
								return
							}
							this.shop_speciList[index].list.splice(index1, 1)
						}
					}
				})

			},
			confirm() {
				if (!this.addGg) {
					this.$fun.msg('请输入规格名称');
					this.$refs.uModal.clearLoading();
					return
				}
				if (this.isOne) {
					this.shop_speciList.push({
						title: this.addGg,
						list: []
					})
				} else {
					this.shop_speciList[this.oneIndex].list.push(this.addGg)
				}
				this.show = false;
			},
			// 上传头像
			uploadImage(key, key1, index = null) {
				// 从相册选择图片
				const _this = this;
				uni.chooseImage({
					count: 1,
					sizeType: ['original', 'compressed'],
					sourceType: ['album'],
					success: function(res) {
						_this.handleUploadFile(res.tempFilePaths, key, key1, index);
					}
				});
			},
			// 上传头像
			handleUploadFile(data, key, key1, index) {
				const _this = this;
				const filePath = data.path || data[0];
				this.$fun.uploadPic(
					filePath
				).then(res => {
					this.$fun.msg(res.msg)
					if (res.status == 1) {
						if (index == '1008611') {
							console.log(1231)
							_this.prams[key][key1].push(res.data.url);
							return
						}
						if (index == null) {
							_this.prams[key][key1] = res.data.url;
							console.log(_this.prams[key][key1])
						} else {
							_this.prams[key][key1][index] = res.data.url;
							this.$forceUpdate()
						}

					}
				})
			},
			saveJson() {
				this.submit(1)
			}
		}
	}
</script>

<style lang="scss">
	.push_shop_box {
		.top_step {
			margin-top: 32rpx;
		}

		.shop_box {
			margin: 24rpx;

			.ship_tip {
				padding: 14rpx 24rpx;
				background: #FFE9E4;
				border-radius: 8rpx 8rpx 8rpx 8rpx;
				border: 2rpx solid #310FFF;
				font-weight: 400;
				font-size: 20rpx;
				color: #310FFF;
				line-height: 23rp
			}

			.class_item {
				margin-left: 80rpx;
				width: calc(100% - 80rpx);
				padding: 16rpx 0;
				height: 74rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.class_title {
					font-weight: 400;
					font-size: 28rpx;
					color: #333333;
				}

				.radius {
					display: flex;
					align-items: center;

					image {
						width: 24rpx;
					}
				}
			}
		}

		.box_input {
			margin: 30rpx 24rpx;
			padding: 30rpx 24rpx;
			border-radius: 12rpx 12rpx 12rpx 12rpx;
			border: 2rpx solid #E4E4E4;
		}


		// 规格
		.shop_speci {
			.shop_speci_item {
				margin-top: 24rpx;
				padding: 24rpx;
				background: #FFFFFF;
				border-radius: 8rpx 8rpx 8rpx 8rpx;

				.shop_speci_title {
					display: flex;
					padding-bottom: 6rpx;
					justify-content: space-between;
					border-bottom: 1rpx solid #E4E4E4;


					.t {
						font-weight: 400;
						font-size: 28rpx;
						color: #333333;
					}

					.del {
						image {
							width: 32rpx;
						}
					}
				}

				.shop_speci_list {
					margin-top: 16rpx;
					display: flex;
					flex-wrap: wrap;

					.shop_speci_item1 {
						margin-bottom: 16rpx;
						margin-right: 24rpx;
						padding: 16rpx 24rpx;
						background: #310FFF;
						border-radius: 8rpx 8rpx 8rpx 8rpx;
						font-weight: 400;
						font-size: 24rpx;
						color: #FFFFFF;
						position: relative;

						image {
							width: 28rpx;
							position: absolute;
							top: 0;
							right: 0;

						}
					}

					.shop_speci_item1_add {
						margin-bottom: 16rpx;
						width: 120rpx;
						height: 68rpx;
						background: #310FFF;
						display: flex;
						align-items: center;
						justify-content: center;
						border-radius: 8rpx 8rpx 8rpx 8rpx;

						image {
							width: 44rpx;
						}
					}
				}
			}
		}



		// 商品信息
		.shop_info {
			margin: 24rpx;
			padding: 24rpx;
			background: #FFFFFF;
			border-radius: 8rpx 8rpx 8rpx 8rpx;
			padding: 18rpx 24rpx;

			.info_title {
				display: flex;
				flex-direction: column;
				padding-bottom: 34rpx;
				border-bottom: 1rpx solid #E4E4E4;

				.t {
					font-weight: bold;
					font-size: 28rpx;
					color: #333333;
					position: relative;

					.txt {
						color: #666666;
						font-size: 26rpx;
						font-weight: normal;
					}

					display: flex;
					justify-content: space-between;

					.num {
						font-weight: 400;
						font-size: 26rpx;
						color: #AAAAAA;
					}
				}

				.tb::after {
					position: absolute;
					content: '*';
					top: 5rpx;
					left: -15rpx;
					color: #310FFF;
					font-size: 28rpx;
				}

				.up_list {
					display: flex;
					flex-wrap: wrap;
					.up_item {
						margin-right: 20rpx;
						display: flex;
						flex-direction: column;
						align-items: center;

						.t {

							font-weight: 400;
							font-size: 24rpx;
							color: #666666;
						}
					}
				}

				image {
					margin-top: 20rpx;
					width: 178rpx;
					height: 178rpx;
				}
			}


			.box2_item {
				display: flex;
				padding: 30rpx 0;
				align-items: center;
				justify-content: space-between;
				position: relative;
				border-bottom: 1rpx solid #E4E4E4;

				.title {
					width: 230rpx;
					font-weight: 500;
					font-size: 28rpx;
					color: #333333;

				}

				.tb::after {
					position: absolute;
					content: '*';
					top: 35rpx;
					left: -15rpx;
					color: #310FFF;
					font-size: 28rpx;
				}

				.radio {
					input {
						text-align: right;
					}

					textarea {
						padding: 24rpx;
						width: 654rpx;
						height: 270rpx;
						border-radius: 12rpx 12rpx 12rpx 12rpx;
						border: 2rpx solid #E4E4E4;
					}
					switch{
						margin:  0 20rpx;
					}
				}

				.r {
					image {
						width: 24rpx;
					}
				}

				.b {
					position: absolute;
					top: 0;
					left: 0;
					background: transparent;
					width: 100%;
					height: 100%;
				}
			}



		}

		.net_btn1 {
			position: fixed;
			bottom: 200rpx;
			left: calc((100% - 560rpx)/2);
			width: 560rpx;
			height: 76rpx;
			border: 2rpx solid #310FFF;
			display: flex;
			align-items: center;
			justify-content: center;
			font-weight: 500;
			font-size: 32rpx;
			color: #310FFF;
			border-radius: 38rpx 38rpx 38rpx 38rpx;
		}

		.net_btn {
			position: fixed;
			bottom: 100rpx;
			left: calc((100% - 560rpx)/2);
			width: 560rpx;
			height: 76rpx;
			background: #310FFF;
			display: flex;
			align-items: center;
			justify-content: center;
			font-weight: 500;
			font-size: 32rpx;
			color: #FFFFFF;
			border-radius: 38rpx 38rpx 38rpx 38rpx;
		}
	}

	.fb_flex1 {
		position: fixed;
		right: 32rpx;
		bottom: 350rpx;
		width: 110rpx;
		height: 110rpx;
		background: #310FFF;
		border-radius: 50%;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;

		text {
			font-weight: 400;
			font-size: 24rpx;
			color: #FFFFFF;
		}
	}
</style>