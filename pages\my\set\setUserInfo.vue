<template>
	<view class="page">
		<!-- 用户信息列表 -->
		<view class="user-list">
			<view class="list" style="height: 160rpx;">
				<view class="title">
					<text>头像</text>
				</view>
				<view class="more-content">
					<image :src="$fun.imgUrl(userInfo.avatar)" @click="uploadImage" mode=""></image>
							<button class="avatar-wrapper" open-type="chooseAvatar"
						style="position: absolute;top: 0;left: 0;opacity: 0;width: 100upx; height: 100upx; border-radius: 100%;"
						@chooseavatar="handleUploadFile">

					</button>
					<text class="iconfont icon-more more"></text>
				</view>
			</view>
			<view class="list">
				<view class="title">
					<text>昵称</text>
				</view>
				<view class="more-content">
					<input style="text-align: right;" type="nickname" 
						class="content" maxlength="5" v-model="userInfo.nickname">
			<!-- 			<input style="text-align: right;" type="nickname"
						@focus="nicknameFocus" @blur="onNickName" class="content" v-model="userInfo.nickname"> -->
					<!-- <text class="content">{{userInfo.nickname}}</text> -->
					<!-- <text class="iconfont icon-more more"></text> -->
				</view>
			</view>
			<!-- <view class="list" @click="onNickname">
				<view class="title">
					<text>姓名</text>
				</view>
				<view class="more-content">
					<text class="content">{{userInfo.nickname}}</text>
					<text class="iconfont icon-more more"></text>
				</view>
			</view> -->
<!-- 			<view class="list">
				<view class="title">
					<text>性别</text>
				</view>
				<view class="more-content">
					<text class="content">{{userInfo.sex?'男':'女'}}</text>
					<text class="iconfont icon-more more"></text>
				</view>
				<view class="picker">
					<picker @change="sexPickerChange" :value="sexIndex" :range="sexArray">
						<view class="uni-input" style="height: 100rpx;">{{sexText}}</view>
					</picker>
				</view>
			</view> -->
			<!-- <view class="list">
				<view class="title">
					<text>年龄</text>
				</view>
				<view class="more-content">
					<input style="text-align: right;"  type="number" class="content" v-model="userInfo.age">
				</view>
			</view> -->
			<view class="list">
				<view class="title">
					<text>联系电话</text>
				</view>
				<view class="more-content" style="position: relative;">
					<input style="text-align: right;" disabled  type="mobile" placeholder="请点击获取手机号" class="content" v-model="userInfo.mobile">
					<button v-if="!userInfo.mobile" class="avatar-wrapper" open-type="getPhoneNumber"
						style="position: absolute;top: 0;left: 0;width: 100%; height: 100%;z-index: 10;opacity: 0; "
						@getphonenumber="onGetPhoneNumber">
					
					</button>
				</view>
			</view>
			<!-- <view class="list">
				<view class="title">
					<text>家庭住址</text>
				</view>
				<view class="more-content">
					<input @click="lotusAddressData.visible = true" v-if="userInfo.address" style="text-align: right;" disabled  type="mobile" class="content" v-model="userInfo.address">
					<text v-else @click="lotusAddressData.visible = true" class="content" >请选择您的家庭住址</text>
				</view>
			</view> -->
			
			<!-- 
			<view class="list">
				<view class="title">
					<text>出生日期</text>
				</view>
				<view class="more-content">
					<text class="content">{{userInfo.birthday}}</text>
					<text class="iconfont icon-more more"></text>
				</view>
				<view class="picker">
					<picker @change="birthdayPickerChange" mode="date" :value="birthdayDate" :start="startDate" :end="endDate">
						<view class="uni-input" style="height: 100rpx;">{{birthdayDate}}</view>
					</picker>
				</view>
			</view> -->
			<view style="height: 100rpx;">

			</view>
			<button style="background: linear-gradient(to right, #310FFF, #310FFF);" class="add-btn" @tap="confirm">
				提交
			</button>
		</view>
		<lotusAddress v-on:choseVal="choseValue" :lotusAddressData="lotusAddressData"></lotusAddress>
		<!-- 提示框 -->
		<DialogBox ref="DialogBox"></DialogBox>
	</view>
</template>

<script>
	import lotusAddress from "@/components/Winglau14-lotusAddress/Winglau14-lotusAddress.vue";
	export default {
		components: {
			lotusAddress
		},
		data() {
			const currentDate = this.getDate({
				format: true
			})
			return {
				// 性别
				sexArray: ['女', '男'],
				sexIndex: 0,
				sexText: '保密',
				// 生日
				birthdayDate: currentDate,
				startDate: this.getDate('start'),
				endDate: this.getDate('end'),
				birthday: '2020-02-02',
				DialogBox: {},
				// 昵称
				nickname: '',
				userInfo: {},
				lotusAddressData: {
					visible: false,
					provinceName: '',
					cityName: '',
					townName: '',
				},
			};
		},
		onLoad(option) {
			if (option.type) {
				this.$fun.msg('请先修改自己的姓名')
			}
			this.getUserInfo()
		},
		methods: {
			onGetPhoneNumber(e) {
				let _this = this;
				if (e.detail.errMsg == "getPhoneNumber:fail user deny") { //用户决绝授权  
					//拒绝授权后弹出一些提示  
					this.$fun.msg('授权失败无法获取手机号');
				} else {
					//允许授权  
					uni.login({
						provider: 'weixin',
						success: function(loginRes) {
							let prams = {
								code: loginRes.code,
								encryptedData: e.detail.encryptedData,
								iv: e.detail.iv,
								newcode: ''
							}
							_this.$fun.ajax.post('user/getMobile', prams).then(res => {
								if (res.status == 1) {
									_this.$fun.msg(res.msg);
									_this.userInfo.mobile=res.data;
			
								} else {
									_this.$fun.msg(res.msg);
								}
							})
						}
					});
			
				}
			},
			//回传已选的省市区的值
						choseValue(res) {
							//res数据源包括已选省市区与省市区code
							console.log(res);
							//res.isChose = 1省市区已选 res.isChose = 0;未选
							if (res.isChose) {
								this.lotusAddressData.visible = res.visible; //visible为显示与关闭组件标识true显示false隐藏
								this.lotusAddressData.provinceName = res.province; //省
								this.lotusAddressData.cityName = res.city; //市
								this.lotusAddressData.townName = res.town; //区
								this.userInfo.address = `${res.province} ${res.city} ${res.town}`; //region为已选的省市区的值
								this.$forceUpdate()
							} else {
								this.$fun.msg('请完整选择地址')
							}
						},
			onNickName(e) {
				this.userInfo.nickname = e.detail.value
			},
			nicknameFocus(e) {
				console.log(e)
				this.userInfo.nickname = ''
			},
			confirm() {
				if (!this.userInfo.avatar || this.userInfo.avatar.slice(0, 10) == 'data:image') {
					this.$fun.msg('请上传头像');
					return;
				}
				if (!this.userInfo.username) {
					this.$fun.msg('请选您的姓名！')
					return;
				}
				// if (!this.userInfo.sex) {
				// 	this.$fun.msg('请选您的性别！')
				// 	return;
				// }
				// if (!this.userInfo.age) {
				// 	this.$fun.msg('请输入您的年纪！')
				// 	return;
				// }
				// if (!this.userInfo.age) {
				// 	this.$fun.msg('请输入您的手机号！')
				// 	return;
				// }
				// if (!this.userInfo.age) {
				// 	this.$fun.msg('请选择您的家庭地址！')
				// 	return;
				// }
				let params = {
					avatar: this.userInfo.avatar,
					nickname: this.userInfo.nickname,
					// sex: this.userInfo.sex,
					// age: this.userInfo.age,
					mobile: this.userInfo.mobile,
					// address: this.userInfo.address,
					// birthday: this.userInfo.birthday,
				}
				this.$fun.ajax.post('user/profile', {
					...params
				}).then(res => {
					if (res.status == 1) {
						this.$fun.msg(res.msg)
						this.getUserInfo()
						this.$fun.jump('/pages/my/my', 3, 1500)
					}
				})
			},
			getUserInfo() {
				this.$fun.ajax.post('user/index', {}).then(res => {
					if (res.status == 1) {
						this.userInfo = res.data
						this.nickname = res.data.nickname
						uni.setStorageSync('userInfo', res.data)
					}
				})
			},
			// 上传头像

			uploadImage() {
				// 从相册选择图片
				var _this = this;
				console.log(11111111111111)
				try {
					console.log(333333.)
					uni.chooseImage({
						count: 1, //默认选择1张图片
						sizeType: ['original', 'compressed'], //original 原图，compressed 压缩图，默认二者都有
						sourceType: ['album'],
						success: function(res) {
							_this.handleUploadFile(res.tempFilePaths);
						}
					});
				} catch (error) {
					conso.log('333333.', error)
				}
				console.log(2222222222)
			},
			// 上传头像
			handleUploadFile(data) {
				const _this = this;
				const filePath = data.path || data[0] || data.detail.avatarUrl;
				console.log(filePath)
				this.$fun.uploadPic(
					filePath
				).then(res => {
					this.$fun.msg(res.msg)
					if (res.status == 1) {
						_this.userInfo.avatar = res.data.url;
					}
				})
			},
			/**
			 * 性别
			 * @param {Object} e
			 */
			sexPickerChange(e) {
				this.sexIndex = e.detail.value;
				this.sexText = this.sexArray[this.sexIndex];
				this.userInfo.sex = e.detail.value
			},
			/**
			 * 生日
			 * @param {Object} e
			 */
			birthdayPickerChange(e) {
				this.birthday = e.detail.value;
				this.userInfo.birthday = e.detail.value;
			},
			/**
			 * 获取日期
			 * @param {Object} type
			 */
			getDate(type) {
				const date = new Date();
				let year = date.getFullYear();
				let month = date.getMonth() + 1;
				let day = date.getDate();

				if (type === 'start') {
					year = year - 60;
				} else if (type === 'end') {
					year = year + 2;
				}
				month = month > 9 ? month : '0' + month;;
				day = day > 9 ? day : '0' + day;
				return `${year}-${month}-${day}`;
			},
			/**
			 * 昵称点击
			 */
			onNickname() {
				this.$refs['DialogBox'].confirm({
					title: '更改姓名',
					placeholder: '请输入修改的昵称',
					value: this.nickname,
					DialogType: 'input',
					animation: 0
				}).then((res) => {
					this.userInfo.nickname = res.value;
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	.page {
		position: absolute;
		left: 0;
		bottom: 0;
		width: 100%;
		height: 100%;
		background-color: #FFFFFF;
	}

	/* 用户信息列表 */
	.user-list {
		padding: 0 4%;
		background-color: #FFFFFF;
		border-radius: 20rpx;
		margin: 20rpx auto;

		.list {
			position: relative;
			display: flex;
			align-items: center;
			justify-content: space-between;
			width: 100%;
			height: 100rpx;
			border-bottom: 2rpx solid #f6f6f6;

			.title {
				display: flex;
				align-items: center;

				text {
					font-size: 28rpx;
					color: #222222;
				}
			}

			.more-content {
				display: flex;
				align-items: center;
				position: relative;


				image {
					width: 100rpx;
					height: 100rpx;
					border-radius: 100%;
				}

				.content {
					font-size: 28rpx;
					color: #959595;
				}

				.more {
					font-size: 24rpx;
					color: #959595;
					margin-left: 20rpx;
				}
			}

			.picker {
				position: absolute;
				width: 100%;
				height: 100%;
				opacity: 0;
			}
		}
	}
</style>