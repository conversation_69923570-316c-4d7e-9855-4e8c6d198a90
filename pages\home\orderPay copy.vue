<template>
	<view class="page">
		<view class="price-count-down">
			<view class="price">
				<text class="min">￥</text>
				<text class="max">{{orderInfo.money}}</text>
			</view>
			<view class="count-down" v-if="CountDown">
				<view class="title">支付剩余时间</view>
				<view class="count">
					<text class="time">{{hour>10?hour:'0'+hour}}</text>
					<text class="dot">:</text>
					<text class="time">{{min>10?min:'0'+min}}</text>
					<text class="dot">:</text>
					<text class="time">{{sec>10?sec:'0'+sec}}</text>
				</view>
			</view>
			<view class="count-down" v-else>
				<view class="title">订单已超时</view>
			</view>
		</view>
		<!-- 支付方式列表 -->
		<view class="pay-way">
			<view class="pay-list">
				<view class="list" v-for="(item,index) in orderInfo.pay" @click="onPayWay(item,index)" :key="index">
					<view class="pay-type">
						<image :src="$fun.imgUrl(item.image)" mode=""></image>
						<text>{{item.name}}</text>
						<text style="color: #310FFF;font-size: 20rpx;">({{item.money}})</text>
					</view>
					<view class="check">
						<radio :checked="PayWay == index" color="#310FFF" />
						<!-- <text class="iconfont" :class="PayWay === index ? 'icon-checked action':'icon-check'"></text> -->
					</view>
				</view>
			</view>
		</view>
		<view class="pay-submit">
			<view class="submit" @click="onSubmit">{{PayPirce}}</view>
		</view>
		<passkeyborad ref="passkeyborad" @configPay="configPay" :show="show" @close="onSubmit"
			:payTitle="orderInfo.pay[PayWay].name" :payMoney="orderInfo.money"></passkeyborad>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				PayList: [{
					icon: '/static/wx_pay.png',
					name: '微信支付',
				}, {
					icon: '/static/zfb_pay.png',
					name: '支付宝支付',
				}, {
					icon: '/static/ye_pay.png',
					name: '余额支付',
				}, ],
				PayWay: 0,
				PayPirce: ``,
				CountDown: 1800,
				day: 0,
				hour: 0,
				min: 0,
				sec: 0,
				orderInfo: {
					money:0
				},
				show: false,
				oid: null
			};
		},
		onLoad(option) {
			// this.CountDownData();
			this.oid = option.oid
			this.getpayWallet(option.oid)
		},
		methods: {
			/**
			 * 支付方式切换点击
			 */
			onPayWay(item, index) {
				this.PayWay = index;
				this.PayPirce = `${item.name}￥${this.orderInfo.money}`
			},
			/**
			 * 倒计时
			 */
			CountDownData() {
				setTimeout(() => {
					this.CountDown--;
					this.day = parseInt(this.CountDown / (24 * 60 * 60))
					this.hour = parseInt(this.CountDown / (60 * 60) % 24);
					this.min = parseInt(this.CountDown / 60 % 60);
					this.sec = parseInt(this.CountDown % 60);
					if (this.CountDown <= 0) {
						return
					}
					this.CountDownData();
				}, 1000)
			},
			// 支付订单
			configPay(pay, type = 1) {
				this.$fun.ajax.post('order/pay', {
					oid: this.oid,
					type: this.orderInfo.pay[this.PayWay].code,
					pay
				}).then(res => {
					this.show = false
					if (res.status == 1) {
						this.$fun.msg(res.msg)
						if (type == 1) {
							this.$fun.jump(`/pages/my/myOrder/orderDetails?order_num=${this.oid}`,2,800)
						} else {
							return res.data
						}
					}
				})
			},
			/**
			 * 支付点击
			 */
			async onSubmit() {
				let vm = this;
				if (this.orderInfo.pay[this.PayWay].code.indexOf('wechat') == -1) {
					this.show = !this.show
					this.$refs.passkeyborad.clear()
				} else {
					// #ifdef H5
					var ua = navigator.userAgent.toLowerCase();
					var isWeixin = ua.indexOf('micromessenger') !== -1;
					// if (!isWeixin) {
					// 	console.log( res.data)
					// 	location.href= res.data
					// } else {
						this.getpayInfo()
					// }
					// #endif
					// #ifdef MP-WEIXIN
					this.getpayInfo()
					// #endif
				}
			},
			getpayInfo() {
				let vm = this;
				this.$fun.ajax.post('order/pay', {
					oid: this.oid,
					type: this.orderInfo.pay[this.PayWay].code
				}).then(res => {
					this.show = false
					if (res.status == 1) {
						this.$fun.msg(res.msg)
						let data = res.data
						// #ifdef H5 || APP-PLUS
						var ua = navigator.userAgent.toLowerCase();
						var isWeixin = ua.indexOf('micromessenger') !== -1;
						if (!isWeixin) {
							console.log(11111111)
							location.href= res.data
						} else {
							if (typeof WeixinJSBridge === 'undefined') { // 微信浏览器内置对象。参考微信官方文档
								if (document.addEventListener) {
									document.addEventListener('WeixinJSBridgeReady', vm.wxpay(data), false)
								} else if (document.attachEvent) {
									document.attachEvent('WeixinJSBridgeReady', vm.wxpay(data))
									document.attachEvent('onWeixinJSBridgeReady', vm.wxpay(data))
								}
							} else {
								vm.wxpay(data)
							}
						}
						
						// #endif
						// #ifdef MP-WEIXIN
						uni.requestPayment({
							appId: data.appId,
							provider: 'wxpay',
							timeStamp: data.timeStamp,
							nonceStr: data.nonceStr,
							package: data.package,
							signType: data.signType,
							paySign: data.paySign,
							success: function(res) {
								vm.$toast('支付成功')
								// uni.reLaunch({
								// 	url: '/pagesC/order/orderDetails?order_num=' +
								// 		vm.oid
								// })
								uni.redirectTo({
									url: '/pagesC/order/orderDetails?order_num=' +
										vm.oid
								});
								// console.log('success:' + JSON.stringify(res));
							},
							fail: function(err) {
								vm.$toast('支付失败')
							}
						});
						// #endif
					}
				})
			},
			wxpay(data) {
				var vm = this
				WeixinJSBridge.invoke(
					'getBrandWCPayRequest', { // 下面参数内容都是后台返回的
						'appId': data.appId, // 公众号名称，由商户传入
						'timeStamp': data.timeStamp, // 时间戳
						'nonceStr': data.nonceStr, // 随机串
						'package': data.package, // 预支付id
						'signType': data.signType, // 微信签名方式
						'paySign': data.paySign // 微信签名
					},
					function(res) {
						// 使用以上方式判断前端返回,微信团队郑重提示：res.err_msg将在用户支付成功后返回ok，但并不保证它绝对可靠。
						if (res.err_msg === 'get_brand_wcpay_request:ok') {
							vm.$toast('支付成功')
							uni.redirectTo({
								url: '/pagesC/order/orderDetails?order_num=' +
									vm.oid
							});
						} else {
							vm.$toast('支付失败')
						}
					}
				)
			},
			getpayWallet(oid) {
				this.$fun.ajax.post('order/getPay', {
					oid
				}).then(res => {
					if (res.status == 1) {
						this.orderInfo = res.data
						this.PayPirce = ` ${res.data.pay[this.PayWay].name}￥${res.data.money}`
						if (res.data.deltime - (new Date().getTime() / 1000) > 0) {
							this.CountDown = res.data.deltime - (new Date().getTime() / 1000)
							this.CountDownData();
						} else {
							this.CountDown = null
						}
					}
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	.page {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: #f6f6f6;
	}

	/* 金额倒计时 */
	.price-count-down {
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 200rpx;
		background-color: #FFFFFF;

		.price {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100%;
			height: 80rpx;

			text {
				color: #310FFF;
				font-weight: bold;
			}

			.min {
				font-size: 32rpx;
			}

			.max {
				font-size: 52rpx;
			}
		}

		.count-down {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100%;
			height: 60rpx;

			.title {
				font-size: 24rpx;
				color: #222222;
			}

			.count {
				display: flex;
				align-items: center;
				margin-left: 20rpx;

				.time {
					padding: 4rpx 4rpx;
					background-color: #EEEEEE;
					font-size: 24rpx;
					color: #222222;
					border-radius: 2rpx;
				}

				.dot {
					margin: 0 10rpx;
					font-size: 24rpx;
					color: #222222;
				}
			}
		}
	}

	/* 支付方式 */
	.pay-way {
		width: 100%;
		background-color: #FFFFFF;
		margin-top: 220rpx;

		.pay-list {
			padding: 0 4%;

			.list {
				display: flex;
				align-items: center;
				justify-content: space-between;
				width: 100%;
				height: 100rpx;
				border-bottom: 2rpx solid #f6f6f6;

				.pay-type {
					display: flex;
					align-items: center;
					max-width: 90%;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;

					image {
						width: 40rpx;
						height: 40rpx;
					}

					text {
						font-size: 28rpx;
						color: #222222;
						margin-left: 20rpx;
					}
				}

				.check {
					display: flex;
					align-items: center;

					text {
						font-size: 42rpx;
						color: #C0C0C0;
					}

					.action {
						color: #310FFF;
					}
				}
			}
		}
	}

	/* 支付提交 */
	.pay-submit {
		position: absolute;
		left: 0;
		bottom: 0;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 100rpx;
		background-color: #FFFFFF;

		.submit {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 90%;
			height: 70%;
			background-color: #310FFF;
			color: #FFFFFF;
			border-radius: 100rpx;
			font-size: 26rpx;
		}
	}
</style>
