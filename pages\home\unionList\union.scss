.page{
	//position: absolute;
	//width: 100%;
	//height: 100%;
	//overflow-x: hidden;
	//overflow-y: auto;
	padding-bottom: 100rpx;
	background-color: #f6f6f6;
}
	/* 菜单导航 */
	.menu-nav{
		position: relative;
		width: 100%;
		// height: 300rpx;
		background: #FFFFFF;
		padding: 30rpx 0;
		margin:30rpx auto;
		margin-top: 0;
		.nav-list{
			white-space: nowrap; 
			// height: 270rpx;
			width: 100%;
			.nav{
				display: inline-block;
				display: flex;
				flex-direction: column;
				flex-wrap: wrap;
				justify-content: flex-start;
				// height: 270rpx;
			}
			.list{
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				width: 20%;
				height: 130rpx;
				margin-bottom: 20rpx;
				image{
					width: 75rpx;
					height: 75rpx;
					border-radius: 100%;
				}
				text{
					font-size: 26rpx;
					color: #363636;
					margin-top: 10rpx;
				}
			}
		}
		.indicator{
			position: absolute;
			left: 0;
			bottom: 0;
			display: flex;
			justify-content: center;
			align-items: center;
			width: 100%;
			height: 30rpx;
			.plan{
				position: relative;
				width: 100rpx;
				height: 8rpx;
				border-radius: 8rpx;
				background-color: #e1e1e1;
				.bar{
					position: absolute;
					width: 50%;
					height: 100%;
					border-radius: 6rpx;
					background-color: $base;
				}
			}
		}
	}

	.goods-list {
		padding: 0 25rpx;
		border-radius: 20rpx;
		margin: 20rpx 0;
		overflow: hidden;
		.list-view {
			float: left;
			width: 49%;
			height: 560rpx;
			background-color: #ffffff;
			border-radius: 20rpx;
			margin-right: 2%;
			margin-bottom: 20rpx;
			overflow: hidden;
			.thumb {
				width: 100%;
				//height: 300rpx;
				overflow: hidden;
				image {
                    height: 350rpx;
				}
			}
			.item {
				width: 100%;
				.title {
					// padding: 20rpx;
					text {
						width: 100%;
						color: #212121;
						font-size: 26rpx;
					}
				}
				
			}
		}
		.list-view:nth-child(2n) {
			margin-right: 0;
		}
		// 列表
		.list-li {
			display: flex;
			align-items: center;
			width: 100%;
			// height: 300rpx;
			padding: 30rpx 10rpx;
			background-color: #ffffff;
			border-radius: 20rpx;
			margin-bottom: 20rpx;
			.thumb {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 30%;
				height: 100%;
				image {
					width: 200rpx;
					height: 200rpx;
					border-radius: 10rpx;
				}
			}
			.item {
				display: flex;
				flex-direction: column;
				justify-content: center;
				width: 70%;
				// padding-bottom: 40rpx;
				// border-bottom: 2rpx solid #f6f6f6;
				.title {
					margin-bottom: 10rpx;
					font-size: 32rpx;
					font-family: PingFang SC-Heavy, PingFang SC;
					font-weight: 800;
					color: #282828;
					padding-left: 20rpx;
					overflow: hidden;
					text-overflow: ellipsis;
					display: -webkit-box;
					/* autoprefixer: off */
					-webkit-box-orient: vertical;
					/* autoprefixer: on */
					-webkit-line-clamp: 3;
					// webkitLineClamp
				}
				.info{
					font-size: 24rpx;
					font-family: PingFang SC-Medium, PingFang SC;
					font-weight: 500;
					color: #777777;
				}
				.website{
					font-size: 24rpx;
					font-family: PingFang SC-Medium, PingFang SC;
					font-weight: 500;
					color: #777777;
				}
			}
		}
	}