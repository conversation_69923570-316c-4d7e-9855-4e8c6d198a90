# 操作系统
.DS_Store
Thumbs.db
desktop.ini

# 编辑器和IDE
.idea/
.vscode/
*.sublime-project
*.sublime-workspace

# 依赖目录
node_modules/
dist/
unpackage/

# 编译文件
*.min.js
*.min.css

# 日志文件
npm-debug.log*
yarn-debug.log*
yarn-error.log*
debug.log

# 本地环境文件
.env.local
.env.*.local

# 构建文件
.hbuilderx/
manifest.json

# 临时文件
*.tmp
*.temp
*.swp
*~

# 其他
.git/
.svn/
.project
.settings/

# uni-app 特定
.uni-app/
.uni/

# 打包文件
*.zip
*.rar
*.7z

# 证书文件
*.pfx
*.p12
*.key
*.pem

# 缓存
.cache/
.temp/

# 测试覆盖率
coverage/ 