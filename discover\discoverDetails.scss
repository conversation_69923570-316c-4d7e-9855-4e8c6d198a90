	
	.page{
		//position: absolute;
		//width: 100%;
		//height: 100%;
		//overflow-x: hidden;
		//overflow-y: auto;
		padding-bottom: 100rpx;
		background-color: #f6f6f6;
	}
	/* banner */
		.banner{
			padding: 0 25rpx;
			height: 260rpx;
			margin-bottom: 30rpx;
			// margin: -200rpx auto 20rpx;
			border-radius: 10rpx;
			overflow: hidden;
			.screen-swiper{
				height: 100%;
				min-height: 100% !important;
				image{
					height: 260rpx;
					border-radius: 10rpx;
				}
			}
		}
		/* 菜单导航 */
		.menu-nav{
			position: relative;
			width: 100%;
			// height: 300rpx;
			background: #FFFFFF;
			padding: 30rpx 0;
			margin:30rpx auto;
			.nav-list{
				white-space: nowrap; 
				height: 270rpx;
				width: 100%;
				.nav{
					display: inline-block;
					display: flex;
					flex-direction: column;
					flex-wrap: wrap;
					justify-content: flex-start;
					height: 270rpx;
				}
				.list{
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-items: center;
					width: 20%;
					height: 130rpx;
					margin-bottom: 20rpx;
					image{
						width: 75rpx;
						height: 75rpx;
						border-radius: 100%;
					}
					text{
						font-size: 26rpx;
						color: #363636;
						margin-top: 10rpx;
					}
				}
			}
			.indicator{
				position: absolute;
				left: 0;
				bottom: 0;
				display: flex;
				justify-content: center;
				align-items: center;
				width: 100%;
				height: 30rpx;
				.plan{
					position: relative;
					width: 100rpx;
					height: 8rpx;
					border-radius: 8rpx;
					background-color: #e1e1e1;
					.bar{
						position: absolute;
						width: 50%;
						height: 100%;
						border-radius: 6rpx;
						background-color: $base;
					}
				}
			}
		}
	/* 文章搜索 */
	.article-search{
		position: fixed;
		left: 0;
		top: 0;
		z-index: 10;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		height: 100rpx;
		/* #ifdef APP-PLUS */
		height: calc(120rpx + var(--status-bar-height));
		/* #endif */
		padding: 0 4%;
		background-color: #FFFFFF;
		border-radius: 0 0 20rpx 20rpx;
		.icon{
			display: flex;
			align-items: center;
			justify-content: space-around;
			width: 10%;
			height: 100%;
			text{
				color: #333333;
				font-size: 52rpx;
			}
		}
		.search{
			display: flex;
			align-items: center;
			padding: 0 3%;
			width: 90%;
			height: 60rpx;
			background-color: #f6f6f6;
			border-radius: 60rpx;
			/* #ifdef APP-PLUS */
			margin-top: var(--status-bar-height);
			/* #endif */
			.iconfont{
				font-size: 28rpx;
				color: #C0C0C0;
			}
			input{
				width: 90%;
				height: 100%;
				color: #212121;
				font-size: 24rpx;
				margin-left: 10rpx;
			}
		}
	}
	/* 文章数据  */
	.article-data{
		width: 100%;
		// margin-top: 120rpx;
		/* #ifdef APP-PLUS */
		// margin-top: calc(150rpx + var(--status-bar-height));
		/* #endif */
		.article-list{
			padding: 0 4%;
			margin: 20rpx auto;
			.list{
				display: flex;
				align-items: center;
				width: 100%;
				height: 300rpx;
				background-color: #FFFFFF;
				border-radius: 20rpx;
				margin-bottom: 20rpx;
				.item{
					display: flex;
					flex-direction: column;
					justify-content: space-between;
					width: 70%;
					height: 100%;
					padding: 0 4%;
					.title{
						display: flex;
						padding:20rpx;
						text{
							color: #212121;
							font-size: 28rpx;
						}
					}
					.find-collect{
						display: flex;
						align-items: center;
						padding: 0 20rpx;
						height: 80rpx;
						.find{
							display: flex;
							align-items: center;
							margin-right: 20rpx;
							.iconfont{
								font-size: 34rpx;
								margin-right: 10rpx;
							}
							text{
								color: #C0C0C0;
								font-size: 28rpx;
							}
						}
					}
				}
				.thumb{
					padding: 20rpx;
					image{
						width: 200rpx;
						height: 200rpx;
					}
				}
			}
		}
	}
		.goods-list {
			padding: 0 25rpx;
			border-radius: 20rpx;
			overflow: hidden;
			.list-view {
				float: left;
				width: 49%;
				height: 560rpx;
				background-color: #ffffff;
				border-radius: 20rpx;
				margin-right: 2%;
				margin-bottom: 20rpx;
				overflow: hidden;
				.thumb {
					width: 100%;
					//height: 300rpx;
					overflow: hidden;
					image {
	                    height: 350rpx;
					}
				}
				.item {
					width: 100%;
					.title {
						padding: 20rpx;
						text {
							width: 100%;
							color: #212121;
							font-size: 26rpx;
						}
					}
					.price {
						padding: 0 20rpx;
						.retail-price {
							display: flex;
							align-items: flex-end;
							width: 100%;
							height: 40rpx;
							.min {
								display: inline-block;
								font-size: 24rpx;
								color: $base;
								font-weight: bold;
								transform: scale(0.7);
							}
							.max {
								font-size: 28rpx;
								color: $base;
								font-weight: bold;
							}
							.tag {
								position: relative;
								background-color: $base;
								border-radius: 4rpx;
								margin-left: 10rpx;
								text {
									display: inline-block;
									color: #ffffff;
									font-size: 24rpx;
									transform: scale(0.7);
								}
							}
							.tag:before {
								position: absolute;
								left: -6rpx;
								top: 0;
								content: '';
								width: 0;
								height: 0;
								border-top: 0rpx solid transparent;
								border-right: 10rpx solid $base;
								border-bottom: 6rpx solid transparent;
							}
						}
						.vip-price {
							display: flex;
							align-items: flex-end;
							width: 100%;
							height: 40rpx;
							.min {
								display: inline-block;
								font-size: 24rpx;
								color: #212121;
							}
							.max {
								font-size: 24rpx;
								color: #212121;
							}
						}
					}
				}
			}
			.list-view:nth-child(2n) {
				margin-right: 0;
			}
			// 列表
			.list-li {
				display: flex;
				align-items: center;
				width: 100%;
				// height: 300rpx;
				padding: 30rpx 10rpx;
				background-color: #ffffff;
				margin-bottom: 20rpx;
				.thumb {
					display: flex;
					align-items: center;
					justify-content: center;
					width: 30%;
					height: 100%;
					image {
						width: 200rpx;
						height: 200rpx;
						border-radius: 10rpx;
					}
				}
				.item {
					display: flex;
					flex-direction: column;
					justify-content: center;
					width: 70%;
					height: 100%;
					border-bottom: 2rpx solid #f6f6f6;
					.title {
						padding: 20rpx;
						text {
							width: 100%;
							color: #212121;
							font-size: 26rpx;
						}
					}
					.price {
						padding: 0 20rpx;
						.retail-price {
							display: flex;
							align-items: flex-end;
							width: 100%;
							height: 40rpx;
							.min {
								display: inline-block;
								font-size: 24rpx;
								color: $base;
								font-weight: bold;
								transform: scale(0.7);
							}
							.max {
								font-size: 28rpx;
								color: $base;
								font-weight: bold;
							}
							.tag {
								position: relative;
								background-color: $base;
								border-radius: 4rpx;
								margin-left: 10rpx;
								text {
									display: inline-block;
									color: #ffffff;
									font-size: 24rpx;
									transform: scale(0.7);
								}
							}
							.tag:before {
								position: absolute;
								left: -6rpx;
								top: 0;
								content: '';
								width: 0;
								height: 0;
								border-top: 0rpx solid transparent;
								border-right: 10rpx solid $base;
								border-bottom: 6rpx solid transparent;
							}
						}
						.vip-price {
							display: flex;
							align-items: flex-end;
							width: 100%;
							height: 40rpx;
							.min {
								display: inline-block;
								font-size: 24rpx;
								color: #212121;
							}
							.max {
								font-size: 24rpx;
								color: #212121;
							}
						}
					}
				}
			}
		}
	.store_info {
		padding: 40rpx 20rpx;
		background: #FFFFFF;
		border-radius: 20rpx;
		margin: 0 30rpx;
		padding-bottom: 10rpx;

		.info_item {
			display: flex;
			align-items: center;
			margin-bottom: 46rpx;

			image {
				width: 34rpx;
				height: 34rpx;
				margin: 0 20rpx;
			}

			.t {
				color: #333333;
				font-size: 30rpx;
				font-weight: bold;
				max-width: 560rpx;
				overflow: hidden;
				text-overflow: ellipsis;
				display: -webkit-box;
				-webkit-line-clamp: 2;
				-webkit-box-orient: vertical;
			}

			.i {
				color: #666666;
				font-size: 26rpx;
				font-weight: 500
			}
		}
	}
	
	.submit-btn{
		display: flex;
		align-items: center;
		justify-content: center;
		width: 94%;
		height: 80rpx;
		margin: 30rpx auto;
		background: linear-gradient(to right,$base,$change-clor);
		border-radius: 80rpx;
		box-shadow: 0 10rpx 10rpx $base;
		text{
			color: #FFFFFF;
			font-size: 28rpx;
		}
	}