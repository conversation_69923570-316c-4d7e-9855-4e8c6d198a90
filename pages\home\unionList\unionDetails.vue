<template>
	<view>
		<view class="goods-list">
			<view :class="'list-li'"  >
				<view class="thumb" >
					<image :src="$fun.imgUrl(unionDetails.logoimage)"></image>
				</view>
				<view class="item">
					<view class="title">
						{{unionDetails.name}}
					</view>
					<view class="title info">
						经营范围：{{unionDetails.title}}
					</view>
					<view class="title website">
						公司网址：{{unionDetails.url}}
					</view>
				</view>
			</view>
			<view class="tagBox">
				<view class="tag" v-for="(item,index) in unionDetails.zzimages" :key="index" @click="$fun.lookImg($fun.imgUrl(item))">
					<u-tag bgColor="rgba(245, 64, 54, .3)" color="#F54036" size="mini" borderColor="transparent"
						:text="index" mode="light" />
				</view>
			</view>
			<view class="infoBox">
				<view class="textInfo">姓名：{{unionDetails.username}}</view>
				<view class="textInfo">微信号：{{unionDetails.wechat}}</view>
				<view class="textInfo">电话：{{unionDetails.mobile}}</view>
			</view>
			<view class="infoBox" style="margin-top: 12rpx;">
				<view class="textInfo">地址：{{unionDetails.address}}</view>
			</view>
		</view>
		<view class="unionInfo">
			<view class="tabList">
				<view class="tabItem"  @click="changeType(1)">
					<view :class="type==1?'active':''" style="background: transparent;">
						企业文化
					</view>
					<view :class="type==1?'b active':'b'">

					</view>
				</view>
				<view class="b">
					<view>

					</view>
				</view>
				<view class="tabItem" @click="changeType(2)">
					<view :class="type==2?'active':''" style="background: transparent;"> 企业照片

					</view>
					<view :class="type==2?'b active':'b'">

					</view>
				</view>
			</view>
			<view class="htmlInfo" v-if="type==1">
				<view v-html="unionDetails.content">
					
				</view>
			</view>
			<view class="imgInfo" v-if="type==2">
				<image v-for="(item,index) in  $fun.splitImg(unionDetails.images)" :key="index" @click="$fun.lookImg($fun.imgUrl(item))" :src="$fun.imgUrl(item)" mode=""></image>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				type: 1,
				unionDetails:{}
			};
		},
		onLoad(option) {
			this.$fun.ajax.post('union/content',{uid:option.id}).then(res=>{
				this.unionDetails= res.data
			})
		},
		methods:{
			changeType(type){
				this.type = type
			}
		}
	}
</script>

<style lang="scss">
	.goods-list {
		padding: 0 25rpx;
		box-sizing: border-box;
		// border-radius: 20rpx;
		overflow: hidden;
		background-color: #ffffff;

		.list-view {
			float: left;
			width: 49%;
			height: 560rpx;
			border-radius: 20rpx;
			margin-right: 2%;
			margin-bottom: 20rpx;
			overflow: hidden;

			.thumb {
				width: 100%;
				//height: 300rpx;
				overflow: hidden;

				image {
					height: 350rpx;
				}
			}

			.item {
				width: 100%;

				.title {

					// padding: 20rpx;
					text {
						width: 100%;
						color: #212121;
						font-size: 26rpx;
					}
				}

			}
		}

		.list-view:nth-child(2n) {
			margin-right: 0;
		}

		// 列表
		.list-li {
			display: flex;
			align-items: center;
			width: 100%;
			// height: 300rpx;
			padding: 30rpx 0rpx;
			padding-bottom: 0;
			background-color: #ffffff;
			margin-bottom: 20rpx;

			.thumb {
				display: flex;
				align-items: center;
				// justify-content: center;
				width: 30%;
				height: 100%;

				image {
					width: 200rpx;
					height: 200rpx;
					border-radius: 10rpx;
				}
			}

			.item {
				display: flex;
				flex-direction: column;
				justify-content: center;
				width: 70%;
				// padding-bottom: 40rpx;
				// border-bottom: 2rpx solid #f6f6f6;

				.title {
					margin-bottom: 10rpx;
					font-size: 32rpx;
					font-family: PingFang SC-Heavy, PingFang SC;
					font-weight: 800;
					color: #282828;
					padding-left: 20rpx;
				}

				.info {
					font-size: 24rpx;
					font-family: PingFang SC-Medium, PingFang SC;
					font-weight: 500;
					color: #777777;
				}

				.website {
					font-size: 24rpx;
					font-family: PingFang SC-Medium, PingFang SC;
					font-weight: 500;
					color: #777777;
				}
			}
		}

		.tagBox {
			display: flex;
			flex-wrap: wrap;

			.tag {
				margin-right: 10rpx;
			}
		}

		.infoBox {
			display: flex;
			flex-wrap: wrap;
			justify-content: flex-start;
			margin: 23rpx 0;

			.textInfo {
				margin-right: 38rpx;
				font-size: 24rpx;
				font-family: PingFang SC-Medium, PingFang SC;
				font-weight: 500;
				color: #777777;
			}

			.textInfo:nth-child(3) {
				margin-right: 0;
			}
		}
	}

	.unionInfo {
		margin: 24rpx;
		padding: 30rpx 38rpx;
		background: #ffffff;
		border-radius: 8rpx;

		.tabList {
			display: flex;

			.tabItem {
				font-size: 30rpx;
				font-family: PingFang SC-Medium, PingFang SC;
				font-weight: 500;
				color: #333333;
				width: 49%;
				text-align: center;
				display: flex;
				align-items: center;
				justify-content: center;
				flex-direction: column;

				.b {
					margin-top: 10rpx;
					width: 49rpx;
					height: 3rpx;
					background: transparent;
					text-align: center;
				}

				.active {
					background: #F54036;
					color: #F54036;
				}
			}

			.b {
				display: flex;
				align-items: center;
				width: 2%;

				view {
					width: 2rpx;
					height: 24rpx;
					background: #C4C4C4;
				}
			}
		}

		.htmlInfo {
			margin-top: 35rpx;
		}
		.imgInfo {
			margin-top: 35rpx;
			display: flex;
			flex-wrap: wrap;
			justify-content: space-between;
			image{
				width: 300rpx;
				height: 300rpx;
				background: #FFFFFF;
				border-radius: 12rpx ;
				margin-bottom: 10rpx;
			}
		}
	}
</style>
