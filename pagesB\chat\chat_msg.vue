<template>
	<view class="chat_msg">
		<scroll-view scroll-y="true" class="scroll-view" :scroll-top="scrollTop">
			<view class="content">
				<block v-for="(item,index) in msgList" :key="index">

					<view class="user_msg_l" :id="'msg_'+item.id" @longpress="showToolTip($event,index)"
						v-if="userInfo.id!=item.fromid">
						<view class="avatar"
							@click="$fun.jump(`./add_friend/add_confirm?account=${item.fromid}&type=1`)">
							<u-avatar :src="$fun.imgUrl(item.avatar)" mode="circle"></u-avatar>
						</view>
						<view class="msg auto-wrap" v-if="item.type==1">
							{{item.value}}
							<!-- --{{userInfo.id}}--{{item.fromid}} -->
						</view>
						<view class="msg auto-wrap" v-if="item.type==2">
							<image :src="$fun.imgUrl(item.value)" v-if="$fun.isAssetTypeAnImage(item.value)"
								mode="widthFix" @click="$fun.lookImg($fun.imgUrl(item.value))"></image>
						</view>
						<view class="msg auto-wrap" v-if="item.type==3">
							<image v-if="item.value.indexOf('xls')!=-1" src="/static/fileType/xls.png"
								@click="lookfile(item,'xls')" mode="widthFix">
							</image>
							<image v-else-if="item.value.indexOf('doc')!=-1" src="/static/fileType/docx.png"
								@click="lookfile(item,'doc')" mode="widthFix"></image>
							<image v-else-if="item.value.indexOf('txt')!=-1" src="/static/fileType/txt.png"
								@click="lookfile(item,'txt')" mode="widthFix"></image>
							<image v-else-if="item.value.indexOf('ppt')!=-1" src="/static/fileType/ppt.png"
								@click="lookfile(item,'ppt')" mode="widthFix"></image>
							<image v-else-if="item.value.indexOf('pdf')!=-1" src="/static/fileType/pdf.png"
								@click="lookfile(item,'pdf')" mode="widthFix"></image>
						</view>
					</view>
					<view class="user_msg_r" :id="'msg_'+item.id" @longpress="showToolTip($event,index)" v-else>
						<view class="msg auto-wrap" v-if="item.type==1">
							{{item.value}}
						</view>
						<view class="msg auto-wrap" v-if="item.type==2">
							<image :src="$fun.imgUrl(item.value)" mode="widthFix"
								@click="$fun.lookImg($fun.imgUrl(item.value))"></image>
						</view>
						<view class="msg auto-wrap" v-if="item.type==3">
							<image v-if="item.value.indexOf('xls')!=-1" src="/static/fileType/xls.png"
								@click="lookfile(item,'xls')" mode="widthFix">
							</image>
							<image v-else-if="item.value.indexOf('doc')!=-1" src="/static/fileType/docx.png"
								@click="lookfile(item,'doc')" mode="widthFix"></image>
							<image v-else-if="item.value.indexOf('txt')!=-1" src="/static/fileType/txt.png"
								@click="lookfile(item,'txt')" mode="widthFix"></image>
							<image v-else-if="item.value.indexOf('ppt')!=-1" src="/static/fileType/ppt.png"
								@click="lookfile(item,'ppt')" mode="widthFix"></image>
							<image v-else-if="item.value.indexOf('pdf')!=-1" src="/static/fileType/pdf.png"
								@click="lookfile(item,'pdf')" mode="widthFix"></image>
						</view>
						<view class="avatar"
							@click="$fun.jump(`./add_friend/add_confirm?account=${item.fromid}&type=1`)">
							<u-avatar :src="$fun.imgUrl(item.avatar)" mode="circle"></u-avatar>
						</view>
					</view>
				</block>
				<block v-for="(item,index) in nmsgList" :key="index">

					<view class="user_msg_l" :id="'msg_'+item.id" @longpress="showToolTip($event,index)"
						v-if="userInfo.id!=item.fromid">
						<view class="avatar"
							@click="$fun.jump(`./add_friend/add_confirm?account=${item.fromid}&type=1`)">
							<u-avatar :src="$fun.imgUrl(item.avatar)" mode="circle"></u-avatar>
						</view>
						<view class="msg auto-wrap" v-if="item.type==1">
							{{item.value}}
						</view>
						<view class="msg auto-wrap" v-if="item.type==2">
							<image :src="$fun.imgUrl(item.value)" mode="widthFix"
								@click="$fun.lookImg($fun.imgUrl(item.value))"></image>
						</view>
						<view class="msg auto-wrap" v-if="item.type==3">
							<image v-if="item.value.indexOf('xls')!=-1" src="/static/fileType/xls.png"
								@click="lookfile(item,'xls')" mode="widthFix">
							</image>
							<image v-else-if="item.value.indexOf('doc')!=-1" src="/static/fileType/docx.png"
								@click="lookfile(item,'doc')" mode="widthFix"></image>
							<image v-else-if="item.value.indexOf('txt')!=-1" src="/static/fileType/txt.png"
								@click="lookfile(item,'txt')" mode="widthFix"></image>
							<image v-else-if="item.value.indexOf('ppt')!=-1" src="/static/fileType/ppt.png"
								@click="lookfile(item,'ppt')" mode="widthFix"></image>
							<image v-else-if="item.value.indexOf('pdf')!=-1" src="/static/fileType/pdf.png"
								@click="lookfile(item,'pdf')" mode="widthFix"></image>
						</view>
					</view>
					<view class="user_msg_r" :id="'msg_'+item.id" @longpress="showToolTip($event,index)" v-else>
						<view class="msg auto-wrap" v-if="item.type==1">
							{{item.value}}
						</view>
						<view class="msg auto-wrap" v-if="item.type==2">
							<image :src="$fun.imgUrl(item.value)" mode="widthFix"
								@click="$fun.lookImg($fun.imgUrl(item.value))"></image>
						</view>
						<view class="msg auto-wrap" v-if="item.type==3">
							<image v-if="item.value.indexOf('xls')!=-1" src="/static/fileType/xls.png"
								@click="lookfile(item,'xls')" mode="widthFix">
							</image>
							<image v-else-if="item.value.indexOf('doc')!=-1" src="/static/fileType/docx.png"
								@click="lookfile(item,'doc')" mode="widthFix"></image>
							<image v-else-if="item.value.indexOf('txt')!=-1" src="/static/fileType/txt.png"
								@click="lookfile(item,'txt')" mode="widthFix"></image>
							<image v-else-if="item.value.indexOf('ppt')!=-1" src="/static/fileType/ppt.png"
								@click="lookfile(item,'ppt')" mode="widthFix"></image>
							<image v-else-if="item.value.indexOf('pdf')!=-1" src="/static/fileType/pdf.png"
								@click="lookfile(item,'pdf')" mode="widthFix"></image>
						</view>
						<view class="avatar"
							@click="$fun.jump(`./add_friend/add_confirm?account=${item.fromid}&type=1`)">
							<u-avatar :src="$fun.imgUrl(item.avatar)" mode="circle"></u-avatar>
						</view>
					</view>
				</block>
			</view>
		</scroll-view>
		<view class="user_input">
			<view class="input">
				<u-input :custom-style="customStyle" @confirm="sendClick({msg:'发送消息'},1)" v-model="keyword"
					:type="'text'" :border="true" />
			</view>
			<view class="photo">
				<u-icon name="photo" @click="uploadImage()" size="70"></u-icon>
			</view>
			<view class="send_btn">
				<u-button type="success" size="mini" @click="sendClick({msg:'发送消息'},1)">发送</u-button>
			</view>
		</view>
		<!-- 长按菜单 -->
		<chunLei-popups v-model="toolTipFlag" :popData="toolTipData" @tapPopup="tapPopup" :x="toolTipX" :y="toolTipY"
			direction="row" theme="dark" :dynamic="true">
		</chunLei-popups>
	</view>
</template>

<script>
	import chunLeiPopups from "../components/chunLei-popups/chunLei-popups.vue";
	export default {
		components: {
			chunLeiPopups,
		},
		data() {
			return {
				keyword: '',
				keywordImg: '',
				toolTipFlag: false,
				toolTipX: 0,
				toolTipY: 0,
				toolTipData: [],
				msgList: [],
				nmsgList: [],
				msgInfo: {},
				customStyle: {
					background: '#FFFFFF'
				},
				userInfo: {},
				scrollTop: 0,
				old: {
					scrollTop: 0,
				},
				currentChatGroupId: ''
			}
		},
		onLoad(option) {
			this.currentChatGroupId = option.id;
			this.pushNewMsg();
		},
		onShow() {
			this.userInfo = uni.getStorageSync('userinfo');
			this.getData()
		},
		onNavigationBarButtonTap(e) {
			this.$fun.jump(`./chat_d?id=${this.currentChatGroupId}&type=${this.msgInfo.type}`)
		},
		methods: {
			pushNewMsg(item) {
				uni.$on('chat', (data) => {
					let json = JSON.parse(data);
					let value = '';
					if (json.value) {
						value = JSON.parse(json.value);
					}
					if (value.chatUserId == this.currentChatGroupId) {
						this.nmsgList.push(value);
						setTimeout(() => {
							this.goBot()
						}, 500);
					}
				});
			},
			lookfile(item, fileType) {
				// #ifdef APP-PLUS
				uni.openDocument({
					filePath: this.$fun.imgUrl(item.value),
					fileType,
					success: function(res) {
						console.log('打开文档成功');
					},
					fail: function(err) {
						console.log('打开文档失败', err);
					}
				});
				// #endif
				// #ifdef H5
				if (fileType == 'doc') {
					let file = this.$fun.imgUrl(item.value);
					const url = `https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(file)}`
					window.open(url) //新建窗口打开链接预览
				} else {
					uni.openDocument({
						filePath: this.$fun.imgUrl(item.value),
						fileType,
						success: function(res) {
							console.log('打开文档成功');
						},
						fail: function(err) {
							console.log('打开文档失败', err);
						}
					});
				}
				// #endif
			},
			// 上传头像
			uploadImage() {
				// 从相册选择图片
				const _this = this;
				uni.chooseFile({
					count: 1,
					success: function(res) {
						_this.handleUploadFile(res.tempFilePaths);
					}
				});
			},
			// 上传头像
			handleUploadFile(data) {
				const _this = this;
				const filePath = data.path || data[0];
				this.$fun.uploadPic(
					filePath
				).then(res => {
					if (res.status == 1) {
						this.keywordImg = res.data.url;
						this.sendClick({
							msg: '发送图片'
						}, 2)
					}
				})
			},
			goBot() {
				let itemsHeight = 0; //所有的item加起来的高度
				let scrollHeight = 0; //scroll-view的高度
				let itemsElement = uni.createSelectorQuery().select('.content');
				itemsElement.boundingClientRect(data => {
					itemsHeight = data.height
				}).exec();
				let scrollElement = uni.createSelectorQuery().select('.scroll-view');
				scrollElement.boundingClientRect(data => {
					scrollHeight = data.height
				}).exec();
				if (itemsHeight > scrollHeight) {
					this.scrollTop = this.old.scrollTop
					this.$nextTick(function() {
						this.scrollTop = 9999
					});
				}
			},
			getData() {
				this.$fun.ajax.post('chat/getChat', {
					id: this.currentChatGroupId
				}).then(res => {
					if (res.status == 1) {
						this.msgList = res.data.chat.reverse();
						this.msgInfo = res.data.info
						uni.setNavigationBarTitle({
							title: res.data.info.name
						})
						setTimeout(() => {
							this.goBot()
						}, 100)
					}
				})
			},
			sendClick(item, type = 1) {
				let param = {
					id: this.currentChatGroupId,
					type: type,
				};
				if (type == 1) {
					param.value = this.keyword
					if (param.value == '') {
						this.$fun.msg('请输入消息信息');
						return;
					}
				}
				if (type == 2) {
					param.value = this.keywordImg;
					var index = param.value.lastIndexOf(".");
					var ext = param.value.substr(index + 1);
					let tplist = [
						'png', 'jpg', 'jpeg', 'bmp', 'gif', 'webp', 'psd', 'svg', 'tiff'
					];
					if (tplist.indexOf(ext) == -1) {
						param.type = 3
					}
					if (param.value == '') {
						this.$fun.msg('请选择发送的图片或文件');
						return;
					}
				}
				this.$fun.ajax.post(`chat/setChat`, param).then(res => {
					if (res.status == 1) {
						this.keywordImg = '';
						this.keyword = '';
					}
				})
			},
			tapPopup(e) {
				this.$fun.msg(e.title)
			},
			// 长按菜单
			//操作项
			showToolTip(e, index) {
				console.log("showToolTip", index);
				if (this.forbidFlag) {
					return;
				}
				this.toolTipX = e.touches[0].clientX;
				this.toolTipY = e.touches[0].clientY;
				this.toolTipFlag = !this.toolTipFlag;
				this.toolTipData = [{
						id: 1,
						icon: '/static/chat/copy.png',
						title: '复制',
						disabled: false
					},
					{
						id: 2,
						icon: '/static/chat/zhuanfa.png',
						title: '转发',
						disabled: false
					},
					{
						id: 4,
						icon: '/static/chat/checkbox.png',
						title: '多选',
						disabled: false
					},
					{
						id: 5,
						icon: '/static/chat/revert.png',
						title: '撤回',
						disabled: false
					},

				]

			},
		}
	}
</script>

<style lang="scss">
	page {
		background: #FFFFFF;

		.chat_msg {
			.scroll-view {
				white-space: nowrap;
				height: calc(100vh - 240rpx);
				padding: 32rpx;
				box-sizing: border-box;

				.content {
					.user_msg_l {
						display: flex;
						margin-bottom: 20rpx;

						.avatar {}

						.msg {
							margin-top: 48rpx;
							margin-left: 16rpx;
							max-width: calc(100% - 200rpx);
							width: fit-content;
							padding: 32rpx;
							background: #F2F2F2;
							border-radius: 0rpx 47rpx 47rpx 47rpx;

							image {
								width: 100rpx;
							}
						}
					}

					.user_msg_r {
						margin-bottom: 20rpx;
						display: flex;
						justify-content: flex-end;

						.avatar {}

						.msg {
							margin-top: 48rpx;
							margin-right: 16rpx;
							max-width: calc(100% - 200rpx);
							width: fit-content;
							padding: 32rpx;
							background: #310FFF;
							border-radius: 47rpx 0rpx 47rpx 47rpx;

							image {
								width: 100rpx;
							}
						}
					}
				}
			}

			.user_input {
				padding: 32rpx;
				box-sizing: border-box;
				position: fixed;
				bottom: 0;
				left: 0;
				width: 100vw;
				height: 172rpx;
				background: #F2F2F2;
				border-radius: 40rpx 40rpx 0rpx 0rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.input {
					width: calc(100% - 200rpx);
					background: #FFFFFF;
				}

				.send_btn {}
			}
		}

		.auto-wrap {
			white-space: normal;
			word-break: break-all;
			word-wrap: break-word;

			image {
				width: 150rpx;
			}
		}
	}
</style>