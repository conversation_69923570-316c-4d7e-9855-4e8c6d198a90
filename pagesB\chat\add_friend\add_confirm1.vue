<template>
	<view class="add_confirm">

		<view class="chat_list" v-if="type==1">
			<view class="title">
				发送添加好友申请
			</view>
			<view class="add_confirm_info1">
				<u-input v-model="memo" placeholder="请输入好友申请语" :type="'textarea'" :border="false" />
			</view>
		</view>
		<view class="chat_list">
			<view class="title">
				备注
			</view>
			<view class="add_confirm_info1">
				<u-input v-model="frommemo" placeholder="设置备注" :type="'text'" :border="false" />
			</view>
		</view>
		<view v-if="type==1" class="confirm_btn1" @click="addFirend()">
			添加好友
		</view>
		<view v-if="type==2" class="confirm_btn1" @click="firendAgree()">
			完成
		</view>
		<view v-if="type==3||type==4" class="confirm_btn1" @click="setBz()">
			完成
		</view>


	</view>
</template>

<script>
	export default {
		data() {
			return {
				type: 1,
				account: '',
				frommemo: '',
				memo: '',
				id: '',
				isClick:false
			};
		},
		onLoad(option) {
			this.type = option.type;
			this.id = option.id ? option.id : '';
			this.frommemo = option.frommemo ? option.frommemo : '';
			this.account = option.account;
			this.getuser();
			uni.setNavigationBarTitle({
				title: option.type == 1 ? '添加好友' : option.type == 2 ? '验证好友': option.type == 3 ? '备注' : '群昵称'
			})
		},
		methods: {
			addFirend() {
				let prams = {
					account: this.account,
					frommemo: this.frommemo,
					memo: this.memo,
				};
				if (!this.account) {
					this.$fun.msg('系统错误!');
					return
				}
				if(this.isClick){
					return
				}
				this.isClick = true;
				this.$fun.ajax.post('chat/addUser', prams).then(res => {
					if (res.status == 1) {
						this.$fun.msg(res.msg);
						this.$fun.jump('', 4, 1200);
						setTimeout(()=>{
							this.isClick = false;
						},1200)
					}else{
						this.isClick = false;
					}
				})
			},
			getuser() {
				if (this.type == 1) {
					this.memo = '我是' + uni.getStorageSync('userinfo').username
				}
				return
				this.$fun.ajax.post('chat/getUser', {
					account: this.keyword
				}).then(res => {
					if (res.status == 1) {
						if (res.data) {
							// this.frommemo = 
						}
					}
				})
			},
			firendAgree() {
				if(this.isClick){
					return
				}
				this.isClick = true;
				this.$fun.ajax.post('chat/confirmUser', {
					id: this.id,
					tomemo: this.frommemo,
					status: 1
				}).then(res => {
					if (res.status == 1) {
						this.$fun.msg(res.msg);
						this.$fun.jump('', 4, 1200);
						setTimeout(()=>{
							this.isClick = false;
						},1200)
					}else{
						this.isClick = false;
					}
				})
			},
			setBz() {
				if(this.isClick){
					return
				}
				this.isClick = true;
				this.$fun.ajax.post('chat/setChatInfo', {
					id: this.id,
					username: this.frommemo,
				}).then(res => {
					if (res.status == 1) {
						this.$fun.msg(res.msg);
						this.$fun.jump('', 4, 1200);
						setTimeout(()=>{
							this.isClick = false;
						},1200)
					}else{
						this.isClick = false;
					}
				})
			}
		},
		onNavigationBarButtonTap(e) {
			if (e.text == '+') {
				if (!this.show) {
					this.show = true;
					this.$refs.popup.open('top');
				} else {
					this.show = false;
					this.$refs.popup.close()
				}
			}
		},
	}
</script>

<style lang="scss">
	page {
		background: #FFFFFF;

		.chat_search {
			margin: 32rpx;
		}

		.chat_list {
			padding: 18rpx 32rpx;

			.title {
				margin-bottom: 20rpx;
				font-size: 23rpx;
			}

			.add_confirm_info1 {

				margin-bottom: 10rpx;

				padding: 28rpx;
				background: #F6F6F6;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				font-size: 28rpx;
				color: #666666;
				line-height: 40rpx;
				text-align: left;
				font-style: normal;
				text-transform: none;
				border-radius: 16rpx 16rpx 16rpx 16rpx;
			}
		}

		.confirm_btn1 {
			width: 352rpx;
			height: 94rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			background: #310FFF;
			position: absolute;
			bottom: 160rpx;
			left: 0;
			right: 0;
			margin: auto;
			font-family: Source Han Sans CN, Source Han Sans CN;
			font-weight: 500;
			font-size: 32rpx;
			color: #FFFFFF;
			line-height: 38rpx;
			text-align: center;
			font-style: normal;
			text-transform: none;
			border-radius: 16rpx 16rpx 16rpx 16rpx;
		}

		.chat_san {
			width: 50rpx;
			height: 50rpx;
			position: fixed;
			top: 15rpx;
			right: 30rpx;
			background: #FFFFFF;
			transform: rotate(45deg);
		}



		// 添加好友
		.chat_add_box {
			position: fixed;
			top: 20rpx;
			right: 20rpx;
			display: flex;
			flex-direction: column;
			padding: 46rpx 50rpx;
			background: #FFFFFF;
			border-radius: 12rpx 12rpx 12rpx 12rpx;

			.add_item {
				display: flex;
				margin-bottom: 40rpx;
				align-items: center;

				.add_title {
					margin-left: 10rpx;
					font-weight: 400;
					font-size: 28rpx;
					color: #333333;
					line-height: 33rpx;
					text-align: center;
					font-style: normal;
					text-transform: none;
				}
			}

			.add_item:nth-child(3) {
				margin-bottom: 0rpx;
			}
		}
	}
</style>