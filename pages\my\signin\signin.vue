/**
* @Description:  太和永康 



* 
*/
<template>
	<view class="index_class">

		<youlanSignIn type="sign" :singArr="signInfo.days" @change="dosign" />
			<div class="steps">
				<div class="ad_til">连续签到有礼</div>
				<div class="ad_pil" v-html="signInfo.rule"></div>
			</div>

		</view>
	</view>
</template>

<script>
	import youlanSignIn from '@/components/youlan-SignIn/youlan-SignIn.vue'
	export default {
		data() {
			return {
				height: "",
				show1: true,
				// sing_arr:[], // 当前月份签到的天数 数组
				data: "", // 当前点击的 天数,
				signInfo: {
					days:[]
				}, // 签到信息
				valueStatus: 0, // 更新页面状态 防止页面卡死
			}
		},
		components: {
			youlanSignIn
		},
		onShow() {
			this.getSignInfo()
		},
		methods: {


			changeDate1: function(e, data, all) {
				console.log(data)
				if (data == undefined) {
					return false
				}
				this.data = data

				var countArr = e.split('-')
				var newArr = data.split('-')
				if (e == data) {

					let time = this.data.split('-')
					let dataObj = {
						type: 0,
						date: time[time.length - 1],
					}
					this.dosign(dataObj)
				}


			},
			typesignin: function(num) {
				let data = {
					type: 1,
					days: num,
				}
				this.dosign(data)
			},
			getSignInfo() {

				// this.$http.getSignInfo().then(res => {

				// 	if (res.status === 200) {

				// 		this.signInfo = res.data
				// 		this.statusMath(this)
				// 	}
				// })
				this.$fun.jump(`/pages/home/<USER>
				return;
				this.$fun.ajax.post('/Leesign/index', {}).then(res => {
					if (res.status == 1) {
						// console.log(res)
						this.signInfo = res.data
						// this.statusMath(this)
					}
				})
			},
			// 点击签到
			dosign: function(data) {
				this.$fun.ajax.post('/Leesign/sign', {}).then(res => {
					if (res.status == 1) {
						this.$fun.msg(res.msg)
						this.getSignInfo()
					}
				})
				return;
				this.$fun.ajax.post('advertise/getAdvert').then(res=>{
					if(res.data){
						this.$fun.lookAdever('Leesign/sign',res.data).then(res=>{
							this.$fun.msg(res.msg)
							if (res.status == 1) {
								this.getSignInfo()
							}
						})
					}else{
						this.$fun.msg('加载失败请点击后重试!')
					}
				})
			}



		},
		created() {

		},

	}
</script>

<style lang="scss">
	page{
		background: #FFFFFF;
	}
	.steps{
		margin-top: 40rpx;
	}
 .ad_til {
    padding-left: 16px;
    font-size: 16px;
    color: #333;
}
.steps .ad_pil {
    font-size: 11px;
    color: #999;
    padding-left: 16px;
        margin: 4px 0 8px 0;}
</style>
