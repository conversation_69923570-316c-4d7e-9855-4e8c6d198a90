<template>
	<view class="page">
		<view class="address-input">
<!-- 				<picker   :range="$fun.objToArr(nearbyType,'name')" @change="pickerChange">
					<view class="list-input">
					<view class="title">
						<text>行业类型：</text>
					</view>
					<view class="content">
						<input type="text" disabled v-model="nearbyNmae" placeholder="请选择行业类型">
					</view>
					</view>
				</picker> -->
				
			<view class="list-input">
				<view class="title">
					<text>姓名：</text>
				</view>
				<view class="content">
					<input type="text" v-model="addressInfo.username" placeholder="请输入商家姓名">
				</view>
			</view>
<!-- 			<view class="list-input">
				<view class="title">
					<text>姓名</text>
				</view>
				<view class="content">
					<input type="text" v-model="addressInfo.username" placeholder="请填写姓名">
				</view>
			</view> -->
			<view class="list-input">
				<view class="title">
					<text>手机号:</text>
				</view>
				<view class="content">
					<input type="tel" v-model="addressInfo.mobile" placeholder="请填写手机号">
				</view>
			</view>
			<!-- <view class="list-input">
				<view class="title">
					<text>微信号:</text>
				</view>
				<view class="content">
					<input type="text" v-model="addressInfo.wechat" placeholder="请填写微信号">
				</view>
			</view>
			<view class="list-input">
				<view class="title">
					<text>电子邮箱：</text>
				</view>
				<view class="content">
					<input type="text" v-model="addressInfo.email" placeholder="请输入电子邮箱">
				</view>
			</view>
			<view class="list-input">
				<view class="title">
					<text>店铺名称：</text>
				</view>
				<view class="content">
					<input type="text" v-model="addressInfo.name" placeholder="请输入店铺名称">
				</view>
			</view>
			<view class="list-input">
				<view class="title">
					<text>店长姓名：</text>
				</view>
				<view class="content">
					<input type="text" v-model="addressInfo.nickname" placeholder="请输入店长姓名">
				</view>
			</view> -->
			<view class="list-input">
				<view class="title">
					<text>店铺地址：</text>
				</view>
				<view class="content">
					<input type="text" v-model="addressInfo.address" placeholder="请输入店铺地址">
				</view>
			</view>
		</view>
	<!-- 	<view class="top">
			<view class="left">
				<view class="title">上传LOGO：</view>
			</view>
		</view>
		<view class="top">
			<view class="left">
				<image v-for="(item,index) in addressInfo.logoimage" @click="uploadImage('logoimage',index)"
					:src="$fun.imgUrl(item)" :key="index" style="width: 200rpx;height:200rpx;margin-right: 35rpx;"
					mode=""></image>
				<image v-if="addressInfo.logoimage.length<1" :src="'/static/ico-101.png'" @click="uploadImage('logoimage')"
					style="width: 200rpx;height:200rpx;" mode=""></image>
			</view>
		</view> -->
<!-- 		<view class="top">
			<view class="left">
				<view class="title">上传门头照片：</view>
			</view>
		</view> -->
		<!-- <view class="top">
			<view class="left">
				<image v-for="(item,index) in addressInfo.mtimage" @click="uploadImage('mtimage',index)"
					:src="$fun.imgUrl(item)" :key="index" style="width: 200rpx;height:200rpx;margin-right: 35rpx;"
					mode=""></image>
				<image v-if="addressInfo.mtimage.length<1" :src="'/static/ico-101.png'" @click="uploadImage('mtimage')"
					style="width: 200rpx;height:200rpx;" mode=""></image>
			</view>
		</view> -->
		<!-- <view class="address-input">
			<view class="list-textarea">
				<view class="title">
					<text>商家介绍</text>
				</view>
				<view class="content">
					<textarea  placeholder-style="font-size:35rpx" style="font-size: 35rpx;"  type="text" v-model="addressInfo.content" placeholder="请填写商家介绍" />
				</view>
			</view>
		</view> -->
		<!-- <view class="top">
					<view class="left">
						<view class="title">店铺介绍：</view>
					</view>
				</view>
				<view class="top">
					<view class="left">
						<textarea v-model="addressInfo.content" style="width: 652rpx;
		height: 220rpx;
		background: #F3F5F9;
		padding:30rpx 27rpx;
		border-radius: 8rpx;" name="" id="" placeholder="描述一下店铺介绍" placeholder-style="font-size: 32rpx;
		font-family: Adobe Heiti Std;
		font-weight: normal;
		color: #BBBBBB;" cols="30" rows="10"></textarea>
					</view>
				</view>
				<view class="top">
					<view class="left">
						<view class="title">上传门店照片：</view>
					</view>
				</view>
				<view class="top">
					<view class="left">
						<image v-for="(item,index) in addressInfo.images" @click="uploadImage('images',index)"
							:src="$fun.imgUrl(item)" :key="index" style="width: 200rpx;height:200rpx;margin-right: 35rpx;"
							mode=""></image>
						<image v-if="addressInfo.images.length<3" :src="'/static/ico-101.png'" @click="uploadImage('images')"
							style="width: 200rpx;height:200rpx;" mode=""></image>
					</view>
				</view> -->
				<!-- <view class="top">
					<view class="left">
						<view class="title">上传营业执照：</view>
					</view>
				</view>
				<view class="top">
					<view class="left">
						<image v-for="(item,index) in addressInfo.licenseimage" @click="uploadImage('licenseimage',index)"
							:src="$fun.imgUrl(item)" :key="index" style="width: 200rpx;height:200rpx;margin-right: 35rpx;"
							mode=""></image>
						<image v-if="addressInfo.licenseimage.length<1" :src="'/static/ico-101.png'" @click="uploadImage('licenseimage')"
							style="width: 200rpx;height:200rpx;" mode=""></image>
					</view>
				</view> -->
				<view style="height: 200rpx;background: #FFFFFF;">
					
				</view>
		<!-- <view class="top">
			<view class="left">
				<view class="title">门店照片</view>
			</view>
		</view>
		<view class="top">
			<view class="left">
				<image v-for="(item,index) in addressInfo.indoorimages" :src="$fun.imgUrl(item)" :key="index"
					style="width: 150rpx;height:150rpx;" mode=""></image>
				<image :src="'/static/ico-101.png'" @click="uploadImage('indoorimages')" style="width: 150rpx;height:150rpx;" mode="">
				</image>
			</view>
		</view>
		<view class="address-input">
			<view class="list-input">
				<view class="title">
					<text>商家地址</text>
				</view>
				<view class="content">
					<input type="text" v-model="addressInfo.address" placeholder="请填写商家地址">
				</view>
			</view>
		</view> -->
		<view class="footer-btn">
			<view class="btn" @click="saveAddress()">
				<text>保存</text>
			</view>
		</view>
	</view>
</template>

<script>
	import lotusAddress from "@/components/Winglau14-lotusAddress/Winglau14-lotusAddress.vue";
	export default {
		components: {
			lotusAddress
		},
		data() {
			return {
				addressType: '2',
				addressInfo: {
					// cid:'',
					username:'',
					mobile:'',
					// wechat:'',
					// email:'',
					// name:'',
					// nickname:'',
					address:'',
					// logoimage:[],
					// mtimage:[],
					// content:'',
					// images:[],
					// licenseimage:[]
				},
				address: '',
				id: null,
				lotusAddressData: {
					visible: false,
					provinceName: '',
					cityName: '',
					townName: '',
				},
				nearbyType:[],
				nearbyNmae:''
			};
		},
		onLoad(params) {
			this.addressType = params.type || '2';
			uni.setNavigationBarTitle({
				title: params.name
			})
			if (this.addressType == 1) {
				this.id = params.id
				this.getAddressInfo(params.id)
			}
			this.$fun.ajax.post('category/list',{type:'nearby'}).then(res=>{
				if(res.status==1){
					this.nearbyType =res.data
				}
			})
		},
		methods: {
			pickerChange(e){
				this.nearbyNmae = this.nearbyType[e.detail.value].name
				this.addressInfo.cid = this.nearbyType[e.detail.value].id
			},
			//打开picker
			openPicker() {
				this.lotusAddressData.visible = true;
			},
			switchChange(e) {
				this.addressInfo.status = e.detail.value ? 1 : 0
			},
			uploadImage(str, index = 10086) {
				// 从相册选择图片
				const _this = this;
				uni.chooseImage({
					count: 1,
					sizeType: ['original', 'compressed'],
					sourceType: ['album'],
					success: function(res) {
						_this.handleUploadFile(res.tempFilePaths, str, index);
					}
				});
			},
			// 上传头像
			handleUploadFile(data, str, index) {
				const _this = this;
				const filePath = data.path || data[0];
				this.$fun.uploadPic(
					filePath
				).then(res => {
					this.$fun.msg(res.msg)
					if (res.status == 1) {
						if (str == 'zimage') {
							_this.addressInfo[str] = [res.data.url]
							this.$forceUpdate()
						} else {
							if (index == 10086) {
								_this.addressInfo[str].push(res.data.url)
								this.$forceUpdate()
							} else {
								_this.addressInfo[str][index] = res.data.url
								this.$forceUpdate()
							}
						}
					}
				})
			},
			saveAddress() {
				// buname:'',
				// username:'',
				// mobile:'',
				// mtimage:[],
				// content:'',
				// indoorimages:'',
				// address:''
				// if (!this.addressInfo.cid) {
				// 	this.$fun.msg('请选择行业类型')
				// 	return false
				// }
				if (!this.addressInfo.username) {
					this.$fun.msg('请输入姓名')
					return false
				}
				if (!this.addressInfo.mobile) {
					this.$fun.msg('请输入手机号')
					return false
				}
				// if (!this.addressInfo.wechat) {
				// 	this.$fun.msg('请输入微信号')
				// 	return false
				// }
				// if (!this.addressInfo.email) {
				// 	this.$fun.msg('请输入电子邮箱')
				// 	return false
				// }
				// if (!this.addressInfo.name) {
				// 	this.$fun.msg('请输入店铺名称')
				// 	return false
				// }
				// if (!this.addressInfo.nickname) {
				// 	this.$fun.msg('请输入店长姓名')
				// 	return false
				// }
				if (!this.addressInfo.address) {
					this.$fun.msg('请输入店铺地址')
					return false
				}
				// if (!this.addressInfo.logoimage) {
				// 	this.$fun.msg('请上传店铺LOGO')
				// 	return false
				// }
				// if (!this.addressInfo.mtimage) {
				// 	this.$fun.msg('请上传门头照片')
				// 	return false
				// }
				// if (!this.addressInfo.content) {
				// 	this.$fun.msg('请描述一下店铺介绍')
				// 	return false
				// }
				// if (this.addressInfo.images.length < 3) {
				// 	this.$fun.msg("请上至少上传三张门店图片");
				// 	return false;
				// }	
				// if (!this.addressInfo.licenseimage.length) {
				// 	this.$fun.msg("请上传营业执照");
				// 	return false;
				// }
				// let prams = JSON.parse(JSON.stringify(this.addressInfo))
				// prams.images.join(',')
				// prams.mtimage.join(',')
				// prams.licenseimage.join(',')
				// prams.logoimage.join(',')
				this.$fun.ajax.post('news/business', prams).then(res => {
					this.$fun.msg(res.msg)
					if (res.status == 1) {
						this.$fun.msg(res.msg)
						this.$fun.jump('', 4)
					}
				})
			},
			//回传已选的省市区的值
			choseValue(res) {
				//res数据源包括已选省市区与省市区code
				console.log(res);
				//res.isChose = 1省市区已选 res.isChose = 0;未选
				if (res.isChose) {
					this.lotusAddressData.visible = res.visible; //visible为显示与关闭组件标识true显示false隐藏
					this.lotusAddressData.provinceName = res.province; //省
					this.lotusAddressData.cityName = res.city; //市
					this.lotusAddressData.townName = res.town; //区
					this.addressInfo.pro = res.province; //省
					this.addressInfo.city = res.city; //市
					this.addressInfo.area = res.town; //区
					this.address = `${res.province} ${res.city} ${res.town}`; //region为已选的省市区的值
					this.$forceUpdate()
				} else {
					this.$fun.msg('请完整选择地址')
				}
			},
			getAddressInfo(id) {
				this.$fun.ajax.post('address/get', {
					id
				}).then(res => {
					if (res.status == 1) {
						this.addressInfo = res.data
						this.address = `${res.data.pro} ${res.data.city} ${res.data.area}`
					}
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	page {
		background: #FFFFFF;
	}


	.address-input {
		width: 100%;
		background-color: #FFFFFF;

		.list-input {
			display: flex;
			align-items: center;
			padding: 0 4%;
			height: 104rpx;
			border-bottom: 2rpx solid #F3F5F9;

			.title {
				display: flex;
				align-items: center;
				width: 40%;
				height: 100%;
				// font-size:35rpx;
				font-size: 34px;
				font-family: Adobe Heiti Std;
				font-weight: normal;
				color: #333333;

				text {
					font-size: 34rpx;
					font-family: Adobe Heiti Std;
					font-weight: normal;
					color: #333333;
				}

				.phsy {
					font-size: 34rpx;
					color: #999999;
				}

				input {
					font-size: 34rpx;
				}
			}

			.content {
				display: flex;
				align-items: center;
				width: 70%;
				height: 100%;

				input {
					width: 100%;
					height: 100%;
					font-size: 35rpx;
					color: #222222;
				}
			}
		}

		.list-textarea {
			display: flex;
			align-items: center;
			padding: 0 4%;
			height: 200rpx;
			border-bottom: 2rpx solid #f6f6f6;

			.title {
				display: flex;
				width: 25%;
				height: 80%;

				text {
					color: #222222;
					font-size: 35rpx;
				}
			}

			.content {
				display: flex;
				align-items: center;
				width: 70%;
				height: 100%;

				textarea {
					width: 100%;
					height: 80%;
					font-size: 26rpx;
					color: #222222;
				}
			}
		}
	}

	.tag-default {
		width: 100%;
		border-top: 20rpx solid #f6f6f6;

		.tag-list {
			display: flex;
			align-items: center;
			padding: 0 4%;
			height: 200rpx;

			.title {
				width: 20%;
				height: 80%;

				text {
					font-size: 26rpx;
					color: #222222;
				}
			}

			.content {
				display: flex;
				width: 70%;
				height: 80%;

				.list {
					display: flex;
					align-items: center;
					justify-content: center;
					min-width: 120rpx;
					height: 60rpx;
					border: 2rpx solid #f6f6f6;
					border-radius: 60rpx;
					margin-right: 20rpx;

					text {
						color: #555555;
						font-size: 24rpx;
					}
				}

				.action {
					background-color: $base;
					border: 2rpx solid $base;

					text {
						color: #FFFFFF;
					}
				}
			}
		}

		.default-address {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 4%;
			height: 100rpx;

			.title {
				display: flex;
				align-items: center;
				width: 20%;
				height: 80%;
			}

			.switch-default {
				uni-switch .uni-switch-input {
					background: #22AA44 !important;
				}
			}
		}
	}

	.footer-btn {
		position: fixed;
		left: 0;
		bottom: 0rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 100rpx;
		background-color: #FFFFFF;

		.btn {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 80%;
			height: 70rpx;
			background: linear-gradient(to right, $base, $change-clor);
			border-radius: 70rpx;
			box-shadow: 0 10rpx 10rpx $base;

			text {
				font-size: 28rpx;
				color: #FFFFFF;
			}
		}
	}
	.content_box {
		padding: 35rpx
	}

	.my-account {
		background-color: #FFFFFF;
		/*  #ifndef H5  */
		height: 100vh;
		/*  #endif  */
		padding: 32upx 20upx;
		width: 100%;

		.header {
			padding: 30upx;
			height: 200upx;
			display: flex;
			align-items: center;
			opacity: 0.9;
			border-radius: 20upx;
			color: rgba(255, 255, 255, 0.6);
			font-size: 24rpx;
			position: relative;

			.account {
				width: calc(100% - 60upx);
				display: flex;
				position: absolute;
				z-index: 2;
				justify-content: space-between;

				.assets {
					.money {
						color: #fff;
						font-size: 30upx;
						margin: 0;
					}
				}

				.recharge {
					font-size: 28rpx;
					width: 150upx;
					height: 54upx;
					line-height: 54upx;
					border-radius: 28rpx;
					background-color: #fff9f8;
					text-align: center;
					margin-top: 10upx;
				}
			}

			.cumulative {
				width: calc(100% - 240upx);
				position: absolute;
				bottom: 20upx;
				display: flex;
				justify-content: space-between;

				.money {
					color: #fff;
					font-size: 36rpx;
					margin: 0;
				}
			}

			.header-bg {
				position: absolute;
				width: 100%;
				height: 320upx;
				z-index: 1;
				top: 0;

				image {
					width: 100%;
					height: 320upx;
				}
			}
		}

		.nav {
			border-bottom: 1px solid #f5f5f5;
			display: flex;

			.item {
				flex: 1;
				margin: 20upx;
				font-size: 26rpx;
				display: inline-block;
				text-align: center;
				color: #999;

				.iconfont {
					display: block;
					margin: 0 auto;
					font-size: 35rpx;
				}
			}
		}

		.advert {
			display: flex;

			.item {
				flex: 1;
				border-radius: 24upx;
				padding: 10upx 0;
				margin: 20upx 10upx;
				display: flex;
				justify-content: space-between;

				.iconfont {
					font-size: 40upx;
					margin-right: 20upx;
				}

				.text {
					margin-left: 20upx;

					.name {
						font-size: 28rpx;
						font-weight: bold;
						height: 40upx;
					}

					.desc {
						font-size: 24rpx;
					}
				}
			}

			.on {
				background-color: #fff3f3;
			}
		}
	}

	/* 内容区 */
	.conter {
		.title {
			color: #666;
			margin-top: 35rpx;
			font-size: 35rpx;
		}

		.cent {
			margin-top: 25rpx;
			overflow: hidden;

			.list {
				width: 200rpx;
				height: 120rpx;
				border: 1rpx solid #e8e8e8;
				float: left;
				margin: 0 18rpx;
				margin-bottom: 45rpx;
				position: relative;
				text-align: center;
				line-height: 120rpx;
				border-radius: 15rpx;

				// &:nth-child(3n) {
				// 	margin-right: 0;
				// }

				input {
					height: 35rpx;
					margin-left: 10rpx;
					margin-top: 35rpx;
				}

				.van-cell {
					padding: 0;
					width: 180rpx;
				}

				image {
					width: 35rpx;
					height: 37rpx;
					position: absolute;
					bottom: 0;
					right: 0;
					display: none;
				}

				&.active {
					color: #dd1021;
					border: 1rpx solid #dd1021;

					image {
						display: block;
					}
				}
			}
		}
	}

	.top {
		display: flex;
		justify-content: space-between;
		padding-left: 36rpx;
		background: #fff;
		.left {
			margin-top: 35rpx;

			.title {
				font-size: 34rpx;
				font-family: Adobe Heiti Std;
				font-weight: normal;
				color: #333333;
			}

			.num {
				font-size: 48rpx;
				color: #000;
				margin-top: 35rpx;
				font-weight: bold;
			}

			.jyjl {
				font-size: 24rpx;
				color: #dd1021;
				margin-top: 35rpx;
			}
		}

		.rightimg {
			width: 233rpx;
			height: 240rpx;
			margin-right: 35rpx;
		}
	}
</style>
