<template>
	<view class="page">

		<!-- 筛选 -->
		<!-- <view class="screen-info">
			<view class="screen-list">
				<view class="list" @click="chnageTab('weigh')" :class="{'action':screenShow=='weigh'}">
					<text>综合</text>
				</view>
				<view class="list" @click="chnageTab('num')" :class="{'action':screenShow=='num'}">
					<text>销量</text>
					<text></text>
				</view>
				<view class="list" @click="chnageTab('',2)" :class="{'action':screenShow=='asc'||screenShow=='desc'}">
					<text>价格</text>
					<view class="icon_j">
						<text class="iconfont icon-sanjiao up" v-if="screenShow=='asc'"
							:style="{color:screenShow=='asc'?'#310FFF !important':'#55555 !important'}"></text>
						<text class="iconfont icon-sanjiao down" v-if="screenShow=='desc'"
							:style="{color:screenShow=='desc'?'#310FFF !important':'#55555 !important'}"></text>
					</view>
				</view>
			</view>
			<view class="screen-popup" @click.stop="isScreen = false" v-show="isScreen">
				<view class="synthesize">
					<view class="list">
						<text class="check"></text>
						<text class="title">综合排序</text>
					</view>
					<view class="list">
						<text class="check"></text>
						<text class="title">评论数从高到低</text>
					</view>
				</view>
			</view>
		</view> -->
		<!-- 商品列表 -->
		<!-- 订单tab -->
		<mescroll-body ref="mescrollRef" @init="mescrollInit" @down="downCallback" @up="upCallback" :down="downOption"
			:up="upOption" :top="0">
			<view class="head-search">
				<view class="search">
					<view class="icon">
						<image :src="$fun.imgUrl('/static/fdj_ico.png')" mode=""></image>
					</view>
					<view class="hint">
						<input v-model="keyword" type="text" placeholder="请输入要搜索的商品" />
					</view>
				</view>
			</view>
			<view class="goods-data">
				<view class="goods-list-left">
					<YWaterfall @showShare="handleShare" :items="leftList" :pin="pin" ref="yWaterfall">
					</YWaterfall>
				</view>
				<view class="goods-list-right">

					<YWaterfall @showShare="handleShare" :items="rightList" :pin="pin" ref="yWaterfall">
					</YWaterfall>
				</view>

			</view>
		</mescroll-body>
		<!-- 提示框 -->
		<!-- <DialogBox ref="DialogBox"></DialogBox> -->

	</view>
</template>

<script>
	// 引入mescroll-mixins.js
	import MescrollMixin from "@/components/mescroll-uni/mescroll-mixins.js";
	import YWaterfall from '@/components/y-components/y-waterfall.vue';

	export default {
		components: {
			YWaterfall
		},
		mixins: [MescrollMixin], // 使用mixin
		data() {
			return {
				cartList: [],
				OrderType: 'all',
				id: '',
				mescroll: null, // mescroll实例对象 (此行可删,mixins已默认)
				// 下拉刷新的配置(可选, 绝大部分情况无需配置)
				downOption: {},
				// 上拉加载的配置(可选, 绝大部分情况无需配置)
				upOption: {},
				// 列表视图切换
				isList: true,
				// 筛选弹窗
				isScreen: false,
				// 筛选切换
				screenShow: 'weigh',
				// 抽屉
				isDrawer: false,
				keyword: '',
				goodsList: [],
				leftList: [],
				rightList: [],
				pin: 0,
				currentShareItem: null
			}
		},


		watch: {
			keyword(val) {
				this.cartList = []
				this.leftList = [];
				this.rightList = [];
				this.goodsList = [];
				try {
					this.$refs.uWaterfall.clear();
				} catch (e) {

				}
				this.mescroll.resetUpScroll(false);
			}
		},
		onLoad(params) {
			uni.setNavigationBarTitle({
				title: params.name ? params.name : '商品搜索'
			});
			this.keyword = decodeURIComponent(params.keyword || '');
			this.id = params.id;
			if (params.pin) {
				this.pin = params.pin
			}
		},
		methods: {
			handleShare() {
				// #ifdef MP-WEIXIN
				uni.showShareMenu({
					withShareTicket: true,
					menus: ['shareAppMessage', 'shareTimeline']
				})
				// #endif
			},
			onBack() {
				uni.navigateBack();
			},

			/*下拉刷新的回调, 有三种处理方式:*/
			downCallback() {
				this.cartList = []
				try {
					this.$refs.uWaterfall.clear();
				} catch (e) {

				}
				this.mescroll.resetUpScroll(false);
			},
			/*上拉加载的回调*/
			upCallback(e) {
				this.mescroll.removeEmpty();
				const data = {
					page: e.num,
					pagenum: e.size,
					cid: this.id,
					sort: this.screenShow,
					search: this.keyword
				};
				// this.leftList = [];
				// this.rightList = [];
				this.$fun.ajax.post('goods/list', data).then(res => {
					if (res.status == 1) {
						const curList = res.data;

						if (e.num === 1) {
							this.cartList = [];

							//第一页清空数据重载
							if (curList.length > 0) {
								uni.pageScrollTo({
									scrollTop: 0,
									duration: 200
								});
							}
						}
						this.cartList = this.cartList.concat(curList); //追加新数据
						this.mescroll.endSuccess(curList.length); //结束加载状态
						for (let i = 0; i < curList.length; i++) {
							if (i % 2 == 0) {
								this.leftList.push(curList[i]);
							} else {
								this.rightList.push(curList[i]);
							}
						}
						console.log(curList.length)

					}
				})
			},
			chnageTab(type, t = 1) {
				if (this.screenShow == type) {
					return
				}
				if (t == 1) {
					this.screenShow = type;
				} else {
					if (this.screenShow == 'desc') {
						this.screenShow = 'asc'
					} else {
						this.screenShow = 'desc'
					}
				}
				this.goodsList = []
				this.mescroll.resetUpScroll(false);
				// this.isScreen = !this.isScreen;
			},
		},
		// 分享给朋友
		onShareAppMessage(res) {
			return {
				title: '好物分享',
				path: '/pages/home/<USER>',
				imageUrl: '', // 分享图片
				success(res) {
					uni.showToast({
						title: '分享成功'
					});
				},
				fail(res) {
					uni.showToast({
						title: '分享失败',
						icon: 'none'
					});
				}
			}
		},

		// 分享到朋友圈
		onShareTimeline() {
			return {
				title: '好物分享',
				query: '',
				imageUrl: '' // 分享图片
			}
		}
	}
</script>

<style scoped lang="scss">
	.page {
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		background-color: #f6f6f6;
	}

	/* 搜索 */
	.search-head {
		position: fixed;
		left: 0;
		top: 0;
		display: flex;
		align-items: center;
		width: 100%;
		// height: 100rpx;
		z-index: 10;
		/* #ifdef APP-PLUS */
		height: calc(90rpx + var(--status-bar-height));
		/* #endif */
		/* #ifdef MP */
		height: calc(300rpx + var(--status-bar-height));
		padding-top: var(--status-bar-height);
		/* #endif */
		background-color: #ffffff;

		.back {
			margin-top: 50rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			width: 10%;
			height: 80rpx;

			text {
				width: 20rpx;
				height: 20rpx;
				border-left: 2rpx solid #555555;
				border-bottom: 2rpx solid #555555;
				transform: rotate(45deg);
			}
		}

		.search {
			margin-top: 50rpx;
			display: flex;
			align-items: center;
			width: 76%;
			height: 80rpx;
			background-color: #f6f6f6;
			border-radius: 60rpx;
			padding: 0 4%;

			text {
				font-size: 34rpx;
				color: #c0c0c0;
			}

			input {
				width: 90%;
				height: 100%;
				font-size: 26rpx;
				color: #212121;
				margin-left: 10rpx;
			}
		}

		.cut {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 10%;
			height: 100%;

			text {
				font-size: 38rpx;
				color: #555555;
			}
		}
	}

	/* 筛选 */
	.screen-info {
		position: fixed;
		left: 0;
		top: 100rpx;
		z-index: 11;
		/* #ifdef APP-PLUS */
		top: 150rpx;
		/* #endif */
		/* #ifdef MP */
		top: calc(200rpx + var(--status-bar-height));
		/* #endif */
		width: 100%;
		height: 100rpx;
		background-color: #ffffff;

		.screen-list {
			display: flex;
			align-items: center;
			width: 100%;
			height: 100%;
			justify-content: space-between;

			.list {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 25%;
				height: 100%;

				text {
					font-size: 26rpx;
					color: #555555;
				}

				.icon_z {
					font-size: 24rpx;
					transform: rotate(90deg) scale(0.7);
				}

				.icon_j {
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-items: center;
					width: 40rpx;
					height: 100%;

					.up {
						transform: rotate(-90deg) scale(0.7);
						margin-bottom: -15rpx;
						//margin-left: 8rpx;
					}

					.down {
						transform: rotate(90deg) scale(0.7);
					}
				}

				.icon_s {
					font-size: 24rpx;
					margin-left: 10rpx;
					// transform: scale(0.7);
				}
			}

			.action {
				text {
					color: $base;
				}
			}
		}

		// 弹出层
		.screen-popup {
			position: fixed;
			left: 0;
			top: 200rpx;
			/* #ifdef APP-PLUS */
			top: 250rpx;
			/* #endif */
			/* #ifdef MP */
			top: 300rpx;
			/* #endif */
			width: 100%;
			height: 100%;
			background-color: rgba(0, 0, 0, 0.3);

			// 综合
			.synthesize {
				padding: 0 20rpx;
				height: 200rpx;
				background-color: #f6f6f6;
				border-radius: 0 0 20rpx 20rpx;

				.list {
					display: flex;
					align-items: center;
					width: 100%;
					height: 80rpx;

					.check {
						display: inline-block;
						width: 20rpx;
						height: 10rpx;
						border-left: 4rpx solid $base;
						border-bottom: 4rpx solid $base;
						border-radius: 4rpx;
						transform: rotate(-45deg);
					}

					.title {
						font-size: 26rpx;
						color: #555555;
						margin-left: 20rpx;
					}
				}
			}
		}
	}

	.goods-data {
		width: 100%;
		padding: 0 25rpx;
		display: flex;
		justify-content: space-between;

		.goods-list-left {
			width: 49%;
		}

		.goods-list-right {
			width: 49%;
		}
	}

	.basis-lg {
		padding-top: 0;
		border-radius: 20rpx 0 0 20rpx;
		flex-basis: 80% !important;

		.serve {
			/* #ifdef APP-PLUS */
			padding-top: 50rpx;
			/* #endif */
			/* #ifdef MP */
			padding-top: 140rpx;
			/* #endif */
			padding-left: 20rpx;
			padding-right: 20rpx;
			background-color: #ffffff;

			.title {
				display: flex;
				align-items: center;
				width: 100%;
				height: 80rpx;

				text {
					color: #212121;
					font-size: 28rpx;
				}
			}

			.serve-list {
				display: flex;
				flex-wrap: wrap;
				padding: 20rpx 0;

				.list {
					display: flex;
					align-items: center;
					justify-content: center;
					width: 30%;
					height: 60rpx;
					border-radius: 60rpx;
					margin-right: 4%;
					background-color: #f6f6f6;

					text {
						color: #555555;
						font-size: 24rpx;
					}
				}

				.list:nth-child(3n) {
					margin-right: 0;
				}

				.action {
					background-color: $rgba-03;
					border: 2rpx solid $base;

					text {
						color: $base;
					}
				}
			}
		}

		.price-screen {
			padding: 0 4%;
			background-color: #ffffff;
			margin-top: 20rpx;

			.title {
				display: flex;
				align-items: center;
				width: 100%;
				height: 80rpx;

				text {
					color: #212121;
					font-size: 28rpx;
				}
			}

			.price-section {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 100%;
				height: 80rpx;

				input {
					width: 180rpx;
					height: 50rpx;
					border-radius: 50rpx;
					font-size: 24rpx;
					color: #555555;
					background-color: #f6f6f6;
				}

				text {
					display: flex;
					width: 60rpx;
					height: 2rpx;
					background-color: #f6f6f6;
					margin: 0 20rpx;
				}
			}
		}

		.operation-btn {
			position: absolute;
			left: 0;
			bottom: 0;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 4%;
			width: 100%;
			height: 100rpx;
			background-color: #ffffff;

			.btn {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 46%;
				height: 70rpx;
				background-color: #ffffff;
				border: 2rpx solid #f6f6f6;
				border-radius: 70rpx;

				// margin-left: 5%;
				text {
					color: #212121;
					font-size: 26rpx;
				}
			}

			.action {
				background-color: $base;

				text {
					color: #ffffff;
				}
			}
		}
	}

	::v-deep .u-column {
		width: 50%;

		.list-view {
			padding-bottom: 20rpx;
			width: 94% !important;
			margin-left: 3%;
			height: auto !important;
		}
	}

	.head-search {
		margin-top: 20rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding-bottom: 20rpx;

		.search {
			display: flex;
			align-items: center;
			justify-content: flex-start;
			width: 100%;
			background: #FFFFFF;
			margin: 0 32rpx;
			padding: 0 20rpx;
			height: 76rpx;
			border-radius: 10rpx;
			border-radius: 38rpx 38rpx 38rpx 38rpx;

			.icon {
				display: flex;
				align-items: center;
				margin-right: 20rpx;

				image {
					width: 40rpx;
					height: 40rpx;
				}
			}

			.hint {
				display: flex;
				align-items: center;

				.max {
					font-size: 30rpx;
					font-weight: bold;
					color: #FFFFFF;
				}

				.min {
					font-size: 24rpx;
					color: #AAAAAA;
					margin-left: 10rpx;
				}
			}
		}
	}
</style>