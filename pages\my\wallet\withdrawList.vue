/**
* @Description: 小玉兔



* 
*/
<template>
	<mescroll-body ref="mescrollRef" @init="mescrollInit" @down="downCallback" @up="upCallback" :down="downOption"
		:up="upOption" :top="0">
		<view class="index_class">
			<view v-if="type=='withdrawal'" class="myrecord" hover-class="click-active-bgc"
				v-for="(item,index) in recordList" :key="index"
				@click="$fun.jump(`./walletDetails?id=${item.id}&name=提现详情&type=${type}`)">
				<view class="color">￥{{item.money}}</view>
				<view>
					<view class="title" v-if="item.status == 0">等待审核</view>
					<view class="title" v-if="item.status == 1">审核通过</view>
					<view class="title" v-if="item.status == 2">审核拒绝</view>
	
				</view>
				<view class="memo" v-if="item.memo&&item.status == 2">
					原因:{{item.memo}}
				</view>
								<view class="time">{{$u.timeFormat(item.create_time, 'yyyy-mm-dd hh:MM')}}</view>

			</view>
			<view v-if="type=='recharge'" class="myrecord" hover-class="click-active-bgc"
				v-for="(item,index) in recordList" :key="index"
				@click="$fun.jump(`./walletDetails?id=${item.id}&name=充值详情&type=${type}`)">
				<view class="color">￥{{item.money}}</view>
				<view>
					<view class="title" v-if="item.status == 0">等待审核</view>
					<view class="title" v-if="item.status == 1">审核通过</view>
					<view class="title" v-if="item.status == 2">审核拒绝</view>
	
				</view>
				<view class="memo" v-if="item.memo&&item.status == 2">
					原因:{{item.memo}}
				</view>
								<view class="time">{{$u.timeFormat(item.create_time, 'yyyy-mm-dd hh:MM')}}</view>
			</view>
			<view v-if="type=='transfer'" class="myrecord" hover-class="click-active-bgc"
				v-for="(item,index) in recordList" :key="index"
				@click="$fun.jump(`./walletDetails?id=${item.id}&name=转账详情&type=${type}`)">
				<view class="color">￥{{item.money}}</view>
				<view>
					<view class="title" v-if="item.status == 0">等待审核</view>
					<view class="title" v-if="item.status == 1">审核通过</view>
					<view class="title" v-if="item.status == 2">审核拒绝</view>
	
				</view>
				<view class="memo" v-if="item.memo&&item.status == 2">
					原因:{{item.memo}}
				</view>
								<view class="time">{{$u.timeFormat(item.create_time, 'yyyy-mm-dd hh:MM')}}</view>

			</view>
			<!-- <loadMore :loadingType="loadingType"></loadMore> -->
		</view>

	</mescroll-body>
</template>

<script>
	let page = 1
	import MescrollMixin from "@/components/mescroll-uni/mescroll-mixins.js";
	export default {
		mixins: [MescrollMixin], // 使用mixin
		onLoad(options) {
			page = 1
			this.type = options.type
			uni.setNavigationBarTitle({
				title: options.name
			});
		},
		data() {
			return {
				recordList: [],
				loadingType: 0,
				type: '',
				mescroll: null, // mescroll实例对象 (此行可删,mixins已默认)
				// 下拉刷新的配置(可选, 绝大部分情况无需配置)
				downOption: {},
				// 上拉加载的配置(可选, 绝大部分情况无需配置)
				upOption: {},
				// 列表视图切换
			}
		},
		methods: {
			/*下拉刷新的回调, 有三种处理方式:*/
			downCallback() {
				this.cartList = []
				this.mescroll.resetUpScroll(false);
			},
			/*上拉加载的回调*/
			upCallback(e) {
				this.mescroll.removeEmpty();
				const data = {
					page: e.num,
					pagenum: e.size,
					type: this.type,
				};
				this.$fun.ajax.post('wallet/coinFlow', data).then(res => {
					if (res.status == 1) {
						const curList = res.data.data;
						if (e.num === 1) {
							this.recordList = [];
							//第一页清空数据重载
							// if (curList.length > 0) {
							// 	uni.pageScrollTo({
							// 		scrollTop: 0,
							// 		duration: 200
							// 	});
							// }
						}
						this.recordList = this.recordList.concat(curList); //追加新数据
						this.mescroll.endSuccess(curList.length); //结束加载状态
					}
				})
				this.$fun.ajax.post('News/lists', {type:'project'}).then(res => {
					if (res.status == 1) {
						this.swiperList = res.data
					}
				})
			},
			getWithdrawList() {
				this.$fun.ajax.post('/wallet/coinFlow', {
					type: this.type,
					page: page
				}).then(res => {
					if (res.status == 1) {
						this.recordList = [...this.recordList, ...res.data.data]
						this.loadingType = 0
						if (res.data.data.length < res.data.per_page) {
							this.loadingType = 2
						}
					}
				})
			}
		},
	}
</script>

<style lang="scss">
	pgae {
		background: #FFFFFF;
	}
	.index_class{
		.myrecord{
			width: 100%;
			display: flex;
			flex-wrap: wrap;
			padding: 20rpx 40rpx;
			background: #FFFFFF;
			view{
				width: 50%;
				.title{
					width: 100%;
					text-align: right;
				}
			}
			view.color{
				font-size: 15px;
				    color: #333;
			}
			view.time{
				margin-top: 20rpx;
				width: 100%;
				text-align: right;
				    color: #c0c0c0;
				    font-size: 12px;
			}
			view.memo{
				margin-top: 10rpx;
				width: 100%;
				    color: red;
					font-size: 13px;
			}
		}
	}
</style>
