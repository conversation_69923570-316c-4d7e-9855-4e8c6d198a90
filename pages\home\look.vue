<template>
	<view class="videoBox">
		<view class="flex_box">
			<view class="time" v-if="time1">
				{{time1}}
			</view>
			<view class="close" @click="closeViode">
				关闭
			</view>
		</view>
		<view style="height: 5vh;background: #000000;">
			
		</view>
		<video id="myVideo"  @ended="endPlay" :controls="false"
			style="width: 100vw;height: 95vh;" autoplay :src="$fun.imgUrl(File)"></video>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				type: null,
				File: '',
				id: '',
				time: 0,
				time1: 0,
				timer: null,
				isL: false
			}
		},
		watch:{
			time1(val){
				console.log('----')
				if(val==0){
					this.$fun.ajax.post('Advertise/setIndex', {
						type: this.type,
						aid: this.id
					}).then(res => {
						this.$fun.msg(res.msg)
					})
				}
				console.log('----')
			}
		},
		onLoad(option) {
			uni.setNavigationBarTitle({
				title: option.name ? option.name : ''
			})
			this.type = option.type;
			let prams = {
				type: option.type,
				aid: option.aid ? option.aid : '',
			}
			this.$fun.ajax.post('Advertise/shipinList', prams).then(res => {
				if (res.data) {
					console.log(res.data)
					this.id = res.data.id
					this.File = res.data.file
					this.time = res.data.time
					this.time1 = res.data.time
					uni.createVideoContext(`myVideo`).play();
					this.setTime()
				}
			})
		},
		methods: {
			getPlayTime(e) {
				let playTime = e.detail.currentTime;
				if (playTime > this.time && this.isL) {
					this.isL = true
					this.$fun.ajax.post('Advertise/setIndex', {
						type: this.type,
						aid: this.id
					}).then(res => {
						this.$fun.msg(res.msg)
					})
				}
			},
			setTime() {
				this.time1 = this.time1 - 1;
				this.timer = setInterval(() => {
					if (this.time1 > 0) {
						this.time1 = this.time1 - 1;
					} else {
						clearInterval(this.timer);
						this.timer = null;
					}
				}, 1000)
			},
			pauseTime() {
				clearInterval(this.timer);
				this.timer = null;
			},
			closeViode() {
				let _this = this;
				uni.createVideoContext(`myVideo`).pause()
				this.pauseTime();
				if(this.time1!=0){
					uni.showModal({
						title: '提示',
						content: '确定要退出领取奖励吗?',
						success: function(res) {
							if (res.confirm) {
								_this.$fun.jump('/pages/home/<USER>',3,0)
							} else if (res.cancel) {
								_this.setTime();
								uni.createVideoContext(`myVideo`).play();
							}
						}
					});
				}else{
					_this.$fun.jump('/pages/home/<USER>',3,0)
				}
				
			},
			endPlay() {
				this.$fun.ajax.post('Advertise/setIndex', {
					type: this.type,
					aid: this.id
				}).then(res => {
					this.$fun.msg(res.msg)
					if (res.status == 1) {
						setTimeout(() => {
							uni.navigateBack()
						}, 1200)
					}
				})
			}
		},
		onUnload() {
			clearInterval(this.timer);
			this.timer = null;
		}
	}
</script>

<style lang="scss">
	#myVideo {
		width: 100vw;
		height: 90vh;
	}

	.videoBox {
		position: relative;

		.flex_box {
			position: absolute;
			top: 20rpx;
			right: 20rpx;
			z-index: 100;
			display: flex;

			.time {
				width: 50rpx;
				height: 50rpx;
				border-radius: 50%;
				background-color: #FFFFFF;
				text-align: center;
				color: #000000;
				display: flex;
				font-size: 12px;
				justify-content: center;
				align-items: center;
				font-size: 28rpx;
			}

			.close {
				margin-left: 50rpx;
				padding: 10rpx 20rpx;
				background: #FFFFFF;
				font-size: 24rpx;
				border-radius: 45%;
			}
		}
	}
</style>