<template>
	<view class="container-out">
		<view class="circle" v-for="(item,index) in circleList" :key="index" :style="{'top':item.topCircle+'rpx','left':item.leftCircle+'rpx','background-color': (index%2==0)?colorCircleFirst:colorCircleSecond}"></view>
		<view class="container-in">
			<view class="content-out" v-for="(item,index) in awardList" :key="index" :style="{'top':item.topAward+'rpx','left':item.leftAward+'rpx','background-color': (index==indexSelect)?colorAwardSelect:colorAwardDefault}">
				<img class="award-image" :src="item.imageAward"></img>
			</view>
			<view class="start-btn" @click="startGame" :style="{'background-color':isRunning?'#e7930a':'#DC143C'}">开始</view>
		</view>
		<view class="reward_instruction">
			<text>{{rewardInstruction}}</text>
		</view>
	</view>
</template>

<script>
	export default {
		name: "NineBox",
		data() {
			return {
				colorCircleFirst: '#FFDF2F', //圆点颜色1
				colorCircleSecond: '#FE4D32', //圆点颜色2
				colorAwardDefault: '#F5F0FC', //奖品默认颜色
				colorAwardSelect: '#ffe400', //奖品选中颜色
				indexSelect: 0, //被选中的奖品index
				isRunning: false, //是否正在抽奖
				intervalTimer: null,//循环器
				setOutTimer: null,//定时器
				stopRewardFlag:false//是否停止抽奖
			};
		},
		props: {
			circleList: {
				type: Array,
				default: new Array()
			},
			awardList: {
				type: Array,
				default: new Array()
			},
			rewardInstruction: {
				type: String,
				default: ""
			}
		},
		watch: {
			circleList: {
				deep: true,
				handler(v) {
					this.circleList = v
				}
			},
			awardList: {
				deep: true,
				handler(v) {
					this.imageAward = v
				}
			},
			rewardInstruction(v) {
				this.rewardInstruction = v
			}
		},
		methods: {
			//开始游戏
			startGame: function() {
				if (this.isRunning) return;
				this.isRunning = true;
				let _this = this;
				let indexSelect = 0;
				let i = 0;
				_this.intervalTimer = setInterval(function() {
					indexSelect++;
					i += 30;
					indexSelect = indexSelect % 8;
					_this.indexSelect = indexSelect;
				}, 200 + i);
				_this.setOutTimer=setTimeout(function() {
					_this.$emit('goPlayReward');
				}, 5000);
			},
			openReward(selectIndexRes) {
				let that = this;
				let newIntervalTimer = setInterval(function() {
					if (that.indexSelect == selectIndexRes) {
						//去除循环
						clearInterval(that.intervalTimer);
						clearInterval(newIntervalTimer);
						if(this.stopRewardFlag){
							return false;
						}
						//获奖提示
						uni.showModal({
							title: '抽奖结果',
							content: that.awardList[selectIndexRes].noPrize ? that.awardList[selectIndexRes].awardName : '恭喜，获得了' + (
								that.awardList[selectIndexRes].awardName),
							showCancel: false, //去掉取消按钮
							success: function(res) {
								if (res.confirm) {
									//that.isRunning = false;
								}
							}
						});
					}
				}, 1)
			},
			stopReward(){
				this.stopRewardFlag=true;
				clearInterval(this.intervalTimer);
				clearTimeout(this.setOutTimer);
			}
		}
	};
</script>

<style>
	/**index.wxss**/

	.container-out {
		height: 600rpx;
		width: 650rpx;
		background-color: #b136b9;
		margin: 100rpx auto;
		border-radius: 40rpx;
		box-shadow: 0 10px 0 #871a8e;
		position: relative;
	}

	.container-in {
		width: 580rpx;
		height: 530rpx;
		background-color: #871a8e;
		border-radius: 40rpx;
		position: absolute;
		left: 0;
		right: 0;
		top: 0;
		bottom: 0;
		margin: auto;
	}

	/**小圆球
	box-shadow: inset 3px 3px 3px #fff2af;*/

	.circle {
		position: absolute;
		display: block;
		border-radius: 50%;
		height: 20rpx;
		width: 20rpx;
	}

	.content-out {
		position: absolute;
		height: 150rpx;
		width: 166.6666rpx;
		background-color: #f5f0fc;
		border-radius: 15rpx;
		box-shadow: 0 5px 0 #d87fde;
	}

	/**居中 加粗*/

	.start-btn {
		position: absolute;
		margin: auto;
		top: 0;
		left: 0;
		bottom: 0;
		right: 0;
		border-radius: 15rpx;
		height: 150rpx;
		width: 166.6666rpx;
		background-color: #ffe400;
		box-shadow: 0 5px 0 #e7930a;
		color: #FFFFFF;
		text-align: center;
		font-size: 55rpx;
		font-weight: bolder;
		line-height: 150rpx;
	}

	.award-image {
		position: absolute;
		margin: auto;
		top: 0;
		left: 0;
		bottom: 0;
		right: 0;
		height: 140rpx;
		width: 130rpx;
	}
	.reward_instruction{
		position: absolute;
		top:650rpx;
	}
</style>
