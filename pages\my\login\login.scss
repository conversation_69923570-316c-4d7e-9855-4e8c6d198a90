.page{
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: #FFFFFF;
}
.logo{
  position: absolute;
  top: 0;
  width: 100%;
  // height: 240rpx;
  image{
	  width: 100vw;
  }
  .login_title{
	  position: absolute;
	  top: 250rpx;
	  left: 74rpx;
	  z-index: 1;
	  font-family: <PERSON><PERSON>ei;
	  font-weight: 500;
	  font-size: 52rpx;
	  color: #310FFF;
	  line-height: 69rpx;
	  text-align: left;
	  font-style: normal;
	  text-transform: none;
	  view{
		  margin-bottom: 10rpx;
	  }
  }
}
.login_content{
	padding: 48rpx 10%;
	background: #FFFFFF;
	z-index: 10;
	position: relative;
	border-radius: 80rpx 80rpx 0rpx 0rpx;
}
/* 填写 */
.input-info{
  padding-bottom: 0;
  .title{
	  font-family: Ya<PERSON><PERSON>;
	  font-weight: bold;
	  font-size: 32rpx;
	  color: #333333;
	  line-height: 43rpx;
	  text-align: left;
	  font-style: normal;
	  text-transform: none;
	  margin-top: 48rpx;
  }
  .info{
    display: flex;
    align-items:center;
    justify-content: space-between;
    width: 100%;
    height: 80rpx;
    border-bottom: 2rpx solid #f6f6f6;
	position: relative;
	.input_p{
		position: absolute;
		width: 100%;
		height: 100%;
		background: transparent;
	}
	.sendCode{
		position: absolute;
		top: 0;
		bottom: 20rpx;
		right: 40rpx;
		margin: auto;
		display: flex;
		align-items: center;
		justify-content: center;
		color: $base;
	}
    input{
      width: 70%;
      height: 100%;
      font-size: 26rpx;
      color: #222222;
    }
    .more{
      display: flex;
      align-items: center;
      justify-content: flex-end;
      width: 30%;
      height: 100%;
      .iconfont{
        font-size: 34rpx;
      }
      .mo{
        font-size: 26rpx;
        padding-left: 20rpx;
        margin-left: 10rpx;
        border-left: 2rpx solid #EEEEEE;
      }
    }
  }
}
/* 按钮 */
.btn-info{
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100rpx;
  background: #310FFF;
  color: #FFFFFF;
  border-radius: 58rpx 58rpx 58rpx 58rpx;
  .btn{
    display: flex;
    align-items: center;
    justify-content: center;
    width: 90%;
    height: 80rpx;
    border-radius: 100rpx;
    font-size: 28rpx;
	font-weight: 500;
	font-size: 44rpx;
	line-height: 59rpx;
	text-align: left;
	font-style: normal;
	text-transform: none;
    // opacity: 0.4;
  }
}
/* 操作 */
.operation{
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  text{
   font-family: Source Han Sans CN, Source Han Sans CN;
   font-weight: 400;
   font-size: 26rpx;
   color: #666666;
   line-height: 35rpx;
   text-align: left;
   font-style: normal;
   text-transform: none;
  }
}

/* 其他 */
.other-ways{
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 80rpx;
  margin-top: 100rpx;

  text{
    font-size: 28rpx;
    color: #999999;
  }
}
.other-ways::after{
  content: "";
  width: 36%;
  height: 2rpx;
  background-color: #EEEEEE;
}
.other-ways::before{
  content: "";
  width: 36%;
  height: 2rpx;
  background-color: #EEEEEE;
}
/* 登录方式 */
.login-way{
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  // height: 200rpx;
  .way{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 50%;
    height: 100%;
    image{
      width: 100rpx;
      height: 100rpx;
    }
    text{
      font-size: 28rpx;
      color: #959595;
      margin-top: 20rpx;
    }
  }
}
