<template>
	<view class="content">
		<view class="bg_img" :style="{ height: (350 + system_info.statusBarHeight) + 'rpx' }">
			<image :src="currentBgImage" mode="aspectFill"></image>
		</view>
		<view class="heradr_top">
			<view :style="{ height: system_info.statusBarHeight + 'px' }"></view>
			<view class="head-search">
				<view class="search" @click="$fun.jump(`/pages/home/<USER>">

					<view class="hint">
						<text class="min">热门内容</text>
					</view>
					<view class="icon">
						<image :src="$fun.imgUrl('/static/fdj_ico.png')" mode=""></image>
					</view>
				</view>
			</view>
			<view class="content_box">
				<view class="banner1" v-if="swiperList.length > 0">
					<swiper class="screen-swiper square-dot" @change="swiperChange" autoplay indicator-dots="true"
						circular="true" interval="5000" duration="500">
						<swiper-item v-for="(item, index) in swiperList" :key="index"
							@click="item.goods_id * 1 ? $fun.jump(`/pages/home/<USER>''">
							<image :src="$fun.imgUrl(item.image)">
							</image>
						</swiper-item>
					</swiper>
				</view>
			</view>

		</view>
		<view class="menu-nav">
			<scroll-view scroll-x @scroll="ScrollMenu" class="nav-list">
				<view :style="{ display: 'flex', width: `calc(100%*${ListLength})` }">
					<view class="nav" ref="nav" :style="'flex-direction:column'" v-for="(item, index) in navList"
						:key="index">

						<view class="list" style="position: relative;" v-for="(item2, index2) in item"
							@click="navListJump(item2)" :key="index2">
							<image :src="$fun.imgUrl(item2.image)" mode=""></image>
							<text>{{ item2.name }}</text>
							<!-- #ifdef MP-WEIXIN -->
							<button class="leftBox" v-if="item2.keywords == 'kf'" type="default" plain="true"
								open-type="contact">
							</button>
							<!-- #endif -->
						</view>
					</view>
				</view>
			</scroll-view>
			<view class="indicator" v-if="navList.length > 1">
				<view class="plan">
					<view class="bar" :style="'left:' + slideNum + '%'"></view>
				</view>
			</view>
		</view>
		<view class="content_box">
			<view class="inform" v-if="newsList.length > 0">
				<view class="inform-info">
					<view class="picture" @click="$fun.jump(`./messageList1?type=news&name=公告`)">
						<image :src="$fun.imgUrl(`/static/gg_ico.png`)" mode="widthFix"></image>
					</view>
					<view class="info">
						<swiper class="swiper" :circular="true" :vertical="true" :indicator-dots="false"
							:autoplay="true" :interval="3000" :duration="1000">
							<swiper-item v-for="(item, index) in newsList" :key="index">
								<view class="swiper-item" @click="$fun.jump(`./messageInfo?id=${item.id}&name=公告详情`)">
									<text class="one-omit">{{ item.title }}</text>
									<text class="icon iconfont icon-more"></text>
								</view>
							</swiper-item>
						</swiper>
					</view>
				</view>
			</view>
			<view class="class_box" v-if="newsList1.length">
				<image v-for="(item, index) in newsList1" :key="index" :src="$fun.imgUrl(item.image)"
					@click="navListJump(item)" mode="widthFix"></image>
			</view>
			<view class="class_box1">
				<block v-for="(item, index) in newsList2" :key="index">
					<image v-if="index == 0" class="l" :src="$fun.imgUrl(item.image)"
						@click="$fun.jump(`/pages/home/<USER>" mode="widthFix"></image>
				</block>
				<view class="r">
					<block v-for="(item, index) in newsList2" :key="index">
						<image v-if="index != 0" :src="$fun.imgUrl(item.image)"
							@click="$fun.jump(`/pages/home/<USER>" mode="widthFix">
						</image>
					</block>

				</view>


			</view>
			<view class="recommend-info" v-if="goodsList.length > 0">
				<view class="recommend-title">
					<view class="title">
						<image src="/static/wntj_title.png" mode="widthFix"></image>
					</view>
				</view>
				<view class="goods-data">
					<view class="goods-list-left">
						<YWaterfall  :items="leftList" :pin="pin" ref="yWaterfall">
						</YWaterfall>
					</view>
					<view class="goods-list-right">
						<YWaterfall  :items="rightList" :pin="pin" ref="yWaterfall">
						</YWaterfall>
					</view>
				</view>
			</view>
		</view>
		<view style="height: 90rpx;"></view>
		<!-- #ifdef APP-PLUS   -->
		<appUpdate v-if="appUpdateShow" :update_info="update_info" @closeModel="closeModel"></appUpdate>
		<!-- #endif -->
		<TabBar></TabBar>
	</view>
</template>

<script>
	// #ifdef APP-PLUS
	import appUpdate from "@/components/yzhua006-update/app-update.vue";
	// #endif
	import TabBar from '@/components/TabBar/TabBar.vue';
	// 引入mescroll-mixins.js
	import MescrollMixin from "@/components/mescroll-uni/mescroll-mixins.js";
	import {
		checkUpdate
	} from "@/components/yzhua006-update/js/app-update-check.js";
	import YWaterfall from '@/components/y-components/y-waterfall.vue';

	export default {
		mixins: [MescrollMixin], // 使用mixin
		components: {
			TabBar,
			YWaterfall,
			// #ifdef APP-PLUS
			appUpdate
			// #endif
		},
		data() {
			return {
				system_info: {},
				swiperList: [],
				swiperList1: [],
				navList: [],
				goodsList: [],
				goodsList1: [],
				newsList: [],
				newsList1: [],
				newsList2: [],
				store_list: [],
				leftList: [],
				rightList: [],
				ListLength: 1,
				slideNum: 0,
				page: 1,
				currentBgImage: '',
				appUpdateShow: false,
				update_info: {},
				page: 1,
				pin: 0
			}
		},
		onLoad(option) {
			this.system_info = uni.getSystemInfoSync();
			this.init()
			// #ifdef APP-PLUS
			this.getAppInfo(0); //获取线上APP版本信息  参数type 0自动检查  1手动检查（手动检查时，之前取消更新的版本也会提示出来）
			// #endif
		},
		onShow() {

		},
		mounted() {

		},
		// onReachBottom() {
		// 	if (this.page == 1) {
		// 		this.goodsList = []
		// 		return
		// 	}
		// 	this.getIndexGoodsList()
		// },
		methods: {
			/**
			 * 初始化
			 */
			async init(page) {
				await this.getIndexSwipter();
				await this.getIndexNavList();
				await this.getIndexNews();
				await this.getNewsList();
				await this.getIndexGoodsList1();
				await this.getIndexGoodsList();
			},
			getAppInfo(type) {
				const system_info = uni.getSystemInfoSync();
				let params = {
					os: system_info.platform //本机设备操作系统  （android || ios） 
				}
				if (params.os != 'ios' && params.os != 'android') false; //如果不是安卓或ios 返回false
			
				//这里自行请求API获取版本信息 建议传入操作系统标识，返回本机对应的操作系统最新版本信息，也就是安卓的返回就是安卓的版本信息  ios返回就是ios的版本信息
			
				//请求获取最新版本
				this.$fun.ajax.post('config/version', {
					id: 74
				}).then(res => {
					this.update_info = {
						version: res.data.version, //线上版本
						now_url: res.data.pkgUrl, //更新链接
						silent: res.data.silent, //是否是静默更新
						force: res.data.force, //是否是强制更新
						net_check: res.data.net_check, //非WIfi是否提示
						note: res.data.content, //更新内容
					}
					checkUpdate(this.update_info, type, this).then(res => {
						if (res.data) {
							this.appUpdateShow = true
						} else {
							// this.$fun.msg(res.msg)
						}
					}); ///检查更新  
					//checkUpdate 这个方法会做什么？：线上版本号 与 本地版本号做对比 ，如果需要更新  根据静默，强制、wifi等信息执行静默更新或跳转到升级页面
					//跳转升级页面的前提是，需要新建并在pages.json中配置升级页面，配置方法请查看插件详情
				})
			},
			
			
			/**
			 * 首页轮播图
			 */
			getIndexSwipter() {
				this.$fun.ajax.post('News/lists', {
					type: 'index'
				}).then(res => {
					if (res.status == 1) {
						this.swiperList = res.data;
						console.log(res.data[0])
						this.currentBgImage = this.$fun.imgUrl(res.data[0].image)
					}
				})
				this.$fun.ajax.post('News/lists', {
					type: 'index2'
				}).then(res => {
					if (res.status == 1) {
						this.swiperList1 = res.data
					}
				})
			},
			/**
			 * 获取分类
			 */
			getIndexNavList() {
				this.$fun.ajax.post('category/list', {
					type: 'index'
				}).then(res => {
					if (res.status == 1) {
						this.navList = []
						let l = res.data.length / 5 < 1 ? 1 : Math.ceil(res.data.length / 5)
						this.ListLength = l
						let index = 0
						for (var i = 0; i < l; i++) {
							let o = res.data.slice(i * 5, i * 5 + 5)
							console.log(o)
							this.navList.push(o)
						}
					}
				})
			},
			/**
			 * 获取公告
			 */
			getIndexNews() {
				this.$fun.ajax.post('News/lists', {
					type: 'news'
				}).then(res => {
					if (res.status == 1) {
						this.newsList = res.data
					}
				})
			},
			/**
			 * 获取商品列表
			 */
			getIndexGoodsList() {
				this.$fun.ajax.post('goods/list', {
					cid: 'hot',
				}).then(res => {

					if (res.status == 1) {
						if (this.page == 1) {
							this.goodsList = []
						}
						const curList = res.data;
						for (let i = 0; i < curList.length; i++) {
							if (i % 2 == 0) {
								this.leftList.push(curList[i]);
							} else {
								this.rightList.push(curList[i]);
							}
						}
						this.goodsList = this.goodsList.concat(curList); //追加新数据
						if (curList.length > 0) {
							this.page++
						}
					}
				})
			},
			getIndexGoodsList1() {
				this.$fun.ajax.post('goods/list', {
					cid: 'index',
					limit: 3,
				}).then(res => {
					this.goodsList1 = res.data
				})
			},
			ScrollMenu(e) {
				let scrollLeft = e.target.scrollLeft;
				const query = uni.createSelectorQuery().in(this);
				query.select('.nav').boundingClientRect(data => {
					let wid = e.target.scrollWidth - data.width - (data.left * 2 + 5);
					this.slideNum = (scrollLeft / wid * 300) / 2;
				}).exec();
			},
			getNewsList() {
				this.$fun.ajax.post('category/list', {
					type: 'hot'
				}).then(res => {
					if (res.status == 1) {
						this.newsList1 = res.data
					}
				})
				this.$fun.ajax.post('News/lists', {
					type: 'index1'
				}).then(res => {
					if (res.status == 1) {
						this.newsList2 = res.data.reverse();
					}
				})
				this.$fun.ajax.post('business/indexBusiness', {}).then(res => {
					if (res.status == 1) {
						this.store_list = res.data;
					}
				})
			},
			/**
			 * 分类跳转
			 */
			navListJump(item) {
				if (item.keywords == 'video') {
					this.lookAdever()
					return
				}
				let keywords = item.keywords
				if (keywords.indexOf('name') == -1) {
					if (keywords.indexOf('?') == -1) {
						keywords = keywords + '?name=' + item.name
					} else {
						keywords = keywords + '&name=' + item.name
					}
				}
				// if (!uni.getStorageSync('userinfo')) {
				// 	this.$fun.msg('请先登录!')
				// 	return
				// }
				if (keywords == '') {
					// #ifdef APP-PLUS
					this.$fun.msg('开发中.....')
					return
					// #endif
					// #ifdef H5 || MP-WEIXIN
					this.$fun.msg('开发中.....')
					return
					// #endif
				}
				// if(item.mode=='web'){
				// 	this.$fun.jump(keywords,0,0)
				// 	return
				// }
				let isTab = false
				for (var i = 0; i < this.$store.state.tabList.length; i++) {
					if (keywords == this.$store.state.tabList[i].path) {
						isTab = this.$store.state.tabList[i].path
					}
				}
				if (keywords == isTab) {
					this.$fun.jump(keywords, 3, 0)
				} else {
					if (item.mode == 'web') {
						uni.setStorageSync('webUrl', keywords)
						this.$fun.jump('/pages/home/<USER>', 0, 0)
						return
					}
					if (item.mode == 'h5') {
						// #ifdef H5  
						location.href = keywords
						return
						// #endif
						// #ifdef APP-PLUS  
						plus.runtime.openURL(keywords)
						return
						// #endif
					}
					this.$fun.jump(keywords, 0, 0)
				}
			},
			swiperChange(e) {
				const current = e.detail.current
				if (this.swiperList && this.swiperList.length > 0) {
					this.currentBgImage = this.$fun.imgUrl(this.swiperList[current].image)
				}
			}
		}
	};
</script>

<style lang="scss">
	page {
		background: #FFFFFF;

		.content {


			.heradr_top {
				position: relative;
				padding-bottom: 0;
				width: 100%;
				//background: #310FFF;
				z-index: 2;
				overflow: hidden;

				.header_name {
					padding: 20rpx 32rpx;
					font-weight: 400;
					font-size: 32rpx;
					color: #333333;
					// margin-bottom: 40rpx;
				}

				.head-search {
					padding: 20rpx 32rpx;
					// #ifdef MP-WEIXIN
					padding-top: 0;
					width: 500rpx;
					// #endif
					z-index: 10;
					display: flex;
					align-items: center;
					justify-content: space-between;

					.search {
						display: flex;
						align-items: center;
						justify-content: space-between;
						width: 100%;
						padding: 0 20rpx;
						height: 70rpx;
						box-sizing: border-box;
						background: #FFFFFF;
						border-radius: 10rpx;
						border-radius: 38rpx 38rpx 38rpx 38rpx;

						.icon {
							display: flex;
							align-items: center;
							margin-right: 20rpx;

							image {
								width: 40rpx;
								height: 40rpx;
							}
						}

						.hint {
							display: flex;
							align-items: center;

							.max {
								font-size: 30rpx;
								font-weight: bold;
								color: #FFFFFF;
							}

							.min {
								font-size: 24rpx;
								color: #AAAAAA;
								margin-left: 10rpx;
							}
						}
					}
				}

				.heradr_radius {
					position: absolute;
					bottom: -1px;
					border-radius: 40rpx 40rpx 0rpx 0rpx;
					height: 32rpx;
					background: #FFFFFF;
					width: 100%;
				}
			}

			/* 菜单导航 */
			.menu-nav {
				margin-top: 20rpx;
				position: relative;
				margin-left: 24rpx;
				width: calc(100% - 48rpx);
				box-shadow: 0rpx 2rpx 8rpx 0rpx rgba(144, 144, 144, 0.24);
				border-radius: 12rpx 12rpx 12rpx 12rpx;

				.nav-list {
					white-space: nowrap;
					width: 100%;

					.nav {
						display: inline-block;
						display: flex;
						flex-direction: inherit !important;
						flex-wrap: wrap;
						justify-content: flex-start;
						width: 100vw;
					}

					.list {
						display: flex;
						flex-direction: column;
						justify-content: center;
						align-items: center;
						width: 20%;
						height: 130rpx;
						margin-bottom: 20rpx;
						position: relative;

						image {
							width: 76rpx;
							height: 76rpx;
						}

						text {
							font-size: 26rpx;
							color: #333333;
							margin-top: 5rpx;
						}

						.leftBox {
							position: absolute;
							width: 100%;
							height: 100%;
							border: none;
							opacity: 0;
						}
					}
				}

				.indicator {
					position: absolute;
					left: 0;
					bottom: 0;
					display: flex;
					justify-content: center;
					align-items: center;
					width: 100%;
					height: 30rpx;

					.plan {
						position: relative;
						width: 100rpx;
						height: 8rpx;
						border-radius: 8rpx;
						background-color: #e1e1e1;

						.bar {
							position: absolute;
							width: 50%;
							height: 100%;
							border-radius: 6rpx;
							background-color: #310FFF;
						}
					}
				}
			}

			.content_box {
				padding: 20rpx 24rpx;
				padding-bottom: 0;

				.banner1 {
					height: 258rpx;
					border-radius: 10rpx;
					overflow: hidden;

					.screen-swiper {
						height: 258rpx;
						min-height: 100% !important;

						image {
							width: 100%;
							height: 258rpx;
							border-radius: 10rpx;
						}
					}
				}

				/* 通知 */
				.inform {
					margin: 20rpx 0;

					.inform-info {
						display: flex;
						padding: 0 20rpx;
						height: 72rpx;
						border-radius: 8rpx 8rpx 8rpx 8rpx;
						border: 2rpx solid #4F874F;

						.picture {
							margin-right: 10rpx;
							display: flex;
							align-items: center;

							image {
								width: 86rpx;
								height: 40rpx;
							}
						}

						.info {
							width: 90%;
							height: 100%;

							.swiper {
								width: 100%;
								height: 100%;

								.swiper-item {
									display: flex;
									align-items: center;
									justify-content: space-between;
									width: 100%;
									height: 100%;

									text {
										font-size: 28rpx;
										color: #C6C6C6;
									}

									.icon {
										color: #d9cfcf !important;
									}
								}
							}
						}
					}
				}

				.class_box {
					margin-top: 20rpx;
					display: flex;
					justify-content: space-between;
					flex-wrap: wrap;

					image {
						width: 100%;
						margin-bottom: 20rpx;
						border-radius: 20rpx;
					}
				}

				.class_box1 {
					display: flex;
					justify-content: space-between;
					flex-wrap: wrap;

					image.l {
						width: 344rpx;
						border-radius: 10rpx;
					}

					.r {
						display: flex;
						justify-content: space-between;
						flex-direction: column;

						image {
							width: 344rpx;
							margin: 0;
							border-radius: 10rpx;
						}
					}


				}

				.selected_box {
					width: 100%;

					image {
						width: 100%;
						margin: 0;
						padding: 0;
					}

					.selected_shop {
						margin-top: 20rpx;
						width: calc(100% - 12rpx);
						margin: 0 auto;
						margin-top: -9rpx;
						padding: 9rpx 20rpx;
						background: #8554E6;
						display: flex;
						justify-content: space-between;
						padding-bottom: 20rpx;

						.selected_item {
							width: 208rpx;

							image {
								width: 208rpx;
								height: 208rpx;
								border-radius: 8rpx 8rpx 0rpx 0rpx;
							}

							.shop_info {
								margin-top: -9rpx;
								padding: 12rpx;
								width: 208rpx;
								background: #FFFFFF;
								border-radius: 0 0 8rpx 8rpx;

								.title {
									font-weight: 500;
									font-size: 20rpx;
									color: #333333;
									line-height: 23rpx;
									width: 100%;
									overflow: hidden;
									text-overflow: ellipsis;
									display: -webkit-box;
									-webkit-line-clamp: 2;
									-webkit-box-orient: vertical;
								}

								.price {
									margin-top: 10rpx;
									font-weight: 500;
									font-size: 24rpx;
									color: #FF512E;
								}

								.ys {
									font-weight: 500;
									font-size: 16rpx;
									color: #B6B6B6;
								}
							}
						}
					}

					.selected_shop:last-child {
						border-radius: 0 0 16rpx 16rpx;

					}
				}

				.selected_box1 {
					margin-top: 20rpx;
					width: 100%;
					position: relative;

					.selected_img {
						position: relative;

						image {
							width: 100%;

						}

						.shop_top {
							position: absolute;
							bottom: 0;
							left: 0;
							width: 100%;
							height: 40rpx;
							background: #FFF0EB;
							border-radius: 16rpx 16rpx 0 0;
						}

					}

					.selected_shop {
						padding: 9rpx 20rpx;
						background: #FFF0EB;
						z-index: 10;
						// border-radius: 0 0 16rpx 16rpx;

						padding-bottom: 20rpx;

						.shop_box {
							display: flex;
							flex-wrap: wrap;
							justify-content: space-between;
						}

						.shop_store {
							width: 100%;
							display: flex;
							justify-content: space-between;
							align-items: center;

							.l {
								display: flex;
								align-items: center;

								image {
									width: 88rpx;
									height: 88rpx;
									border-radius: 50%;
								}

								.store_info {
									margin-left: 20rpx;
									margin-bottom: 20rpx;

									.name {
										font-weight: 500;
										font-size: 28rpx;
										color: #333333;
									}
								}
							}


							.r {
								width: 98rpx;
								height: 40rpx;
								color: #FFFFFF;
								background: #ED271D;
								text-align: center;
								line-height: 40rpx;
								font-weight: 500;
								font-size: 24rpx;
								color: #FFFFFF;
								border-radius: 20rpx 20rpx 20rpx 20rpx;
							}
						}

						.selected_item {
							width: 208rpx;

							image {
								width: 208rpx;
								height: 208rpx;
								border-radius: 8rpx 8rpx 0rpx 0rpx;
							}

							.shop_info {
								margin-top: -9rpx;
								padding: 12rpx;
								width: 208rpx;
								background: #FFFFFF;
								border-radius: 0 0 8rpx 8rpx;

								.title {
									font-weight: 500;
									font-size: 20rpx;
									color: #333333;
									line-height: 23rpx;
									width: 100%;
									overflow: hidden;
									text-overflow: ellipsis;
									display: -webkit-box;
									-webkit-line-clamp: 2;
									-webkit-box-orient: vertical;
								}

								.price {
									margin-top: 10rpx;
									font-weight: 500;
									font-size: 24rpx;
									color: #FF512E;
								}

								.ys {
									font-weight: 500;
									font-size: 16rpx;
									color: #B6B6B6;
								}
							}
						}
					}

					.selected_shop:last-child {
						margin-bottom: 20rpx;
					}
				}

				.banner2 {
					margin-top: 20rpx;
					height: 258rpx;
					border-radius: 10rpx;
					overflow: hidden;

					.screen-swiper {
						height: 258rpx;
						min-height: 100% !important;

						image {
							width: 100%;
							height: 258rpx;
							border-radius: 10rpx;
						}
					}
				}

				.goods-list {
					margin-top: 20rpx;
					display: flex;
					flex-wrap: wrap;
					justify-content: space-between;
					// padding: 0 20rpx;

					.list {
						// padding: 20rpx 0;
						width: 49%;
						margin-bottom: 20rpx;
						background: #FFFFFF;
						box-shadow: 0rpx 2rpx 8rpx 0rpx rgba(0, 0, 0, 0.25);
						border-radius: 12rpx 12rpx 12rpx 12rpx;
						display: flex;
						flex-direction: column;

						.pictrue {
							display: flex;
							justify-content: center;
							align-items: center;
							// padding: 16rpx;

							image {
								width: 342rpx;
								height: 342rpx;
								border-radius: 12rpx 12rpx 0rpx 0rpx;
							}
						}

						.shop {
							display: flex;
							justify-content: space-between;
							flex-direction: column;
						}

						.title-tag {
							width: 100%;
							padding: 20rpx;

							.tag {
								font-family: Source;
								font-weight: 500;
								font-size: 28rpx;
								color: #333333;
								font-style: normal;
								text-transform: none;
								overflow: hidden;
								text-overflow: ellipsis;
								display: -webkit-box;
								-webkit-line-clamp: 2;
								-webkit-box-orient: vertical;
								white-space: normal;


								text {
									font-size: 24rpx;
									color: #FFFFFF;
									padding: 4rpx 16rpx;
									background: linear-gradient(to right, $base, $change-clor);
									border-radius: 6rpx;
									margin-right: 10rpx;
								}
							}
						}

						.tag_text {
							padding: 0 20rpx;
							font-family: Source;
							font-weight: 400;
							font-size: 20rpx;
							font-size: 20rpx;
							color: #AAAAAA;
							text-align: left;
							font-style: normal;
							text-transform: none;
						}

						.price-info {
							display: flex;
							flex-direction: column;
							margin: 10rpx 20rpx;
							box-sizing: border-box;
							border-radius: 8rpx 8rpx 8rpx 8rpx;
							display: flex;
							justify-content: space-between;

							.user-price {
								padding: 0 10rpx;
								display: flex;
								align-items: baseline;
								margin-right: 10rpx;

								text {
									color: #FFFFFF;
								}

								.min {
									font-family: Source;
									font-weight: 500;
									font-weight: 500;
									font-size: 28rpx;
									color: #FF512E;
									text-align: left;
									font-style: normal;
									text-transform: none;
								}

								.max {
									font-family: Source;
									font-weight: 500;
									font-weight: 500;
									font-size: 28rpx;
									color: #FF512E;
									text-align: left;
									font-style: normal;
									text-transform: none;
								}
							}

							.tag_text {
								font-weight: 500;
								font-size: 28rpx;
								color: #B6B6B6;
							}

							.vip-price {
								height: 60rpx;
								box-sizing: border-box;
								display: flex;
								justify-content: center;
								font-family: Source;
								align-items: center;
								padding-right: 14rpx;
								border-radius: 132rpx 132rpx 132rpx 132rpx;

								image {
									width: 26rpx;
									height: 26rpx;
									margin-right: 10rpx;
								}

								text {
									font-size: 24rpx;
									color: #FFFFFF;
								}
							}
						}
					}
				}
			}
		}
	}

	.price1 {
		margin: 10rpx 0;
		color: #FFFFFF;
		display: flex;
		width: fit-content;
		align-items: center;
		background-color: #cfb06a;

		.min {
			padding-left: 10rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			height: 100%;
			font-size: 24rpx;
		}

		.max {
			border-radius: 30rpx 0 0 0;
			display: flex;
			align-items: center;
			justify-content: center;
			height: 100%;
			padding: 0 10rpx;
			background: #f3e9d0;
			margin-left: 10rpx;
			color: #F15232;
			font-size: 24rpx;
		}
	}

	.bg_img {
		width: 100%;
		height: 350rpx;
		position: absolute;
		left: 0;
		top: 0;

		image {
			width: 100%;
			height: 100%;
			filter: blur(20rpx);
			z-index: 1;
		}
	}

	.goods-data {
		width: 100%;
		// padding: 0 25rpx;
		display: flex;
		justify-content: space-between;

		.goods-list-left {
			width: 49%;
		}

		.goods-list-right {
			width: 49%;
		}
	}

	/* 为你推荐 */
	.recommend-info {
		width: 100%;

		.recommend-title {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100%;
			.title {
				display: flex;
				align-items: center;

				image {
					width: 148.02rpx;
				}
			}
		}
	}
</style>