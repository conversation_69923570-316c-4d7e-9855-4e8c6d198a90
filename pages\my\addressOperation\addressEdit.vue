<template>
	<view class="page">
		<view class="address-input">
			<view class="list-input">
				<view class="title">
					<text>收货人</text>
				</view>
				<view class="content">
					<input type="text" v-model="addressInfo.name" placeholder="请填写收货人姓名">
				</view>
			</view>
			<view class="list-input">
				<view class="title">
					<text>手机号</text>
				</view>
				<view class="content">
					<input type="tel" v-model="addressInfo.mobile" placeholder="请填写收货人手机号">
				</view>
			</view>
			<view class="list-input">
				<view class="title">
					<text>所在地区</text>
				</view>
				<view class="content" @click="openPicker" >
					{{address?address:'请选择地址'}}
				</view>
			</view>
			<view class="list-textarea">
				<view class="title">
					<text>详细地址</text>
				</view>
				<view class="content">
					<textarea type="text" v-model="addressInfo.address"  placeholder="街道/楼牌号等" />
				</view>
			</view>
		</view>
		<view class="tag-default">
			<view class="default-address">
				<view class="title">
					<text>默认地址</text>
				</view>
				<view class="switch-default">
					<switch  class="red sm"  @change="switchChange" color="#310FFF !important" :checked="addressInfo.status==1"></switch>
				</view>
			</view>
		</view>
		<lotusAddress v-on:choseVal="choseValue" :lotusAddressData="lotusAddressData"></lotusAddress>
		<view class="footer-btn">
			<view class="btn" @click="saveAddress()">
				<text>保存</text>
			</view>
		</view>
	</view>
</template>

<script>
	import lotusAddress from "@/components/Winglau14-lotusAddress/Winglau14-lotusAddress.vue";
	export default {
		components: {
			lotusAddress
		},
		data() {
			return {
				addressType: '2',
				addressInfo:{
					address:'',
					area:'',
					city:'',
					mobile:'',
					name:'',
					pro:'',
					status:0
				},
				address:'',
				id:null,
				lotusAddressData: {
					visible: false,
					provinceName: '',
					cityName: '',
					townName: '',
				},
			};
		},
		onLoad(params) {
			this.addressType = params.type||'2';
			uni.setNavigationBarTitle({
				title: this.addressType === '1' ? '编辑收货地址':'新建收货地址'
			})
			if(this.addressType==1){
				this.id = params.id
				this.getAddressInfo(params.id)
			}
		},
		methods:{
			//打开picker
			openPicker() {
				this.lotusAddressData.visible = true;
			},
			switchChange(e){
				this.addressInfo.status=e.detail.value?1:0
			},
			saveAddress(){
				if (!this.addressInfo.name) {
					this.$fun.msg('请输入收货人姓名')
					return false
				}
				if (!this.addressInfo.mobile) {
					this.$fun.msg('请输入联系人手机号')
					return false
				}
				if (!this.address) {
					this.$fun.msg("请选择地址");
					return false;
				}
				if (!this.addressInfo.address) {
					this.$fun.msg("请填写详细地址");
					return false;
				}
					let prams = {
						address:this.addressInfo.address,
						area:this.addressInfo.area,
						city:this.addressInfo.city,
						mobile:this.addressInfo.mobile,
						name:this.addressInfo.name,
						pro:this.addressInfo.pro,
						status:this.addressInfo.status,
					}		
					if(this.addressType==1){
						prams.id=this.addressInfo.id
					}
				this.$fun.ajax.post('address/add', prams).then(res => {
					this.$fun.msg(res.msg)
					if (res.status == 1) {
						this.$fun.msg(res.msg)
						this.$fun.jump('',4)
					}
				})
			},
			//回传已选的省市区的值
						choseValue(res) {
							//res数据源包括已选省市区与省市区code
							console.log(res);
							//res.isChose = 1省市区已选 res.isChose = 0;未选
							if (res.isChose) {
								this.lotusAddressData.visible = res.visible; //visible为显示与关闭组件标识true显示false隐藏
								this.lotusAddressData.provinceName = res.province; //省
								this.lotusAddressData.cityName = res.city; //市
								this.lotusAddressData.townName = res.town; //区
								this.addressInfo.pro = res.province; //省
								this.addressInfo.city = res.city; //市
								this.addressInfo.area = res.town; //区
								this.address = `${res.province} ${res.city} ${res.town}`; //region为已选的省市区的值
								this.$forceUpdate()
							} else {
								this.$fun.msg('请完整选择地址')
							}
						},
			getAddressInfo(id){
				this.$fun.ajax.post('address/get', {id}).then(res => {
					if(res.status==1){
						this.addressInfo = res.data
						this.address = `${res.data.pro} ${res.data.city} ${res.data.area}`
					}
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	.page{
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		background-color: #FFFFFF;
	}
	
	
	.address-input{
		width: 100%;
		background-color: #FFFFFF;
		.list-input{
			display: flex;
			align-items: center;
			padding: 0 4%;
			height: 100rpx;
			border-bottom: 2rpx solid #f6f6f6;
			.title{
				display: flex;
				align-items: center;
				width: 20%;
				height: 100%;
				text{
					color: #222222;
					font-size: 26rpx;
				}
			}
			.content{
				display: flex;
				align-items: center;
				width: 70%;
				height: 100%;
				input{
					width: 100%;
					height: 100%;
					font-size: 26rpx;
					color: #222222;
				}
			}
		}
		.list-textarea{
			display: flex;
			align-items: center;
			padding: 0 4%;
			height: 200rpx;
			border-bottom: 2rpx solid #f6f6f6;
			.title{
				display: flex;
				width: 20%;
				height: 80%;
				text{
					color: #222222;
					font-size: 26rpx;
				}
			}
			.content{
				display: flex;
				align-items: center;
				width: 70%;
				height: 100%;
				textarea{
					width: 100%;
					height: 80%;
					font-size: 26rpx;
					color: #222222;
				}
			}
		}
	}
	
	.tag-default{
		width: 100%;
		border-top: 20rpx solid #f6f6f6;
		.tag-list{
			display: flex;
			align-items: center;
			padding: 0 4%;
			height: 200rpx;
			.title{
				width: 20%;
				height: 80%;
				text{
					font-size: 26rpx;
					color: #222222;
				}
			}
			.content{
				display: flex;
				width: 70%;
				height: 80%;
				.list{
					display: flex;
					align-items: center;
					justify-content: center;
					min-width: 120rpx;
					height: 60rpx;
					border: 2rpx solid #f6f6f6;
					border-radius: 60rpx;
					margin-right: 20rpx;
					text{
						color: #555555;
						font-size: 24rpx;
					}
				}
				.action{
					background-color: $base;
					border: 2rpx solid $base;
					text{
						color: #FFFFFF;
					}
				}
			}
		}
		.default-address{
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 4%;
			height: 100rpx;
			.title{
				display: flex;
				align-items: center;
				width: 20%;
				height: 80%;
			}
			.switch-default{
				uni-switch .uni-switch-input{
					background: #22AA44 !important;
				}
			}
		}
	}
	
	.footer-btn{
		position: fixed;
		left: 0;
		bottom: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 100rpx;
		background-color: #FFFFFF;
		.btn{
			display: flex;
			align-items: center;
			justify-content: center;
			width: 80%;
			height: 70rpx;
			background: linear-gradient(to right,$base,$change-clor);
			border-radius: 70rpx;
			box-shadow: 0 10rpx 10rpx $base;
			text{
				font-size: 28rpx;
				color: #FFFFFF;
			}
		}
	}
</style>
