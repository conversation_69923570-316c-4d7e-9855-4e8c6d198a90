<template>
	<view class="page">
		<!-- 订单tab -->
		<view class="order-tab">
			<view class="tab" :class="{'action':OrderType=='all'}" @click="onOrderTab('all')">
				<text>全部</text>
				<text class="line"></text>
			</view>
			<view class="tab" :class="{'action':OrderType==0}" @click="onOrderTab(0)">
				<text>审核中</text>
				<text class="line"></text>
			</view>
			<view class="tab" :class="{'action':OrderType==1}" @click="onOrderTab(1)">
				<text>已通过</text>
				<text class="line"></text>
			</view>
			<view class="tab" :class="{'action':OrderType==2}" @click="onOrderTab(2)">
				<text>已拒绝</text>
				<text class="line"></text>
			</view>
		</view>
		<mescroll-body ref="mescrollRef" @init="mescrollInit" @down="downCallback" @up="upCallback" :down="downOption"
			:up="upOption" :top="0">
			<!-- 订单列表 -->
			<view class="goods-data">
				<view class="goods-list">
					<view :class="'list-li'" v-for="(item,index) in cartList" :key="index">
						<view class="thumb">
							<image :src="$fun.imgUrl(item.zimage)" mode="heightFix"></image>
						</view>
						<view class="item">
							<view class="title" style="display: flex;justify-content: space-between;">
								<text class="two-omit" style="font-size: 16px;font-weight: bold;">{{item.title}}</text>
								<text class="two-omit"
									style="text-align: right;color: #310FFF;">{{item.status==0?'审核中':item.status==1?'已通过':item.status==2?'已拒绝':''}}</text>
							</view>
						<!-- 	<view class="title" style="display: flex;justify-content: space-between;margin-top: 20rpx;">
								<text class="two-omit" style="color:#959595;">浏览量{{item.browse}}</text>
								<text class="two-omit" style="color:#959595;">剩余浏览{{item.paynum}}</text>
							</view> -->

							<view class="price" v-if="item.status==2">
								<view class="retail-price">
									<text class="max">拒绝原因:{{item.refuse}}</text>
								</view>
							</view>
							<view class="title"
								style="display: flex;justify-content: flex-end;margin-top: 20rpx;text-align: right;">
								<text class="two-omit"
									style="color:#959595;">{{$u.timeFormat(item.updatetime*1000, 'yyyy-mm-dd hh:MM')}}</text>
							</view>
							<view class="status-btn" v-if="item.status ==1">
								<view class="btn action" v-if="item.type==0" @click.stop="setType(item.id,1)">
									<text>上架</text>
								</view>
								<view v-else class="btn action" @click.stop="setType(item.id,0)">
									<text>下架</text>
								</view>
								<view class="btn action" @click.stop="setType(item.id,2)">
									<text>删除</text>
								</view>
								<view class="btn action" @click.stop="$fun.jump(`/pagesB/releaseProject/releaseProject?name=编辑项目&id=${item.id}`,0,0)">
									<text>编辑</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>

		</mescroll-body>
		<z-modal :show="modalControl" :btnGroup="btnGroup" :cont1="cont1" :contentType="2" :titleText="'增加浏览量'"
			:placeholderText="'增加浏览量'" @cancle="cancle" @sure="sure">
		</z-modal>
		<!-- 提示框 -->
		<DialogBox ref="DialogBox"></DialogBox>
	</view>
</template>

<script>
	import zModal from '@/components/z-modal/z-modal.vue'
	// 引入mescroll-mixins.js
	import MescrollMixin from "@/components/mescroll-uni/mescroll-mixins.js";
	export default {
		components: {
			zModal
		},
		mixins: [MescrollMixin], // 使用mixin
		data() {
			return {
				mescroll: null, // mescroll实例对象 (此行可删,mixins已默认)
				// 下拉刷新的配置(可选, 绝大部分情况无需配置)
				downOption: {},
				// 上拉加载的配置(可选, 绝大部分情况无需配置)
				upOption: {},
				cartList: [],
				OrderType: 'all',
				type: '',
				modalControl: false,
				btnGroup: [{
						text: '取消',
						color: '#FFFFFF',
						bgColor: '#999999',
						width: '150rpx',
						height: '80rpx',
						shape: 'fillet',
						eventName: 'cancle'
					},
					{
						text: '确定',
						color: '#FFFFFF',
						bgColor: '#007AFF',
						width: '150rpx',
						height: '80rpx',
						shape: 'fillet',
						eventName: 'sure'
					}
				],
				cont1: "",
				pid: ''
			}
		},
		onLoad(params) {
			uni.setNavigationBarTitle({
				title: params.name
			})
			this.type = params.type

		},
		methods: {
			onOrderTab(type) {
				this.OrderType = type
				this.downCallback()
			},
			showModel(item) {
				this.pid = item.id
				this.modalControl = true
			},
			sure(e) {
				if (e.inputText == '') {
					this.$fun.msg('请输入增加额度')
					return;
				}
				this.$fun.ajax.post('project/setPayNum', {
					num: e.inputText,
					pid: this.pid
				}).then(res => {
					if (res.status == 1) {
						this.$fun.msg(res.msg);
						this.modalControl = false
						setTimeout(() => {
							this.downCallback()
						}, 1200)
					}
				})
			},
			cancle(e) {
				this.modalControl = false
			},
			getstatusTip(item) {
				let status = item.status
				let tip = ""
				if (item.pin == 1) {
					status = item.pinstatus
					tip = status == 1 ? '拼团成功' : status == 2 ? '拼团失败' : '拼团中'
				} else {
					tip = status == 0 ? '待支付' : status == 1 ? '待发货' : status == 2 ? '待收货' : status == 3 ? '已完成' : status ==
						4 ?
						'已取消' : ''
				}

				return tip
			},
			/*下拉刷新的回调, 有三种处理方式:*/
			downCallback() {
				this.cartList = []
				this.mescroll.resetUpScroll(false);
			},
			/*上拉加载的回调*/
			upCallback(e) {
				this.mescroll.removeEmpty();
				const data = {
					page: e.num,
					limit: e.size,
					status: this.OrderType,
					type: this.type
				};
				this.$fun.ajax.post('project/getPayNum', {}).then(res => {
					if (res.status == 1) {
						this.cont1 = res.data.paynum
					}
				})
				this.$fun.ajax.post('project/myIndex', data).then(res => {
					if (res.status == 1) {
						const curList = res.data;
						if (e.num === 1) {
							this.cartList = [];
							//第一页清空数据重载
							if (curList.length > 0) {
								uni.pageScrollTo({
									scrollTop: 0,
									duration: 200
								});
							}
						}
						this.cartList = this.cartList.concat(curList); //追加新数据
						this.mescroll.endSuccess(curList.length); //结束加载状态
						console.log(curList.length)
					}
				})
			},
			/**
			 * 确认收货
			 */
			setType(pid, type) {
				uni.showModal({
					content: '确认要执行该操作吗?',
					success: e => {
						if (e.confirm) {
							if(type==2){
								this.$fun.ajax.post('project/delProject', {
									pid,
								}).then(res => {
									if (res.status == 1) {
										this.$fun.msg(res.msg);
										this.list = [];
										this.mescroll.resetUpScroll();
									}
								})
								return
							}
							this.$fun.ajax.post('project/setType', {
								pid,
								type
							}).then(res => {
								if (res.status == 1) {
									this.$fun.msg(res.msg);
									this.list = [];
									this.mescroll.resetUpScroll();
								}
							})
						}
					}
				});
			},
			/**
			 * 取消订单
			 */
			cancellation(oid) {
				console.log(oid)
				// uni.showModal({

				// })
				uni.showModal({
					content: '确认取消订单吗?',
					success: e => {
						if (e.confirm) {
							this.$fun.ajax.post('order/delOrder', {
								oid
							}).then(res => {
								if (res.status == 1) {
									this.$fun.msg(res.msg);
									this.cartList = [];
									this.mescroll.resetUpScroll();
								}
							})
						}
					}
				})
			},
		}
	}
</script>

<style scoped lang="scss">
	.page {
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		background-color: #f6f6f6;
	}

	/* 顶部返回 */
	.head-back {
		position: fixed;
		left: 0;
		top: 0;
		z-index: 10;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 100rpx;
		background-color: #FFFFFF;
		/* #ifdef APP-PLUS */
		height: calc(50rpx + var(--status-bar-height));
		padding-top: var(--status-bar-height);
		/* #endif */
		/* #ifdef MP */
		height: 150rpx;
		padding-top: 20rpx;

		/* #endif */
		.back {
			position: absolute;
			left: 0;
			top: 0;
			/* #ifdef APP-PLUS */
			padding-top: var(--status-bar-height);
			/* #endif */
			/* #ifdef MP */
			padding-top: 20rpx;
			/* #endif */
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100rpx;
			height: 100%;

			text {
				width: 20rpx;
				height: 20rpx;
				border-left: 2rpx solid #555555;
				border-bottom: 2rpx solid #555555;
				transform: rotate(45deg);
			}
		}

		.title {
			display: flex;
			align-items: center;

			text {
				font-size: 28rpx;
				color: #222222;
			}
		}

		.more-icon {
			position: absolute;
			right: 0;
			top: 0;
			/* #ifdef APP-PLUS */
			right: 0rpx;
			padding-top: var(--status-bar-height);
			/* #endif */
			/* #ifdef MP */
			right: 220rpx;
			padding-top: 20rpx;
			/* #endif */
			display: flex;
			align-items: center;
			height: 100%;

			.icon-list {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 80rpx;
				height: 100%;

				text {
					font-size: 34rpx;
					color: #222222;
				}
			}
		}
	}

	/* 订单tab */
	.order-tab {
		position: fixed;
		left: 0;
		// top: 100rpx;
		/* #ifdef APP-PLUS */
		// top: calc(50rpx + var(--status-bar-height));
		/* #endif */
		z-index: 10;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 100rpx;
		background-color: #FFFFFF;

		.tab {
			position: relative;
			display: flex;
			align-items: center;
			justify-content: center;
			width: 20%;
			height: 80%;

			text {
				font-size: 26rpx;
				color: #959595;
			}
		}

		.action {
			text {
				color: #222222;
			}

			.line {
				position: absolute;
				left: 50%;
				bottom: 0;
				width: 60rpx;
				height: 6rpx;
				background: linear-gradient(to right, $base, #f6f6f6);
				transform: translate(-50%, 0);
			}
		}
	}

	.goods-data {
		width: 100%;
		margin-top: 150rpx;
		/* #ifdef APP-PLUS */
		margin-top: 150rpx;
		/* #endif */
		/* #ifdef MP */
		margin-top: calc(320rpx + var(--status-bar-height));

		/* #endif */
		.goods-list {
			padding: 0 25rpx;
			border-radius: 20rpx;
			overflow: hidden;

			.list-view {
				float: left;
				width: 49%;
				height: 560rpx;
				background-color: #ffffff;
				border-radius: 20rpx;
				margin-right: 2%;
				margin-bottom: 20rpx;
				overflow: hidden;

				.thumb {
					width: 100%;
					//height: 300rpx;
					overflow: hidden;

					image {
						height: 200rpx;
					}
				}

				.item {
					width: 100%;

					.title {
						padding: 0 20rpx;

						text {
							width: 100%;
							color: #212121;
							font-size: 26rpx;
						}
					}

					.price {
						padding: 0 20rpx;

						.retail-price {
							display: flex;
							align-items: flex-end;
							width: 100%;
							height: 40rpx;

							.min {
								display: inline-block;
								font-size: 24rpx;
								color: $base;
								font-weight: bold;
								transform: scale(0.7);
							}

							.max {
								font-size: 28rpx;
								color: $base;
								font-weight: bold;
							}

							.tag {
								position: relative;
								background-color: $base;
								border-radius: 4rpx;
								margin-left: 10rpx;

								text {
									display: inline-block;
									color: #ffffff;
									font-size: 24rpx;
									transform: scale(0.7);
								}
							}

							.tag:before {
								position: absolute;
								left: -6rpx;
								top: 0;
								content: '';
								width: 0;
								height: 0;
								border-top: 0rpx solid transparent;
								border-right: 10rpx solid $base;
								border-bottom: 6rpx solid transparent;
							}
						}

						.vip-price {
							display: flex;
							align-items: flex-end;
							width: 100%;
							height: 40rpx;

							.min {
								display: inline-block;
								font-size: 24rpx;
								color: #212121;
							}

							.max {
								font-size: 24rpx;
								color: #212121;
							}
						}
					}
				}
			}

			.list-view:nth-child(2n) {
				margin-right: 0;
			}

			// 列表
			.list-li {
				display: flex;
				align-items: center;
				width: 100%;
				// height: 300rpx;
				padding: 20upx 0;
				background-color: #ffffff;

				.thumb {
					display: flex;
					align-items: center;
					justify-content: center;
					width: 30%;
					height: 100%;

					image {
						width: 200rpx;
						height: 200rpx;
						border-radius: 10rpx;
					}
				}

				.item {
					display: flex;
					flex-direction: column;
					justify-content: space-between;
					width: 70%;
					height: 100%;
					border-bottom: 2rpx solid #f6f6f6;

					.title {
						padding: 0 20rpx;

						text {
							width: 100%;
							color: #212121;
							font-size: 26rpx;
						}
					}

					.price {
						padding: 0 20rpx;

						.retail-price {
							display: flex;
							align-items: flex-end;
							width: 100%;
							height: 40rpx;

							.min {
								display: inline-block;
								font-size: 24rpx;
								color: $base;
								font-weight: bold;
								transform: scale(0.7);
							}

							.max {
								font-size: 28rpx;
								color: $base;
								font-weight: bold;
							}

							.tag {
								position: relative;
								background-color: $base;
								border-radius: 4rpx;
								margin-left: 10rpx;

								text {
									display: inline-block;
									color: #ffffff;
									font-size: 24rpx;
									transform: scale(0.7);
								}
							}

							.tag:before {
								position: absolute;
								left: -6rpx;
								top: 0;
								content: '';
								width: 0;
								height: 0;
								border-top: 0rpx solid transparent;
								border-right: 10rpx solid $base;
								border-bottom: 6rpx solid transparent;
							}
						}

						.vip-price {
							display: flex;
							align-items: flex-end;
							width: 100%;
							height: 40rpx;

							.min {
								display: inline-block;
								font-size: 24rpx;
								color: #212121;
							}

							.max {
								font-size: 24rpx;
								color: #212121;
							}
						}
					}
				}
			}
		}
	}

	.status-btn {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		width: 100%;
		height: 100rpx;
		padding: 0 20upx;

		.btn {
			padding: 10rpx 30rpx;
			border: 2rpx solid #EEEEEE;
			border-radius: 100rpx;
			margin-left: 20rpx;

			text {
				font-size: 26rpx;
				color: #555555;
			}
		}

		.action {
			border: 2rpx solid $base;

			text {
				color: $base;
			}
		}
	}
</style>
