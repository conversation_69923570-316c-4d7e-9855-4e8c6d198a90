/**
* @Description: 爱上疆鸡毛换糖商城
* @Author: 梧桐
* @Copyright: 武汉一一零七科技有限公司©版权所有
* @Link: www.wo-shop.net
* @Contact: QQ:2487937004
*/
<template>
	<view class="index_class">


		<view class="all-order">
			<!--  app 端状态栏站位 -->
			<!-- <mescroll-body ref="mescrollRef" @init="mescrollInit" @down="downCallback" @up="upCallback"
				:down="downOption" :up="upOption" :top="0"> -->
			<view class="box" >
				<view class="title_t"  style="background: linear-gradient( 180deg, rgba(255,232,111,.28) 0%, #F6F6F6 100%);">
					<view class="title">
						社区详情
						<image :src="$fun.imgUrl('/static/yj.png')" mode="widthFix" style="width: 24rpx;margin-left: 10rpx;"></image>
					</view>
				</view>
				<view class="bus-top">
					<view class="b" :style="{'borderRight':index%2==1?'none':'1px solid #E2E2E2'}"
						v-for="(item, index) in mList" :key="index">
						<view class="n">{{ item.name}}:</view>
						<view class="v">{{ item.value*1 }}</view>
					</view>
				</view>
			</view>
			<!-- </mescroll-body> -->
		</view>
	</view>
</template>

<script>
	let zf_number = []; // 支付订单号
	let iconarr = []; // 合并支付订单号
	let orderNumber = "";
	let backindex = "";
	let payOrderNumer;
	import MescrollUni from "@/components/mescroll-uni/mescroll-uni.vue";

	export default {
		components: {
			MescrollUni
		},
		data() {
			return {
				showingIndex: 1,
				list: [],
				valShow: false,
				numberpay: false, // 合并付款
				valueStatus: 0, // 更新页面状态 防止页面卡死
				mescroll: null, //mescroll实例对象
				upOption: {
					page: {
						num: 0, // 当前页码,默认0,回调之前会加1,即callback(page)会从1开始
						size: 10 // 每页数据的数量
					},
					noMoreSize: 4, //如果列表已无数据,可设置列表的总数量要大于半页才显示无更多数据;避免列表数据过少(比如只有一条数据),显示无更多数据会不好看; 默认5
					empty: {
						tip: "~暂无数据~", // 提示
						icon: 'https://woshop-1258844920.cos.ap-nanjing.myqcloud.com/static/images/nullStatus/noList.png'
					},
					wechatMiniProgram: 0,
				},
				totalPrice: 0,
				mList: [],
				mescroll: null, // mescroll实例对象 (此行可删,mixins已默认)
				// 下拉刷新的配置(可选, 绝大部分情况无需配置)
				downOption: {},
				// 上拉加载的配置(可选, 绝大部分情况无需配置)
				upOption: {},
				cartList: [],
				str: '',
				type: 'team',
				tab_list: [{
					name: '我的好友',
					type: 1
				}, {
					name: '合作伙伴',
					type: 2
				}, {
					name: '我的店铺',
					type: 3
				}],
				current: 0,
				type: 1,
			};
		},
		onLoad(option) {
			if (option.type) {
				this.type = option.type
			}
			if (option.name) {
				uni.setNavigationBarTitle({
					title: option.name
				});
			}
			this.list = [];
			this.mescroll && this.mescroll.resetUpScroll();
			//#ifdef MP-WEIXIN
			this.wechatMiniProgram = 1;
			//#endif

		},
		onShow() {
			this.upCallback();
		},

		watch: {
			valShow() {
				this.statusMath(this);
			}

		},
		onReachBottom: function() {
			this.mescroll && this.mescroll.onReachBottom();
		},
		//注册列表滚动事件,用于下拉刷新
		onPageScroll(e) {
			this.mescroll && this.mescroll.onPageScroll(e);
		},
		onBackPress() {
			if (backindex == 2) {
				uni.switchTab({
					url: "/pages/home"
				});
				return true;
			}
		},
		methods: {
			change(e) {
				this.current = e
				this.type = this.tab_list[e].type;
				this.upCallback({
					num: 1,
					size: 15
				})
			},
			toPt(order_num) {
				this.getnav(`/pagesC/goods/assemble?order_num=${order_num}`);
			},

			mescrollInit(mescroll) {
				this.mescroll = mescroll;
			},
			// 下拉刷新的回调
			downCallback(mescroll) {
				mescroll.resetUpScroll(); // 重置列表为第一页 (自动执行 mescroll.num=1, 再触发upCallback方法 )
				iconarr = [] //下拉刷新重置  iconarr
				this.numberpay = false; // 重置合并付款显示状态
			},
			/*上拉加载的回调: mescroll携带page的参数, 其中num:当前页 从1开始, size:每页数据条数,默认10 */
			upCallback(mescroll) {

				this.$fun.ajax.post('/user/temall', {}).then(res => {
					if (res.status == 1) {
						this.mList = res.data
					}
				})

			},
			// 点击空布局按钮的回调
			emptyClick() {
				uni.switchTab({
					url: "/pages/home"
				});
			},
			getbacktel() {
				if (backindex == 2) {
					uni.switchTab({
						url: "/pages/cart"
					});
				} else {
					this.getback();
				}
			},
			getstatusTip(item) {
				let tip = ""
				let status = item.status
				// 状态:0=待匹配,1=已预约,2=待支付,3=待确定,4=已完成,5=已卖出,6=封存订单
				tip = status == 0 ? '待匹配' : status == 1 ? '已预约' : status == 2 ? '待支付' : status == 3 ? '待确定' : status == 4 ?
					'待上架' : status == 5 ? '已上架' : status == 6 ? '封存订单' : status == 7 ? '已拆分' : status == 9 ? '已兑换' :
					status == 10 ? '已提货' : '转场中'
				return tip
			},
			showOptions: function(i) {
				if (this.showingIndex == i) return false;
				this.showingIndex = i;
				this.list = []; // 在这里手动置空列表,可显示加载中的请求进度
				this.mescroll.resetUpScroll(); // 刷新列表数据
				this.numberpay = false;
			},
			hideAl() {
				this.valShow = false;
			},
		}
	};
</script>

<style lang="scss" scoped>
	@import "uview-ui/index.scss";

	page,
	.index_class {
		height: 100%;
	}

	.team-box {

		// background-color: #fff;
		.team_item {
			margin: 20rpx 32rpx;
			background-color: #fff;
			border-radius: 8rpx 8rpx 8rpx 8rpx;

			.name {
				color: #666666;
			}

			.value {
				color: #333333;
			}
		}
	}

	.active {
		border-bottom: 2upx solid #26D0AB;
		color: #26D0AB;
	}

	.shou_sure {
		span {
			display: block;
			width: 100upx;
			height: 40upx;
			line-height: 40upx;
			border-radius: 20upx;
			border: 1upx solid #999;
			color: #999;
			text-align: center;
			font-size: 24upx;
		}
	}

	.padding-btn {
		padding-bottom: 80upx;
	}

	.mescroll-uni.mescroll-uni-fixed {
		top: 96upx !important;
	}

	.to_comment {
		width: 100upx !important;
		margin-left: 0 !important;
	}

	.paybtn {
		width: 100%;
		position: fixed;
		bottom: 0;
		left: 0;
		background-color: #fff;
		padding: 20upx;
	}

	.all-order {
		//padding-top: 88upx;

		.order_list {
			margin-bottom: 20upx;
			background-color: #fff;

			.order_top {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 20upx 30upx;
				border-bottom: 1upx solid #f4f4f4;
				color: #333;
			}
		}

		.order-all-text {
			text-align: right;
			font-size: 24upx;
			color: #333;
			margin-top: 15upx;
		}
	}

	/* 订单列表 */
	.order-list {
		width: 100%;
		//margin-top: 132rpx;
		/* #ifdef APP-PLUS */
		margin-top: 132rpx;

		/* #endif */
		.list {
			padding: 0 4%;
			min-height: 400rpx;
			background-color: #FFFFFF;
			border-radius: 20rpx;
			margin-bottom: 20rpx;

			.title-status {
				display: flex;
				align-items: center;
				justify-content: space-between;
				width: 100%;
				height: 100rpx;

				.title {
					display: flex;
					align-items: center;

					text {
						font-size: 26rpx;
						font-weight: bold;
						color: #222222;
					}
				}

				.status {
					display: flex;
					align-items: center;

					text {
						font-size: 26rpx;
						color: $base;
					}

					.del {
						padding: 10rpx;
						font-size: 34rpx;
						color: #222222;
						background-color: #f6f6f6;
						border-radius: 100%;
						margin-left: 20rpx;
					}
				}
			}

			.goods-list {
				width: 100%;

				.goods {
					display: flex;
					align-items: center;
					width: 100%;
					height: 200rpx;

					.thumb {
						display: flex;
						align-items: center;
						width: 25%;
						height: 100%;

						image {
							width: 160rpx;
							height: 160rpx;
							border-radius: 10rpx;
						}
					}

					.item {
						display: flex;
						align-items: center;
						width: 58%;
						height: 100%;

						.goods-name {
							width: 58%;

							text {
								font-size: 26rpx;
								color: #555555;
							}
						}

						.goods-price {
							display: flex;
							align-items: center;
							justify-content: flex-end;
							width: 30%;

							text {
								color: #222222;
							}

							.min {
								font-size: 26rpx;
							}

							.max {
								font-size: 34rpx;
							}
						}
					}
				}
			}

			.status-btn {
				display: flex;
				align-items: center;
				justify-content: space-between;
				width: 100%;
				height: 100rpx;

				.btn {
					border-radius: 100rpx;

					text {
						font-size: 26rpx;
						color: #555555;
					}
				}

				.action {

					text {
						color: $base;
					}
				}
			}
		}
	}

	.bus-top {
		background: #FFFFFF;
		display: flex;
		// justify-content: center;
		flex-wrap: wrap;

		.b {
			width: calc(100% / 2);
			display: flex;
			padding: 0 20rpx ;
			// justify-content: center;
			justify-content: space-between;
			// flex-direction: column;
			align-items: center;
			flex-wrap: wrap;
			color: #666666;
			font-size: 30rpx;
			margin: 20rpx 0;
			box-sizing: border-box;

			.v {
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 500;
				font-size: 32rpx;
				color: #310FFF;
				// margin-bottom: 10rpx;
			}

			.n {
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				font-size: 24rpx;
				color: #666666;
			}
		}

		.b {
			border-right: 1px solid #E2E2E2;
		}

		.b:nth-child(3) {
			border-right: none;
		}
	}

	.box {
		margin: 24rpx 32rpx;

		.title_t {
			width: 100%;
			height: 92rpx;
			background: linear-gradient(180deg, #D8FFEA 0%, #FFFFFF 100%);
			display: flex;
			flex: 1;
			box-sizing: border-box;
			padding: 10rpx 30rpx;
			padding-bottom: 0;
			align-items: center;
			box-sizing: border-box;

			.title {
				flex: 1;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: bold;
				font-size: 32rpx;
				color: #333333;
			}

			.r {
				flex: 1;
				text-align: right;
			}
		}
	}
</style>