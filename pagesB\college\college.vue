<template>
	<view>
		<view class="banner">
			<swiper class="screen-swiper square-dot" autoplay indicator-dots="true" circular="true" autoplay="true"
				interval="5000" duration="500">
				<swiper-item v-for="(item,index) in swiperList" :key="index">
					<image :src="$fun.imgUrl(item.image)" mode="aspectFill"></image>
				</swiper-item>
			</swiper>
		</view>
		<!-- 		<view class="btns" @click="$fun.jump(`./collegeList`)">
			课程列表
		</view> -->
		<view class="goods-list">
			<view class="goods-list-title">
				<image :src="$fun.imgUrl(`/static/sxy.png`)" mode="widthFix" style="width: 40rpx;"></image>
				<text>健康大课堂</text>
			</view>
			<view class="list" v-for="(item,index) in goodsList"
				@click="$fun.jump(`./collegeInfo?id=${item.id}&name=健康大课堂`)" :key="index">
				<view class="pictrue">
					<image :src="$fun.imgUrl(item.image)"></image>
				</view>
				<view class="shop">
					<view class="">
						<view class="title-tag">
							<view class="tag">
								{{item.name}}
							</view>
						</view>
						<view class="tag_text">
							{{item.title}}
						</view>
					</view>
					<view class="price-info">
						<view class="vip-price">
							<text>{{$u.timeFormat(item.createtime, 'yyyy.mm.dd')}}</text>
						</view>
						<view class="vip-price">
							<text>{{item.read}}已读</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view style="height: 102rpx;">

		</view>

		<TabBar :tabBarShow="2"></TabBar>
	</view>
</template>

<script>
	import TabBar from '@/components/TabBar/TabBar.vue';
	export default {
		components: {
			TabBar
		},
		data() {
			return {
				page: 1,
				goodsList: [],
				swiperList: []
			}
		},
		onLoad() {
			this.getIndexGoodsList();
			this.getIndexSwipter();
		},
		methods: {
			/**
			 * 获取商品列表
			 */
			getIndexGoodsList() {
				this.$fun.ajax.post('news/college', {
					type: '0',
					page: this.page
				}).then(res => {
					if (res.status == 1) {
						if (this.page == 1) {
							this.goodsList = []
						}
						const curList = res.data;
						this.goodsList = this.goodsList.concat(curList); //追加新数据
						if (curList.length > 0) {
							this.page++
						}
					}
				})
			},
			getIndexSwipter() {
				this.$fun.ajax.post('News/lists', {
					type: 'school'
				}).then(res => {
					if (res.status == 1) {
						this.swiperList = res.data
					}
				})
			},
		},
		onReachBottom() {
			if (this.page == 1) {
				this.goodsList = []
				return
			}
			this.getIndexGoodsList()
		},
	}
</script>

<style lang="scss">
	page {
		background: #F8F9F9;
	}

	.btns {
		font-family: Source;
		font-weight: 500;
		font-size: 30rpx;
		padding: 0 30rpx;
		padding-top: 30rpx;
		color: #310FFF;
		display: flex;
		justify-content: flex-end;
		align-items: center;
	}

	.goods-list-title {
		display: flex;
		justify-content: center;
		align-items: center;
		margin-bottom: 20rpx;

		text {
			margin-left: 10rpx;
			font-weight: 400;
			font-size: 32rpx;
			color: #333333;
		}
	}

	.banner {
		// margin-top: calc(110rpx + var(--status-bar-height));
		margin-top: 30rpx;

		padding: 0 25rpx;
		height: 262rpx;
		border-radius: 10rpx;
		overflow: hidden;

		.screen-swiper {
			height: 262rpx;
			min-height: 100% !important;

			image {
				height: 262rpx;
				border-radius: 16rpx;
			}
		}
	}

	.goods-list {
		margin: 20rpx;
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		padding: 20rpx;
		background: #FFFFFF;
		box-shadow: 0rpx 8rpx 12rpx 0rpx rgba(198, 198, 198, 0.25);
		border-radius: 16rpx 16rpx 16rpx 16rpx;

		.list {
			// padding: 20rpx 0;
			width: 100%;
			margin-bottom: 20rpx;
			background-color: #FFFFFF;
			border-radius: 10rpx;
			overflow: hidden;
			margin-left: 0;
			// box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(0, 0, 0, 0.25);
			display: flex;

			.pictrue {
				display: flex;
				justify-content: center;
				align-items: center;
				// padding: 16rpx;

				image {
					width: 200rpx;
					height: 200rpx;
					border-radius: 8rpx 8rpx 8rpx 8rpx;
				}
			}

			.shop {
				margin-left: 20rpx;
				width: calc(100% - 210rpx);
				display: flex;
				justify-content: space-between;
				flex-direction: column;
			}

			.title-tag {
				width: 100%;
				// padding: 20rpx;

				.tag {
					font-family: Source;
					font-weight: 500;
					font-size: 32rpx;
					color: #333333;
					font-style: normal;
					text-transform: none;
					overflow: hidden;
					text-overflow: ellipsis;
					display: -webkit-box;
					-webkit-line-clamp: 2;
					-webkit-box-orient: vertical;
					white-space: normal;


					text {
						font-size: 24rpx;
						color: #FFFFFF;
						padding: 4rpx 16rpx;
						background: linear-gradient(to right, $base, $change-clor);
						border-radius: 6rpx;
						margin-right: 10rpx;
					}
				}
			}

			.tag_text {
				// padding: 0 20rpx;
				font-family: Source;
				font-weight: 400;
				font-size: 20rpx;
				font-size: 20rpx;
				color: #AAAAAA;
				text-align: left;
				font-style: normal;
				text-transform: none;
			}

			.price-info {
				// margin: 10rpx 20rpx;
				height: 60rpx;
				box-sizing: border-box;
				border-radius: 8rpx 8rpx 8rpx 8rpx;
				display: flex;
				justify-content: space-between;

				// background: #C93233;
				.user-price {
					// background: #310FFF;
					padding: 0 10rpx;
					display: flex;
					align-items: baseline;
					// margin-right: 10rpx;

					text {
						color: #F12F30;
					}

					.min {
						font-family: Source;
						font-weight: 500;
						font-size: 24rpx;
						color: #F12F30;
						text-align: left;
						font-style: normal;
						text-transform: none;
					}

					.max {
						font-family: Source;
						font-weight: 500;
						font-size: 32rpx;
						color: #F12F30;
						text-align: left;
						font-style: normal;
						text-transform: none;
					}
				}

				.vip-price {
					height: 60rpx;
					box-sizing: border-box;
					display: flex;
					justify-content: center;
					font-family: Source;
					align-items: center;
					padding-right: 14rpx;
					border-radius: 132rpx 132rpx 132rpx 132rpx;

					image {
						width: 26rpx;
						height: 26rpx;
						margin-right: 10rpx;
					}

					text {
						font-size: 24rpx;
						color: #AAAAAA;
					}
				}
			}
		}
	}
</style>