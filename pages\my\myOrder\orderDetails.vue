<template>
	<view class="page">
		<!-- 订单状态 -->
		<view class="order-status" style="overflow: hidden;">
			<view class="status" style="margin-top: 20px;">
				<text class="iconfont icon-zhuyi" @click="$fun.jump('',0,0)"></text>
				<text>{{getstatusTip(orderInfo)}}</text>
			</view>
			<!-- 	<view class="reason">
				<text>剩余12分68秒</text>
			</view> -->
		</view>
		<!-- 收货地址 -->
		<view class="shipping-address" v-if="orderInfo.address">
			<view class="name-phone">
				<text class="iconfont icon-dingwei"></text>
				<text>{{orderInfo.address.name}}</text>
				<text>{{orderInfo.address.mobile}}</text>
			</view>
			<view class="address">
				<text>{{orderInfo.address.pro}}{{orderInfo.address.city}}{{orderInfo.address.area}}{{orderInfo.address.address}}</text>
			</view>
		</view>
		<!-- 订单商品 -->
		<view class="order-goods">
			<view class="goods-list">
				<view class="list" v-for="(item,index) in orderInfo.goods" :key="index">
					<view class="thumb">
						<image :src="$fun.imgUrl(item.image)" mode=""></image>
					</view>
					<view class="item">
						<view class="title">
							<text class="one-omit">{{orderInfo.category}}</text>
						</view>
						<view class="title">
							<text class="one-omit">{{item.name}}</text>
						</view>
						<view class="num-size">
							<text>数量：{{item.num}}</text>
							<text>{{item.sign}}</text>
						</view>
						<view class="price">
							<text>￥{{item.money}}</text>
							<text style="margin-left: 10rpx;" v-if="item.dikou!=0"> + {{item.dikou}}</text>
						</view>
						<view class="order-btn" v-if="item.mobile_status==1&&mobile_url!=''">
							<view class="btn" @click="selectionNumber(item)">
								<text>选号</text>
							</view>
						</view>
					</view>
				</view>
			</view>
			<!-- <view class="contact">
				<text class="iconfont icon-kefu"></text>
				<text>联系客服</text>
			</view> -->
		</view>
		<!-- 订单信息 -->
		<view class="order-info">
			<view class="info-list">
				<view class="list" v-if="orderInfo.name&&orderInfo.type!=1">
					<view class="title">姓名:</view>
					<view class="content">
						<text>{{orderInfo.name}}</text>
					</view>
				</view>
				<view class="list" v-if="orderInfo.phone&&orderInfo.type!=1">
					<view class="title">手机号:</view>
					<view class="content">
						<text>{{orderInfo.phone}}</text>
					</view>
				</view>
				<view class="list">
					<view class="title">订单编号:</view>
					<view class="content">
						<text>{{orderInfo.order_sn}}</text>
						<!-- <text class="btn">复制</text> -->
					</view>
				</view>
				<view class="list">
					<view class="title">下单时间:</view>
					<view class="content">
						<text>{{$u.timeFormat(orderInfo.createtime, 'yyyy-mm-dd hh:MM')}}</text>
					</view>
				</view>

				<!-- <view class="list">
					<view class="title">支付方式:</view>
					<view class="content">
						<text>在线支付</text>
					</view>
				</view>
				<view class="list">
					<view class="title">配送方式:</view>
					<view class="content">
						<text>普通快递</text>
					</view>
				</view>
				<view class="list">
					<view class="title">配送日期:</view>
					<view class="content">
						<text>2020-11-11</text>
					</view>
				</view> -->
			</view>
		</view>
		<!-- 订单明细 -->
		<view class="order-details">
			<view class="details-list">
				<view class="order-details">
					<view class="details-list" style="padding: 0;">
						<view class="list" v-if="orderInfo.express">
							<view class="title">
								<text>快递公司</text>
							</view>
							<view class="price">
								<text>{{orderInfo.express}}</text>
							</view>
						</view>
						<view class="list" style="padding: 0;" v-if="orderInfo.express_code">
							<view class="title">
								<text>快递单号</text>
							</view>
							<view class="price">
								<text>{{orderInfo.express_code}}</text>
							</view>
						</view>
						<view class="list action">
							<view class="title">
								<text>运费：</text>
							</view>
							<view class="price">
								<text>￥{{orderInfo.freight}}</text>
							</view>
						</view>
						<view class="list action">
							<view class="title">
								<text>实付款：</text>
							</view>
							<view class="price">
								<text>￥{{orderInfo.money*1}}</text>
								<text style="margin-left: 10rpx;" v-if="orderInfo.dikou!=0"> +</text> <text
									style="margin-left: 10rpx;" v-if="orderInfo.dikou!=0">
									{{orderInfo.dikou}}</text>
							</view>
						</view>
					</view>
				</view>

			</view>
		</view>
		<!-- 底部按钮 -->
		<view class="footer-btn">
			<view class="del">
				<!-- <text>删除订单</text> -->
			</view>
			<view class="btn">
				<!-- <text>查看发票</text> -->
				<text class="action" v-if="orderInfo.status == 0" @click="getpay(orderInfo.types)">去支付</text>
				<text class="action" v-if="orderInfo.status> 0&&orderInfo.type==1" @click="$fun.jump(`/pages/home/<USER>/contract1?id=${orderInfo.id}`)">查看合同</text>
				<text class="action" v-if="orderInfo.status == 2" @click="getqrshouhuo(orderInfo.id)">确认收货</text>
				<text class="action" @click="$fun.jump('/pages/home/<USER>',3)">返回首页</text>
				<text class="action" v-if="orderInfo.status==2"
					@click="$fun.jump(`./logistics?express_code=${orderInfo.express_code}&express_idcode=${orderInfo.express_idcode}`,0,0)">查看物流</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				orderInfo: {}
			};
		},
		onLoad(option) {
			this.oid = option.order_num
		},
		onShow() {
			this.$fun.ajax.post('order/getOrder', {
				oid: this.oid
			}).then(res => {
				// console.log(res)
				this.orderInfo = res.data
			})
		},
		methods: {
			selectionNumber(item) {
				if (item.mobile_url && item.mobile_status == 1) {
					// #ifdef H5  
					location.href = item.mobile_url
					return
					// #endif
					// #ifdef APP-PLUS  
					plus.runtime.openURL(item.mobile_url)
					return
					// #endif
				}
			},
			getstatusTip(item) {
				let tip = ""
				let status = item.status
				if (item.pin == 1) {
					status = item.pinstatus
					if (status == 1) {
						let status = item.status
						tip = status == 0 ? '待支付' : status == 1 ? '待发货' : status == 2 ? '待收货' : status == 3 ? '已完成' :
							status == 4 ?
							'已取消' : ''
					} else {
						tip = status == 0 ? '拼团中' : status == 1 ? '拼团成功' : status == 2 ? '拼团失败' : status == 3 ? '已取消' : ''
					}
				} else {
					tip = status == 0 ? '待支付' : status == 1 ? '待发货' : status == 2 ? '待收货' : status == 3 ? '已完成' : status ==
						4 ?
						'已取消' : ''
				}
				return tip
			},
			/**
			 * 确认收货
			 */
			getqrshouhuo(oid) {
				uni.showModal({
					content: '确认收到货了吗?',
					success: e => {
						if (e.confirm) {
							this.$fun.ajax.post('order/received', {
								oid
							}).then(res => {
								if (res.status == 1) {
									this.$fun.msg(res.msg);
									this.list = [];
									this.mescroll.resetUpScroll();
								}
							})
						}
					}
				});
			},
			/**
			 * 售后点击
			 */
			onApplyAftersales() {
				uni.navigateTo({
					url: '/pages/AfterSaleType/AfterSaleType',
				})
			},
			async getpay(ordernumber) {
				this.$fun.ajax.post('order/getDelTime', {
					oid: ordernumber
				}).then(res => {
					if (res.status == 1) {
						this.$fun.jump(`/pages/home/<USER>
					} else {
						this.$fun.msg(res.msg);
					}
				})
			},
		}
	}
</script>

<style scoped lang="scss">
	.page {
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		background-color: #f6f6f6;
	}

	/* 订单状态 */
	.order-status {
		width: 100%;
		height: 200rpx;
		background: linear-gradient(to right, $base, $change-clor);

		.status {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100%;
			height: 100rpx;

			text {
				font-size: 38rpx;
				// font-weight: bold;
				color: #FFFFFF;
			}

			.iconfont {
				margin-right: 20rpx;
			}
		}

		.reason {
			display: flex;
			// align-items: center;
			justify-content: center;
			width: 100%;
			height: 80rpx;

			text {
				font-size: 28rpx;
				color: #f6f6f6;
			}
		}
	}

	/* 收货地址 */
	.shipping-address {
		width: 100%;
		height: 200rpx;
		background-color: #FFFFFF;
		border-radius: 20rpx;
		margin: -20rpx auto 20rpx;

		.name-phone {
			display: flex;
			align-items: center;
			padding: 0 4%;
			height: 80rpx;

			text {
				font-size: 28rpx;
				font-weight: bold;
				color: #222222;
				margin-right: 20rpx;
			}
		}

		.address {
			display: flex;
			padding: 0 4%;
			height: 100rpx;
			margin-left: 50rpx;

			text {
				font-size: 26rpx;
				color: #959595;
			}
		}
	}

	/* 订单商品 */
	.order-goods {
		width: 100%;
		background-color: #FFFFFF;
		border-radius: 20rpx;

		.goods-list {
			padding: 0 4%;

			.list {
				display: flex;
				align-items: center;
				width: 100%;
				min-height: 200rpx;

				.thumb {
					display: flex;
					width: 30%;

					// height: 200rpx;
					// margin-top: -20rpx;
					image {
						width: 160rpx;
						height: 160rpx;
						border-radius: 10rpx;
					}
				}

				.item {
					width: 70%;
					height: 100%;

					.title {
						display: flex;
						align-items: center;
						width: 100%;
						height: 60rpx;

						text {
							font-size: 26rpx;
							color: #222222;
						}
					}

					.num-size {
						display: flex;
						align-items: center;
						width: 100%;
						height: 60rpx;

						text {
							font-size: 26rpx;
							color: #959595;
							margin-right: 20rpx;
						}

						text:last-child {
							margin-right: 0;
						}
					}

					.price {
						display: flex;
						align-items: center;
						width: 100%;
						height: 60rpx;

						text {
							font-size: 28rpx;
							font-weight: bold;
							color: #222222;
						}
					}

					.order-btn {
						display: flex;
						align-items: center;
						justify-content: flex-end;
						width: 100%;
						height: 100rpx;

						.btn {
							padding: 10rpx 30rpx;
							color: #555555;
							font-size: 26rpx;
							border: 2rpx solid #EEEEEE;
							border-radius: 100rpx;
						}
					}

					.order-btn {
						display: flex;
						align-items: center;
						justify-content: flex-end;
						width: 100%;
						height: 100rpx;

						.btn {
							padding: 10rpx 30rpx;
							color: #555555;
							font-size: 26rpx;
							border: 2rpx solid #EEEEEE;
							border-radius: 100rpx;
						}
					}
				}
			}
		}

		.contact {
			display: flex;
			align-items: center;
			justify-content: center;
			display: flex;
			align-items: center;
			width: 100%;
			height: 100rpx;
			background-color: #FFFFFF;
			box-shadow: 0 0 20rpx #EEEEEE;
			border-radius: 0 0 20rpx 20rpx;

			text {
				font-size: 28rpx;
				color: #555555;
			}

			.iconfont {
				font-size: 34rpx;
				margin-right: 20rpx;
			}
		}
	}

	/* 订单信息 */
	.order-info {
		width: 100%;
		background-color: #FFFFFF;
		border-radius: 20rpx;
		margin: 20rpx auto;

		.info-list {
			padding: 0 4%;

			.list {
				display: flex;
				align-items: center;
				width: 100%;
				height: 100rpx;
				border-bottom: 2rpx solid #f6f6f6;

				.title {
					font-size: 26rpx;
					color: #959595;
				}

				.content {
					display: flex;
					align-items: center;
					margin-left: 20rpx;

					text {
						font-size: 26rpx;
						font-weight: bold;
						color: #222222;
					}

					.btn {
						padding: 6rpx 20rpx;
						background-color: #EEEEEE;
						color: #555555;
						font-size: 24rpx;
						border-radius: 50rpx;
						margin-left: 40rpx;
					}
				}
			}
		}
	}

	/* 订单明细 */
	.order-details {
		width: 100%;
		background-color: #FFFFFF;
		border-radius: 20rpx;
		margin: 20rpx auto;

		// padding-bottom: 100rpx;
		.details-list {
			padding: 0 40rpx;

			.list {
				display: flex;
				align-items: center;
				justify-content: space-between;
				width: 100%;
				height: 100rpx;
				border-bottom: 2rpx solid #f6f6f6;

				.title {
					font-size: 26rpx;
					color: #959595;
				}

				.price {
					font-size: 26rpx;
					font-weight: bold;
				}
			}

			.action {
				display: flex;
				align-items: center;
				justify-content: flex-end;

				.price {
					font-size: 32rpx;
					font-weight: bold;
					color: $base;
				}
			}
		}
	}

	.footer-btn {
		position: fixed;
		left: 0;
		bottom: 0;
		display: flex;
		align-items: center;
		justify-content: space-between;
		width: 100%;
		height: 100rpx;
		background-color: #FFFFFF;
		border-top: 2rpx solid #EEEEEE;
		padding: 0 4%;

		.del {
			display: flex;
			align-items: center;

			text {
				padding: 10rpx 30rpx;
				font-size: 24rpx;
				font-weight: bold;
			}
		}

		.btn {
			display: flex;
			align-items: center;

			text {
				padding: 10rpx 30rpx;
				font-size: 24rpx;
				border: 2rpx solid #C0C0C0;
				border-radius: 100rpx;
				color: #c0c0c0;
				margin-left: 20rpx;
			}

			.action {
				background-color: $base;
				color: #FFFFFF;
				border: 2rpx solid #FFFFFF;
			}
		}
	}
</style>