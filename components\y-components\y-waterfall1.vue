<template>
    <div class="waterfall1">
        <view class="goods-list">
            <view :class="isList ? 'list-view' : 'list-view'" v-for="(item, index) in leftList"
                @click="$fun.jump(`./videCcircle?id=${item.id}&cid=${item.category_id}`)" :key="index">
                <view class="thumb">
                    <image :src="$fun.imgUrl(item.coverimage)" mode="widthFix"></image>
                </view>
                <view class="item">
                    <view class="title">
                        <text class="two-omit">{{ item.title }}</text>
                    </view>

                    <view class="price" style="align-items: center;">
                        <u-avatar size="60" :src="$fun.imgUrl(item.avatar)"></u-avatar>
                        <view class="zan">
                            <u-icon name="thumb-up-fill" color="#310FFF"></u-icon>
                            <text style="font-size: 24rpx;">{{ item.dianzan }}</text>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <view class="goods-list">
            <view :class="isList ? 'list-view' : 'list-view'" v-for="(item, index) in rightList"
                @click="$fun.jump(`./videCcircle?id=${item.id}&cid=${item.category_id}`)" :key="index">
                <view class="thumb">
                    <image :src="$fun.imgUrl(item.coverimage)" mode="widthFix"></image>
                </view>
                <view class="item">
                    <view class="title">
                        <text class="two-omit">{{ item.title }}</text>
                    </view>

                    <view class="price" style="align-items: center;">
                        <u-avatar size="60" :src="$fun.imgUrl(item.avatar)"></u-avatar>
                        <view class="zan">
                            <u-icon name="thumb-up-fill" color="#310FFF"></u-icon>
                            <text style="font-size: 24rpx;">{{ item.dianzan }}</text>
                        </view>
                    </view>
                </view>
            </view>
        </view>
    </div>
</template>

<script>
export default {
    name: 'YWaterfall1',
    props: {
        items: {
            type: Array,
            required: true
        },
        isbtn: {
            type: Boolean,
            default: false
        },
        pin: {
            type: Number,
            default: 0
        }
    },
    data() {
        return {
            leftList: [],
            rightList: []
        }
    },
    watch: {
        items: {
            handler(newVal) {
                this.init()
            },
            deep: true
        }
    },
    methods: {
        init() {
            this.leftList = this.items.filter((item, index) => index % 2 == 0)
            this.rightList = this.items.filter((item, index) => index % 2 == 1)
            console.log(this.rightList)
        },
        goosDetails(id) {
            if (this.pin == 0) {
                this.$fun.jump(`/pages/home/<USER>
            } else {
                this.$fun.jump(`/pagesB/shop_p/shop_p?id=${id}`)
            }
        }
    }
}
</script>

<style scoped lang="scss">
.waterfall1 {
    margin: 20rpx;
    display: flex;
    justify-content: space-between;

    .goods-list {
        width: 48%;
        border-radius: 20rpx;
        overflow: hidden;

        .list-view {
            width: 100%;
            background-color: #ffffff;
            border-radius: 10rpx;
            margin-right: 2%;
            margin-bottom: 20rpx;
            overflow: hidden;

            .thumb {
                width: 100%;
                //height: 300rpx;
                overflow: hidden;

                image {
                    width: 100%;
                    height: 300rpx;
                }
            }

            .item {
                width: 100%;

                .title {
                    padding: 20rpx;

                    text {
                        font-family: YaHei;
                        font-weight: 400;
                        font-size: 28rpx;
                        color: #333333;
                        line-height: 33rpx;
                        text-align: left;
                        font-style: normal;
                        text-transform: none;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        display: -webkit-box;
                        -webkit-line-clamp: 2;
                        -webkit-box-orient: vertical;
                        white-space: normal;

                    }
                }

                .tag_text {
                    padding: 0 20rpx;
                    font-family: YaHei;
                    font-weight: 400;
                    font-size: 20rpx;
                    color: #888888;
                    line-height: 23rpx;
                    text-align: left;
                    font-style: normal;
                    text-transform: none;
                }

                .price {
                    margin: 10rpx 20rpx;
                    padding-left: 14rpx;
                    height: 60rpx;
                    //background: #310FFF;
                    border-radius: 8rpx 8rpx 8rpx 8rpx;
                    display: flex;
                    justify-content: space-between;

                    .retail-price {
                        display: flex;
                        align-items: center;
                        margin-right: 10rpx;

                        .min {
                            font-family: YaHei;
                            font-weight: 400;
                            font-size: 20rpx;
                            color: #FFFFFF;
                            text-align: left;
                            font-style: normal;
                            text-transform: none;
                            display: flex;
                            align-items: center;
                        }

                        .max {
                            font-family: YaHei;
                            font-weight: 400;
                            font-size: 32rpx;
                            color: #FFFFFF;
                            text-align: left;
                            font-style: normal;
                            text-transform: none;
                        }

                        .tag {
                            position: relative;
                            background-color: $price-clor;
                            border-radius: 4rpx;
                            margin-left: 10rpx;

                            text {
                                display: inline-block;
                                color: #ffffff;
                                font-size: 24rpx;
                                transform: scale(0.7);
                            }
                        }

                        .tag:before {
                            position: absolute;
                            left: -6rpx;
                            top: 0;
                            content: '';
                            width: 0;
                            height: 0;
                            border-top: 0rpx solid transparent;
                            border-right: 10rpx solid $base;
                            border-bottom: 6rpx solid transparent;
                        }
                    }

                    .vip-price {
                        display: flex;
                        font-family: YaHei;
                        align-items: center;
                        color: #FFFFFF;
                        width: 104rpx;
                        background: #FF1A1B;
                        justify-content: center;
                        border-radius: 8rpx 8rpx 8rpx 8rpx;

                        image {
                            width: 26rpx;
                            height: 26rpx;
                            margin-right: 10rpx;
                        }

                        text {
                            color: #FFFFFF;
                            font-size: 24rpx;
                        }
                    }
                }
            }
        }

        .list-view:nth-child(2n) {
            margin-right: 0;
        }

        // 列表
        .list-li {
            display: flex;
            align-items: center;
            width: 100%;
            height: 300rpx;
            background-color: #ffffff;

            .thumb {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 30%;
                height: 100%;

                image {
                    width: 200rpx;
                    height: 200rpx;
                    border-radius: 10rpx;
                }
            }

            .item {
                display: flex;
                flex-direction: column;
                justify-content: center;
                width: 70%;
                height: 100%;
                border-bottom: 2rpx solid #f6f6f6;

                .title {
                    padding: 20rpx;

                    text {
                        width: 100%;
                        color: #212121;
                        font-size: 26rpx;
                    }
                }

                .price {
                    padding: 0 20rpx;

                    .retail-price {
                        display: flex;
                        align-items: flex-end;
                        width: 100%;
                        height: 40rpx;

                        .min {
                            display: inline-block;
                            font-size: 24rpx;
                            color: $base;
                            font-weight: bold;
                            transform: scale(0.7);
                        }

                        .max {
                            font-size: 28rpx;
                            color: $base;
                            font-weight: bold;
                        }

                        .tag {
                            position: relative;
                            background-color: $base;
                            border-radius: 4rpx;
                            margin-left: 10rpx;

                            text {
                                display: inline-block;
                                color: #ffffff;
                                font-size: 24rpx;
                                transform: scale(0.7);
                            }
                        }

                        .tag:before {
                            position: absolute;
                            left: -6rpx;
                            top: 0;
                            content: '';
                            width: 0;
                            height: 0;
                            border-top: 0rpx solid transparent;
                            border-right: 10rpx solid $base;
                            border-bottom: 6rpx solid transparent;
                        }
                    }

                    .vip-price {
                        display: flex;
                        align-items: flex-end;
                        width: 100%;
                        height: 40rpx;

                        .min {
                            display: inline-block;
                            font-size: 24rpx;
                            color: #212121;
                        }

                        .max {
                            font-size: 24rpx;
                            color: #212121;
                        }
                    }
                }
            }
        }
    }

    .price1 {
        margin: 10rpx 20rpx;
        color: #FFFFFF;
        display: flex;
        width: fit-content;
        align-items: center;
        background-color: #cfb06a;

        .min {
            padding-left: 10rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            font-size: 24rpx;
        }

        .max {
            border-radius: 30rpx 0 0 0;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            padding: 0 10rpx;
            background: #f3e9d0;
            margin-left: 10rpx;
            color: #F15232;
            font-size: 24rpx;
        }
    }
}
</style>
