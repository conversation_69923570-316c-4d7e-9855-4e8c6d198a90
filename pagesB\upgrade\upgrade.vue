<template>
	<view class="page">

		<view class="input_box">
			<view class="input_title">
				<view class="title">
					打款凭证
				</view>
			</view>
			<view class="input_box_img">
				<view class="upload_img_b">
					<image v-for="(item,index) in anchor.image.value" :key="index"
						@tap="$u.throttle(uploadImage('anchor','image',index), 500)" :src="$fun.imgUrl(item)"
						mode="widthFix">
					</image>
					<image @tap="$u.throttle(uploadImage('anchor','image','1008611'), 500)"
						:src="$fun.imgUrl('/static/ico-101.png')" mode="widthFix"></image>
				</view>
			</view>
		</view>
		<view class="input_box">
			<view class="input_title">
				<view class="title">
					备注信息
				</view>
			</view>
			<view class="textarea_b">
				<textarea v-model="anchor.memo.value" placeholder-class="textarea_p" name="" id="" cols="30" rows="10"
					placeholder="请输入备注信息"></textarea>
			</view>
		</view>

		<view class="config_btn" @tap="$u.throttle(btnAClick, 500)">
			提交
		</view>
		<view style="height: 102rpx;">

		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				swiperList: [],
				fuwuInfo: {},
				anchor: {
					memo: {
						value: '',
						placeholder: '请输入您的备注'
					}, //备注
					image: {
						value: [],
						placeholder: '请上传您的资质报告'
					},
				},
				albelS: {
					fontFamily: 'Source',
					fontWeight: '400',
					fontSize: '28rpx',
					color: '#333333'
				},
				id: ''
			};
		},
		onLoad(option) {
			this.id = option.id;
		},
		methods: {
			// 上传头像

			uploadImage(str, str1, index) {
				// 从相册选择图片
				var _this = this;
				try {
					uni.chooseImage({
						count: 1, //默认选择1张图片
						sizeType: ['original', 'compressed'], //original 原图，compressed 压缩图，默认二者都有
						sourceType: ['album'],
						success: function(res) {
							_this.handleUploadFile(res.tempFilePaths, str, str1, index);
						}
					});
				} catch (error) {
					conso.log('333333.', error)
				}
			},
			// 上传头像
			handleUploadFile(data, str, str1, index) {
				const _this = this;
				const filePath = data.path || data[0] || data.detail.avatarUrl;
				console.log(filePath)
				this.$fun.uploadPic(
					filePath
				).then(res => {
					this.$fun.msg(res.msg)
					if (res.status == 1) {
						if (index == 1008611) {
							console.log(_this[str][str1])
							_this[str][str1].value.push(res.data.url);
						} else {
							_this[str][str1].value[index] = res.data.url;
							_this.$forceUpdate()
						}
					}
				})
			},
			/**
			 * 申请
			 */
			btnAClick() {
				let prams = {

				}
				let url = ''
				url = 'user/up';
				for (var key in this.anchor) {
					if (!this.anchor[key].value) {
						this.$fun.msg(this.anchor[key].placeholder);
						return;
					} else {
						prams[key] = JSON.parse(JSON.stringify(this.anchor[key].value))
					}
				}
				prams.image = prams.image.join(',')
				this.$fun.ajax.post(url, {
					...prams
				}).then(res => {
					console.log(res)
					if (res.status == 1) {
						this.$fun.msg(res.msg);
						this.$fun.jump('', 5, 1200)
					}
				})
			},
		}
	}
</script>

<style scoped lang="scss">
	page {
		.page {
			padding: 0 16rpx;

			.banner {
				margin-top: 32rpx;
				// margin-top: calc(110rpx + var(--status-bar-height));
				margin-top: 30rpx;

				padding: 0 25rpx;
				height: 324rpx;
				border-radius: 10rpx;
				overflow: hidden;

				.screen-swiper {
					height: 324rpx;
					min-height: 100% !important;

					image {
						height: 324rpx;
						border-radius: 10rpx;
					}
				}
			}

			.input_box {
				margin-top: 24rpx;
				padding: 24rpx;
				background: #FFFFFF;
				border-radius: 20rpx 20rpx 20rpx 20rpx;

				.input_title {
					display: flex;
					align-items: center;

					image {
						width: 50rpx;
						height: 50rpx;
					}

					.title {
						font-family: Source;
						font-weight: 500;
						font-size: 36rpx;
						color: #310FFF;
					}
				}

				.input_box_img {
					padding-top: 24rpx;
					display: flex;
					flex-wrap: wrap;
					justify-content: space-between;

					.name {
						font-family: Source;
						font-weight: 400;
						font-size: 28rpx;
						color: #333333;
					}

					.upload_img {
						display: flex;
						align-items: center;
						flex-direction: column;

						.up_img {
							display: flex;
							justify-content: center;
							align-items: center;
							width: 110rpx;
							height: 110rpx;
							border: 2rpx dashed #AAAAAA;
							font-size: 80rpx;
							color: #AAAAAA;
						}

						.t {
							font-family: Source;
							font-weight: 400;
							font-size: 24rpx;
							color: #AAAAAA;
						}
					}

					.upload_img_b {
						margin-top: 24rpx;
						width: 100%;
						display: flex;
						flex-wrap: wrap;

						image {
							width: 200rpx;
							margin-right: 22rpx;
						}
					}
				}

				.textarea_b {
					margin-top: 20rpx;
					width: 100%;
					height: 288rpx;
					background: #F8F8F8;
					border-radius: 16rpx 16rpx 16rpx 16rpx;

					textarea {
						padding: 24rpx;
					}

					.textarea_p {
						font-family: Source;
						font-weight: 400;
						font-size: 24rpx;
						color: #AAAAAA;
					}
				}
			}

			.config_btn {
				width: 352rpx;
				height: 92rpx;
				background: #310FFF;
				border-radius: 12rpx 12rpx 12rpx 12rpx;
				font-family: Source;
				font-weight: 500;
				font-size: 32rpx;
				color: #FFFFFF;
				display: flex;
				justify-content: center;
				align-items: center;
				margin: 102rpx auto;
			}
		}
	}
</style>