<template>
	<view class="content">
		<view class="canvasImg">
			<image   :src="imgSrc" mode=""></image>
		</view>
		<view class="imgBox">
			<view class="imgItem"  v-for="(item,index) in imgbox" :key="index" >
				<image :src="$fun.imgUrl(item)" mode=""></image>
			</view>
		</view>
		<canvas :ref="`firstCanvas`" :style="{width: '600rpx',height:'900rpx',position: 'absolute',top:'-1000000rpx',left: '-100000rpx'}"
				:canvas-id="`firstCanvas`" ></canvas>
		<uqrcode :id="'batchQRCode'" style="position: absolute;top:-1000px;left: 10000px;" ref="batchQRCode" :text="text" :size="256" :margin="10" background-color="#FFFFFF" foreground-color="#000000"></uqrcode>
		</view>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				imgbox: [],
				imgSrc: '',
				canvasQrPath: "",
				text: '',
				size: 150,
				codeImg: "",
				colorDark: '#000000',
				colorLight: '#ffffff',
				list: [],
				canvasHeight: 0,
				canvasImageUrl:''
			};
		},
		onLoad() {
			this.$fun.ajax.post('/config/invitation', {}).then(res => {
				if (res.status == 1) {
					let data = []
					res.data.forEach((item) => {
						data.push(this.$fun.imgUrl(item.image))
					})
					this.imgbox = data
					this.list = res.data
				}
			})
			// #ifdef H5
			this.getQrPath()
			// #endif
			// #ifdef MP-WEIXIN
			this.getaccesstoken()
			// #endif
		},
		methods: {
			// 获取token
			getaccesstoken() {
				let that = this
				that.$fun.ajax.post('/user/miniImage', {}).then(res => {
					uni.getImageInfo({
						src: that.$fun.imgUrl(res.data),
						success(res) {

							that.codeImg = res.path
							that.drawImage()
						}
					})
				})
			},
			// 绘制二维码  H5
			getQrPath() {
				this.$fun.ajax.post('/user/gitH5Qrcode', {}).then(res => {
					if (res.status == 1) {
						this.text = res.data
						var that = this;
						setTimeout(function() {
							that.$refs.batchQRCode.toTempFilePath({
								success: res => {
									that.canvasQrPath = res.tempFilePath
									console.log(that.canvasQrPath )
									that.drawImage(0)
								},
								fail: err => {
									uni.showToast({
										icon: 'none',
										title: JSON.stringify(err)
									})
								}
							})
						}, 800)
					}
				})
			},
			// 画图
			drawImage(i) {
				var that = this;
					that.canvasHeight = that.list[i].bg_height
					uni.getImageInfo({
						src: this.imgbox[i],
						success(res) {
							let ctx = uni.createCanvasContext(`firstCanvas`) // 使用画布创建上下文 图片
							ctx.drawImage(res.path, 0, 0, 320,
								that.list[i].bg_height) // 设置图片坐标及大小，括号里面的分别是（图片路径，x坐标，y坐标，width，height）
							ctx.drawImage(res.path, 0, 0, 320,
								that.list[i].bg_height) // 设置图片坐标及大小，括号里面的分别是（图片路径，x坐标，y坐标，width，height）
							// x y
							let x = 320 * (that.list[i].x_axis_bl);
							let y = that.list[i].bg_height * (that.list[i].y_axis_bl);
							let x1 = x - 5;
							let y1 = that.list[i].bg_height * (that.list[i].y_axis_bl) + that.list[i].qr_height +
							5;
							ctx.moveTo(x - 5, y - 5)
							// 02 划线  坐标
							ctx.lineTo(x1, y1 + 30)
							ctx.lineTo(x + that.list[i].qr_width + 5, y1 + 30)
							ctx.lineTo(x + that.list[i].qr_width + 5, y - 5)
							ctx.lineTo(x1, y - 5)
							// 以上两行代码只是一个路径，但还没有绘制
							// 03 绘制
							ctx.fillStyle = "#FFFFFF"
							ctx.fill();

							// #ifdef MP-WEIXIN
							ctx.drawImage(that.codeImg, 320 * (that.list[i].x_axis_bl), that.list[i].bg_height * (
									that.list[i].y_axis_bl), that.list[i].qr_width,
								that.list[i].qr_height) // 设置图片坐标及大小，括号里面的分别是（图片路径，x坐标，y坐标，width，height）
							// #endif
							// #ifdef H5 || APP-PLUS
							ctx.drawImage(that.canvasQrPath, 320 * (that.list[i].x_axis_bl), that.list[i]
								.bg_height * (that.list[i].y_axis_bl), that.list[i].qr_width,
								that.list[i].qr_height) // 设置图片坐标及大小，括号里面的分别是（图片路径，x坐标，y坐标，width，height）
							ctx.textAlign = 'center'; //字体居中
							ctx.font = 'normal 20px STXingkai'; // 字体
							ctx.setFontSize(20) //设置字体大小，默认10
							ctx.setFillStyle('#000000') //文字颜色：默认黑色
							ctx.fillText(uni.getStorageSync('userinfo').invitation, x1 + 70, y1 +
							15); //文字内容、x坐标，y坐标
							// #endif

							ctx.save(); //保存
							ctx.draw() //绘制
							that.saveImgToLocal(`firstCanvas`)
						}
					})
				// }

			},
			saveImgFile(base64) { //base64为base64图片值
				const bitmap = new plus.nativeObj.Bitmap("test");
				bitmap.loadBase64Data(base64, function() {
					const url = "_doc/" + new Date().getTime() + ".png"; // url为时间戳命名方式
					console.log('saveHeadImgFile', url)
					bitmap.save(url, {
						overwrite: true, // 是否覆盖
						// quality: 'quality'  // 图片清晰度
					}, (i) => {
						uni.saveImageToPhotosAlbum({
							filePath: url,
							success: function() {
								uni.showToast({
									title: '图片保存成功',
									icon: 'none'
								})
								bitmap.clear()
							}
						});
					}, (e) => {
						uni.showToast({
							title: '图片保存失败',
							icon: 'none'
						})
						bitmap.clear()
					});
				}, (e) => {
					uni.showToast({
						title: '图片保存失败',
						icon: 'none'
					})
					bitmap.clear()
				});
			},
			// 保存图片
			saveImgToLocal(id, index = 0) {
				uni.canvasToTempFilePath({
						canvasId: 'firstCanvas',
						fileType: 'png',
						quality: 1, //图片质量
						success: function(result) {
							var tempFilePath = result.tempFilePath;
							console.log(tempFilePath)
						},
					});
				// let _this = this
				// uni.canvasToTempFilePath({
				// 	canvasId: 'firstCanvas',
				// 	fileType: 'png',
				// 	quality: 1, //图片质量
				// 	success: function(result) {
				// 		var tempFilePath = result.tempFilePath;
				// 		console.log(tempFilePath)
				// 		uni.downloadFile({
				// 			url: tempFilePath,
				// 			success: (res) => {
				// 				console.log(res);
				// 				_this.imgSrc = res.tempFilePath
				// 				//创建一个a标签
				// 				// var link = document.createElement('a');
				// 				// //把a标签的href属性赋值到生成好了的url
				// 				// link.href = res.tempFilePath;

				// 				// //通过a标签的download属性修改下载图片的名字
				// 				// link.download = '一起来休息一下.png';
				// 				// //让a标签的click函数，直接下载图片
				// 				// link.click();
				// 				// uni.saveImageToPhotosAlbum({
				// 				//                     filePath: res.tempFilePath,
				// 				//                     success: function() {
				// 				//                         uni.showToast({
				// 				//                             title: '已保存至相册',
				// 				//                             icon: 'none'
				// 				//                         })
				// 				//                     }
				// 				//                 });
				// 			}
				// 		})
				// 	},
				// });
			}
		}
	}
</script>

<style lang="scss">
	.content{
		position: fixed;
		width: 100vw;
		height: 100vh;
		top: 0;
		left: 0;
		background: #414141;
		.canvasImg{
			margin-top: 200rpx;
			text-align: center;
			z-index: 100;
			image{
				width: 600rpx;
				height: 900rpx;
			}
		}
		.imgBox{
			margin: 0 20rpx;
			width: calc(100vw - 40rpx);
			box-sizing: border-box;
			height: 200rpx;
			position: fixed;
			bottom: 20rpx;
			background: #FFFFFF;
			overflow-x: auto;
			display: flex;
			.imgItem{
				padding: 25rpx ;
				padding-left: 0;
				box-sizing: border-box;
				image{
					width: 100rpx;
					height: 150rpx;
				}
			}
			.imgItem:nth-child(1){
				padding-left: 25rpx;
			}
		}
	}
</style>
