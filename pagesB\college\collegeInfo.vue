<template>
	<view>
		<view class="activity-list">
			<view class="list" >
				
				<view class="item">
				
					<view class="pictrue" v-if="info.image">
						<image :src="$fun.imgUrl(info.image)" mode=""></image>
						<!-- <view class="hint">
							<text>活动结束</text>
						</view> -->
					</view>
					<view class="title">
						<text class="one-omit">{{info.name}}</text>
					</view>
					<view class="price-info">
						<view class="vip-price">
							<text>{{$u.timeFormat(info.createtime, 'yyyy.mm.dd')}}</text>
						</view>
						<view class="vip-price">
							<text>{{info.read}}已读</text>
						</view>
					</view>
					
				</view>
			</view>
			<view class="content" v-html="info.content">
				
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				info:{}
			}
		},
		methods: {
			
		},
		onLoad(option) {
			uni.setNavigationBarTitle({
				title: option.name
			})
			this.getInfo(option.id);
		},
		methods: {
			getInfo(id) {
				this.$fun.ajax.post('/news/clist',{id}).then(res=>{
					if(res.status==1){
						this.info = res.data;
					}
				})
			}
		},
	}
</script>

<style lang="scss">
.activity-list{
		padding: 20rpx 4%;
		.list{
			width: 100%;
			margin-bottom: 10px;
			.date{
				display: flex;
				align-items: center;
				justify-content: center;
				width: 100%;
				height: 100rpx;
				text{
					padding: 10rpx 30rpx;
					background-color: rgba(0,0,0,0.2);
					color: #FFFFFF;
					font-size: 26rpx;
					border-radius: 10rpx;
				}
			}
			.item{
				// height: 500rpx;
				background-color: #FFFFFF;
				border-radius: 10rpx;
				.title{
					padding: 0 4%;
					display: flex;
					align-items: center;
					width: 100%;
					height: 100rpx;
					text{
						font-size: 32rpx;
						color: #222222;
					}
				}
				.pictrue{
					position: relative;
					width: 100%;
					height: 336rpx;
					image{
						width: 100%;
						height: 100%;
					}
					.hint{
						padding: 0 4%;
						position: absolute;
						left: 0;
						top: 0;
						display: flex;
						align-items: center;
						justify-content: center;
						width: 100%;
						height: 100%;
						background-color: rgba(0,0,0,0.3);
						text{
							font-size: 32rpx;
							color: #FFFFFF;
						}
					}
				}
				.describe{
					padding: 0 4%;
					display: flex;
					align-items: center;
					width: 100%;
					height: 100rpx;
					text{
						font-size: 28rpx;
						color: #959595;
					}
				}
			}
		}
	}
	
.price-info {
	padding: 0 4%;
				// margin: 10rpx 20rpx;
				height: 60rpx;
				box-sizing: border-box;
				border-radius: 8rpx 8rpx 8rpx 8rpx;
				display: flex;
				justify-content: space-between;

				// background: #C93233;
				.user-price {
					// background: #310FFF;
					padding: 0 10rpx;
					display: flex;
					align-items: baseline;
					// margin-right: 10rpx;

					text {
						color: #F12F30;
					}

					.min {
						font-family: Source;
						font-weight: 500;
						font-size: 24rpx;
						color: #F12F30;
						text-align: left;
						font-style: normal;
						text-transform: none;
					}

					.max {
						font-family: Source;
						font-weight: 500;
						font-size: 32rpx;
						color: #F12F30;
						text-align: left;
						font-style: normal;
						text-transform: none;
					}
				}

				.vip-price {
					height: 60rpx;
					box-sizing: border-box;
					display: flex;
					justify-content: center;
					font-family: Source;
					align-items: center;
					padding-right: 14rpx;
					border-radius: 132rpx 132rpx 132rpx 132rpx;

					image {
						width: 26rpx;
						height: 26rpx;
						margin-right: 10rpx;
					}

					text {
						font-size: 24rpx;
						color: #AAAAAA;
					}
				}
			}
</style>
