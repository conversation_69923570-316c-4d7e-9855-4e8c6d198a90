<template>
	<view class="contacts">
		
		<view style="height: 10rpx;width: 100vw;background: #f5f5f5;">

		</view>
		<view class="list-cell u-border-bottom" v-for="(item, index) in list" :key="index">
			<chatItem :key="index" :list="item" :type="2" :b="false"></chatItem>
		</view>
		<!-- 		<u-index-list :index-list="indexList">
			<view v-for="(item, index) in list" :key="index">
				<u-index-anchor :index="item.letter" />
				<view class="list-cell u-border-bottom" v-for="(item1, index) in item.data" :key="index">
				<chatItem :key="index" :list="list1[0]" :type="2" :b="false"></chatItem>
				</view>
			</view>
		</u-index-list> -->
		<view style="height: 100rpx;">

		</view>
	</view>
</template>

<script>
	import chatItem from '../../components/chat-item/chat-item.vue';
	export default {
		components: {
			chatItem,
		},
		data() {
			return {
				titleStyle: {
					fontWeight: 400,
					fontSize: "32rpx",
					color: "#333333"
				},
				keyword: '',
				scrollTop: 100050,
				list: [],
			}
		},
		onLoad() {
			this.getList();
		},
		methods: {
			getList() {
				this.$fun.ajax.post(`chat/userList`, {type:1}).then(res => {
					if (res.status == 1) {
						this.list = res.data
					}
				})
			},
		}
	}
</script>

<style lang="scss">
	page {
		background: #FFFFFF;

		.contacts_search {
			padding: 32rpx;
		}

		.icon_l {
			width: 70rpx;
			height: 70rpx;
			border-radius: 11rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			background: #ff9900;
			margin-right: 20rpx;
		}

		.u-icon_r {
			color: #909399;
		}

		.g {
			background: #20c300;
		}

		.list-cell {
			padding: 20rpx 32rpx;
		}
	}
</style>