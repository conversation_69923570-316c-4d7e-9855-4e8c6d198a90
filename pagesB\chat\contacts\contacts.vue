<template>
	<view class="contacts">
		<view class="contacts_search">
			<u-search placeholder="搜索" @change="searchUser" v-model="keyword" :show-action="false"
				input-align="center"></u-search>
		</view>
		<u-cell-group>
			<u-cell-item title="新的朋友" @click="$fun.jump(`/pagesB/chat/add_friend/add_msg`)" :title-style="titleStyle">
				<view class="icon_l" slot="icon">
					<u-icon color="#FFFFFF" size="40" name="plus-people-fill"></u-icon>
				</view>
			</u-cell-item>
			<u-cell-item title="群聊" @click="$fun.jump(`./contacts1`)" :title-style="titleStyle">
				<view class="icon_l g" slot="icon">
					<u-icon color="#FFFFFF" size="40" name="chat-fill"></u-icon>
				</view>
				<!-- 	<view class="u-icon_r" slot="right-icon">
					12312
				</view> -->
			</u-cell-item>
		</u-cell-group>
		<view style="height: 10rpx;width: 100vw;background: #f5f5f5;">

		</view>
		<block v-for="(item, index) in list" :key="index">
			<view v-if="item.search==1" class="list-cell u-border-bottom">
				<chatItem :key="index" :list="item" :type="2" @clear="clearClick" :b="false"></chatItem>
			</view>
		</block>

		<!-- 		<u-index-list :index-list="indexList">
			<view v-for="(item, index) in list" :key="index">
				<u-index-anchor :index="item.letter" />
				<view class="list-cell u-border-bottom" v-for="(item1, index) in item.data" :key="index">
				<chatItem :key="index" :list="list1[0]" :type="2" :b="false"></chatItem>
				</view>
			</view>
		</u-index-list> -->
		<backHome></backHome>
		<view style="height: 100rpx;">

		</view>
		<TabBar :tabBarShow="1"></TabBar>
	</view>
</template>

<script>
	import TabBar from '@/components/TabBar1.vue';
	import indexList from "./index.list.js";
	const letterArr = indexList.list.map(val => {
		return val.letter;
	})
	import chatItem from '../../components/chat-item/chat-item.vue';
	export default {
		components: {
			chatItem,
			TabBar
		},
		data() {
			return {
				titleStyle: {
					fontWeight: 400,
					fontSize: "32rpx",
					color: "#333333"
				},
				keyword: '',
				scrollTop: 100050,
				indexList: letterArr,
				list: [],
			}
		},
		onShow() {
			this.getList();
		},
		methods: {
			clearClick() {
				this.keyword = '';
			},
			searchUser(e) {
				this.getList(e)
			},
			getList(search = '') {
				this.$fun.ajax.post(`chat/userList`, {
					search
				}).then(res => {
					if (res.status == 1) {
						this.list = res.data
					}
				})
			},
		}
	}
</script>

<style lang="scss">
	page {
		background: #FFFFFF;

		.contacts_search {
			padding: 32rpx;
		}

		.icon_l {
			width: 70rpx;
			height: 70rpx;
			border-radius: 11rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			background: #ff9900;
			margin-right: 20rpx;
		}

		.u-icon_r {
			color: #909399;
		}

		.g {
			background: #20c300;
		}

		.list-cell {
			padding: 20rpx 32rpx;
		}
	}
</style>