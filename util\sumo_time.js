// 根据当前时间获取本周一到本周日日期
function getDates() {
	// var new_Date = new Date(t2)
	var new_Date = new Date()
	var timesStamp = new_Date.getTime()
	var currenDay = new_Date.getDay()
	var dates = []
	for (var i = 0; i < 7; i++) {
		dates.push(new Date(timesStamp + 24 * 60 * 60 * 1000 * (i - (currenDay + 6) % 7)).toLocaleDateString().replace(
			/[年月]/g, '-').replace(/[日上下午]/g, ''));
	}
	var weekStar = new Date(dates[0] + ' 00:00:00').getTime()
	var weekEnd = new Date(dates[dates.length - 1] + ' 23:59:59').getTime()
	var week = [weekStar, weekEnd]
	return week
}
//判断是否是今天，昨天，明天
function isToday(str,type) {
	//type == 0 判断是否是今天，type == 1 判断是否是昨天，type == 2 判断是否是明天
	if (type == 0){
		if (new Date(str).toDateString() === new Date().toDateString()) {
			// console.log('今天');
		    return true
		} else{
			// console.log('不是今天');
			return false
		}
	}if (type == 1) {
		if (new Date(str).toDateString() === new Date(new Date().getTime() -1000 * 60 * 60 * 24).toDateString()) {
			// console.log('昨天');
		    return true
		} else{
			// console.log('不是昨天');
			return false
		}
	}
	if (type == 2) {
		if (new Date(str).toDateString() === new Date(new Date().getTime() +1000 * 60 * 60 * 24).toDateString()) {
			// console.log('明天');
		    return true
		} else{
			// console.log('不是明天');
			return false
		}
	}
}
// 根据时间，显示不同的内容 t1发布时间或者是截止时间2021/04/17 09:50:20 t2当前时间
export function sumo_time(t1) {
	// t1截止时间  t2当前时间
	// 调用getDtes函数把当前时间传过去，获取本周一和本周日时间戳
	// var weeks = getDates(t2)
	var weeks = getDates()
	// console.log(weeks)
	var dateBegin = new Date(t1.replace(/-/g,'/'))//解决ios时new Date(2021-04-17 09:50:20)报错
	// var dateEnd = new Date(t2)
	var dateEnd = new Date()
	// 时间戳
	var end = dateBegin.getTime()
	// console.log(end)
	var nows = dateEnd.getTime()
	// console.log(nows)
	// 获取当天0点时间戳todayStar
	const start = new Date(new Date(dateEnd).toLocaleDateString())
	start.setTime(start.getTime())
	var todayStar = new Date(start).getTime()
	// console.log(todayStar)
	// 获取当天23点59分59秒时间戳todayEnd
	var ends = new Date(new Date(new Date(dateEnd).toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 - 1)
	var todayEnd = new Date(ends).getTime()
	// console.log(todayEnd)
	// 时间差的毫秒数
	var dateDiff = Math.abs(dateBegin.getTime() - dateEnd.getTime())
	// 计算出相差天数
	var dayDiff = Math.floor(dateDiff / (24 * 3600 * 1000))
	// console.log(dayDiff);
	// 计算天数后剩余的毫秒数
	var leave1 = dateDiff % (24 * 3600 * 1000)
	// 计算出小时数
	var hours = Math.floor(leave1 / (3600 * 1000))
	// console.log(hours);
	// 计算相差分钟数
	var leave2 = leave1 % (3600 * 1000) // 计算小时数后剩余的毫秒数
	// 计算相差分钟数
	var minutes = Math.floor(leave2 / (60 * 1000))
	// console.log(minutes);
	// t1截止时间  t2当前时间
	// let TMW =t1.substring(10, 16);
	let TMW ='';
	if (dateEnd < dateBegin) {
		if ((dayDiff === 0)) {
			if (hours === 0) {
				if (minutes < 30 || minutes == 30) {
					return minutes + '分钟后'
				}else{
					return '今天' +TMW
				}
			} else {
				if (isToday(end,0)) {
					return '今天' +TMW
				}else{
					return '明天' +TMW
				}
			}
		} else if (dayDiff === 1) {
			if (isToday(end,2)){
				return '明天' +TMW
			}else{
				if ((weeks[0] < end || weeks[0] == end) && (weeks[1] > end || weeks[1] == end)) {
					var weekArray = new Array('日', '一', '二', '三', '四', '五', '六')
					var week = weekArray[new Date(t1.replace(/-/g,'/')).getDay()] // 注意此处必须是先new一个Date
					return '周' + week +TMW
				} else {
					return t1.substring(0, 16).replace(/-/g, '/')
				}
			}
		} else {
			if ((weeks[0] < end || weeks[0] == end) && (weeks[1] > end || weeks[1] == end)) {
				var weekArray = new Array('日', '一', '二', '三', '四', '五', '六')
				var week = weekArray[new Date(t1.replace(/-/g,'/')).getDay()] // 注意此处必须是先new一个Date
				return '周' + week +TMW
			} else {
				return t1.substring(0, 16).replace(/-/g, '/')
			}
		}
	}
	if (dateEnd === dateBegin) {
		return '刚刚'
	}
	if (dateEnd > dateBegin) {
		if (dayDiff === 0) {
			if (hours === 0) {
				if (minutes < 30 || minutes == 30) {
					if (minutes < 5 || minutes == 5) {
						return '刚刚'
					} else {
						return minutes + '分钟前'
					}
				} else {
					return '今天' +TMW
				}
			} else {
				if (isToday(end,0)) {
					return '今天' +TMW
				}else{
					return '昨天' +TMW
				}
			}
		} else if (dayDiff === 1) {
			if (isToday(end,1)){
				return '昨天' +TMW
			}else{
				if ((weeks[0] < end || weeks[0] == end) && (weeks[1] > end || weeks[1] == end)) {
					var weekArray = new Array('日', '一', '二', '三', '四', '五', '六')
					var week = weekArray[new Date(t1.replace(/-/g,'/')).getDay()] // 注意此处必须是先new一个Date
					return '周' + week +TMW
				} else {
					return t1.substring(0, 16).replace(/-/g, '/')
				}
			}
		} else {
			if ((weeks[0] < end || weeks[0] == end) && (weeks[1] > end || weeks[1] == end)) {
				var weekArray = new Array('日', '一', '二', '三', '四', '五', '六')
				var week = weekArray[new Date(t1.replace(/-/g,'/')).getDay()] // 注意此处必须是先new一个Date
				return '周' + week +TMW
			} else {
				return t1.substring(0, 16).replace(/-/g, '/')
			}
		}
	}
}
