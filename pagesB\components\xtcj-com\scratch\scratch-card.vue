<template>
	<view class="scratch_view">
		<view class="scratch_view_father" id="canvas">
			<cover-view class="scratch_cover_view">
				{{ rewardStrInfo }}
			</cover-view>
			<canvas :style="{width: width+'px', height : height+'px'}" class="abs scratch_canvas" :disable-scroll="true"
			 @touchstart="touchstart" @touchend="touchend" @touchmove="touchmove" canvas-id="scratch-card"></canvas>
		</view>
		<view class="reward_instruction">
			<text>{{rewardInstruction}}</text>
		</view>
	</view>
</template>

<script>
	let ctx = null;
	export default {
		name: "ScratchCard",
		props: {
			percentage: { //刮开百分之多少的时候开奖
				type: Number,
				default: 70
			},
			touchSize: { //触摸画笔大小
				type: Number,
				default: 20
			},
			fillColor: { //未刮开图层时的填充色
				type: String,
				default: '#ddd'
			},
			watermark: { //水印文字
				type: String,
				default: '刮一刮'
			},
			watermarkColor: { //水印文字颜色
				type: String,
				default: '#c5c5c5'
			},
			watermarkSize: { //水印文字大小
				type: Number,
				default: 14
			},
			title: { //提示文字
				type: String,
				default: '刮一刮开奖'
			},
			titleColor: { //提示文字颜色
				type: String,
				default: '#888'
			},
			titleSize: { //提示文字大小
				type: Number,
				default: 24
			},
			disabled: { //是否禁止刮卡
				type: Boolean,
				default: true
			},
			rewardStr: { //是否中奖
				type: String,
				default: ''
			},
			rewardInstruction:{//抽奖说明
				type: String,
				default: ''
			},
			cantManageFlag:{
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				width: 0,
				height: 0,
				startX: null,
				startY: null,
				computing: false,
				complete: false,
				stopRewardFlag:false,
				reset: false,
				ready: false,
				cantManage:this.cantManageFlag,
				cantRequest:true,
				rewardStrInfo:this.rewardStr
			};
		},
		mounted() {
			ctx = uni.createCanvasContext("scratch-card", this);
			this.getRect();
		},
		methods: {
			getRect(e) {
				const query = uni.createSelectorQuery().in(this);
				query.select('#canvas').boundingClientRect(data => {
					this.width = data.width;
					this.height = data.height;
					setTimeout(e => {
						this.init();
					}, 20);
				}).exec();
			},
	
			init(e) {
				this.computing = false;
				this.complete = false;
				this.reset = false;
				this.ready = false;
				ctx.clearRect(0, 0, this.width, this.height);
				//绘制画布
				ctx.setFillStyle(this.fillColor);
				ctx.fillRect(0, 0, this.width, this.height);
				this.ready = true;
				//绘制文字水印
				this.fillWatermark();
				//绘制标题
				this.fillTitle();
				ctx.draw();
			},
	
			/**
			 * 绘制文字水印
			 */
			fillWatermark(e) {
				if (!this.watermark) {
					return;
				}
				var width = this.watermark.length * this.watermarkSize;
				ctx.save();
				ctx.rotate(-10 * Math.PI / 180);
				let x = 0;
				let y = 0;
				let i = 0;
				while ((x <= this.width * 5 || y <= this.height * 5) && i < 300) {
					ctx.setFillStyle(this.watermarkColor);
					ctx.setFontSize(this.watermarkSize);
					ctx.fillText(this.watermark, x, y);
					x += width + width * 1.6;
					if (x > this.width && y <= this.height) {
						x = -Math.random() * 100;
						y += this.watermarkSize * 3;
					}
					i++;
				}
				ctx.restore();
			},
	
			/**
			 * 绘制标题
			 */
			fillTitle(e) {
				if (!this.title) {
					return;
				}
				ctx.setTextAlign("center");
				ctx.setTextBaseline("middle");
				ctx.setFillStyle(this.titleColor);
				ctx.setFontSize(this.titleSize);
				ctx.fillText(this.title, this.width / 2, this.height / 2);
			},
	
			touchstart(e) {
				if(this.stopRewardFlag){
					uni.showLoading({
						title: '抽奖结束~~'
					});
					return false;
				}
				this.startX = e.touches[0].x;
				this.startY = e.touches[0].y;
			},
	
			touchend(e) {
				this.getFilledPercentage();
			},
	
			touchmove(e) {
				if (this.complete || this.cantManage) {
					return;
				}
				ctx.moveTo(this.startX, this.startY);
				ctx.clearRect(this.startX, this.startY, this.touchSize, this.touchSize);
				ctx.draw(true);
				//记录移动点位
				this.startX = e.touches[0].x;
				this.startY = e.touches[0].y;
				//判断
				if(this.cantRequest){
					uni.canvasGetImageData({
						canvasId: 'scratch-card',
						x: 0,
						y: 0,
						width: this.width,
						height: this.height,
						success: (res) => {
							let pixels = res.data;
							let transPixels = [];
							for (let i = 0; i < pixels.length; i += 4) {
								if (pixels[i + 3] < 128) {
									transPixels.push(pixels[i + 3]);
								}
							}
							var percent = (transPixels.length / (pixels.length / 4) * 100).toFixed(2);
							if(percent>0){
								this.cantRequest=false;
								this.extractReward();
							}
						},
						fail: function(e) {
							console.log(e);
						},
					}, this);
				}
			},
	
			getFilledPercentage(e) {
				if (this.computing) {
					return;
				}
				this.computing = true;
				uni.canvasGetImageData({
					canvasId: 'scratch-card',
					x: 0,
					y: 0,
					width: this.width,
					height: this.height,
					success: (res) => {
						let pixels = res.data;
						let transPixels = [];
						for (let i = 0; i < pixels.length; i += 4) {
							if (pixels[i + 3] < 128) {
								transPixels.push(pixels[i + 3]);
							}
						}
						var percent = (transPixels.length / (pixels.length / 4) * 100).toFixed(2);
						if(percent>3&&this.cantRequest){
							this.cantRequest=false;
							this.extractReward();
						}
						if (percent >= this.percentage) {
							this.success();
						}
						this.computing = false;
						console.log(percent)
					},
					fail: function(e) {
						console.log(e);
					},
				}, this);
			},
	
			success(e) {
				this.complete = true;
				if (this.reset) {
					return;
				}
				this.reset = true;
				ctx.moveTo(0, 0);
				ctx.clearRect(0, 0, this.width, this.height);
				ctx.stroke();
				ctx.draw(true);
			},
			extractReward(){
				this.$emit("goPlayReward", {});
			},
			openReward(item){
				this.rewardStrInfo=item.reward_name
			},
			stopReward(){
				this.stopRewardFlag=true;
			}
		}
	}
</script>

<style>
	.scratch_view_father {
		width: 300rpx;
		height: 200rpx;
		margin: 0 auto;
		margin-top: 300rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		z-index:1;
	}

	.scratch_view_father .scratch_cover_view {
		display: inline-block;
		position: absolute;
		color: #F29100;
		font-size: 40rpx;
		z-index:2;
	}

	.scratch_view_father .scratch_canvas {
		display: inline-block;
		position: absolute;
		z-index:2;
	}
	.reward_instruction{
		margin:50rpx auto;
		width:80%;
		z-index:2;
	}
</style>
