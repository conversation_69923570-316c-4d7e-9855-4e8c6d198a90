	
	.page{
		//position: absolute;
		//width: 100%;
		//height: 100%;
		//overflow-x: hidden;
		//overflow-y: auto;
		padding-bottom: 100rpx;
		background-color: #f6f6f6;
	}
	.store1{
		display: flex;
		justify-content: space-between;
		align-items: center;
		flex-wrap: wrap;
		margin: 24rpx   !important;
		.store_address{
			margin-top: 24rpx;
			width: 100%;
			font-weight: 400;
			font-size: 24rpx;
			color: #666666;
		}
	}
	.store_info {
		padding:  20rpx;
		background: #FFFFFF;
		border-radius: 20rpx;
		margin: 0 24rpx;
		.store_btn{
			display: flex;
			align-items: center;
			justify-content: center;
			width: 162rpx;
			height: 60rpx;
			font-weight: 500;
			font-size: 28rpx;
			color: #FFFFFF;
			background: #310FFF;
			border-radius: 30rpx 30rpx 30rpx 30rpx;
		}
		.classify_store_top {
				display: flex;
				align-items: center;
		
				image {
					width: 100rpx;
					height: 100rpx;
					background: #FFFFFF;
					border-radius: 50%;
				}
		
				.info {
					margin-left: 16rpx;
					display: flex;
					flex-direction: column;
					width: calc(100vw - 400rpx);
					.title {
						font-weight: 400;
						font-size: 28rpx;
						color: #000000;
						font-weight: bold;
						display: -webkit-box;
						-webkit-line-clamp: 2;
						overflow: hidden;
						text-overflow: ellipsis;
						-webkit-box-orient: vertical;
						
						// width:calc(100% - 0rpx);
					}
		
					.text {
						font-weight: 400;
						font-size: 24rpx;
						color: #666666;
					}
				}
			}
		
		.info_item {
			display: flex;
			align-items: center;
			margin-bottom: 24rpx;

			image {
				width: 34rpx;
				height: 34rpx;
				margin: 0 20rpx;
			}

			.t {
				font-weight: 500;
				font-size: 28rpx;
				color: #333333;
				font-weight: bold;
				max-width: 560rpx;
				overflow: hidden;
				text-overflow: ellipsis;
				display: -webkit-box;
				-webkit-line-clamp: 2;
				-webkit-box-orient: vertical;
			}

			.i {
				color: #666666;
				font-size: 26rpx;
				font-weight: 500
			}
		}
	
		.flex{
			display: flex;
			flex-wrap: wrap;
			.i{
				width: 100%;
				font-weight: bold;
				font-size: 32rpx;
				color: #333333;
				text-align: center;
				padding-bottom: 24rpx;
				border-bottom: 2rpx solid #F4F4F4;
			}
			.info_content{
				width: 100%;
				font-weight: bold;
				font-size: 28rpx;
				margin: 24rpx 0;
				color: #333333;
			}
			.mt_box{
				width: 100%;
				display: flex;
				flex-direction: column;
				image{
					width: 100%;
					margin: 0 !important;
				}
			}
		}
	}
	
	.submit-btn{
		display: flex;
		align-items: center;
		justify-content: center;
		width: 94%;
		height: 80rpx;
		margin: 30rpx auto;
		background: linear-gradient(to right,$base,$change-clor);
		border-radius: 80rpx;
		box-shadow: 0 10rpx 10rpx $base;
		text{
			color: #FFFFFF;
			font-size: 28rpx;
		}
	}