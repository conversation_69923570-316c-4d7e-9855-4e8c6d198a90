<template>
	<view class="tabs">
		<!-- 		<swiper class="tab-box" ref="swiper1" :duration="300" :current="tabIndex" @transition="onswiperscroll"
			@animationfinish="animationfinish" @onAnimationEnd="animationfinish" @change="onswiperchange">
			<swiper-item class="swiper-item" v-for="(page, index) in tabList" :key="index"> -->
		<view style="flex: 1;position: relative;">
			<tabsPage :ref="'page' + 0" :pageIndex="0" :tabIndex="tabIndex" :tabItem="{cid,id}"
				:statusBarHeight="statusBarHeight"></tabsPage>
		</view>
		<!-- 		</swiper-item>
		</swiper> -->
 v-if="isBottom" :tabBarShow="2"></TabBar>
		<!-- 解决app端视频滑动进度条和swiper冲突滑动遮罩图层 -->
		<!-- #ifdef APP-PLUS -->
		<view class="vodProgress" @touchmove="touchmoveSlider" @touchstart="touchmoveSlider" @touchend="touchendSlider"
			v-if="tabIndex != 0"></view>
		<!-- #endif -->

		<!-- <view class="head f-lv-c" :style="{top:statusBarHeight+'px',width:screenWidth+'px'}">
			<view class="head-item" @click="tabChange(index)" v-for="(item,index) in tabList" :key="index">
				<text class="head-item-text" :class="tabIndex == index ?'head-item-textActive':''">{{item.name}}</text>
				<view class="head-item-sign" v-if="tabIndex == index"></view>
			</view>
		</view> -->

	</view>
</template>
<script>
	const deviceInfo = uni.getSystemInfoSync()
	import tabsPage from './tabs/tabs-page.vue'
	export default {
		components: {
			tabsPage
		},
		computed: {
			isBottom() {
				return false
				// return uni.getStorageSync('isbottom')
			}
		},
		data() {
			return {
				statusBarHeight: deviceInfo.statusBarHeight,
				screenWidth: deviceInfo.screenWidth || 0, //屏幕的宽度
				screenHeight: deviceInfo.screenHeight, //屏幕的高度
				tabList: [],
				tabIndex: 0,
				pageList: [],
				tapOpen: false,
				cid: '',
				id: '',
			}
		},
		onLoad(option) {
			this.cid = option.cid;
			this.id = option.id;
		},
		async onReady() {
			await this.$fun.ajax.post(`/category/list`, {
				type: 'kvideo'
			}).then(res => {
				if (res.status == 1) {
					console.log(res.data)
					this.tabList = [];
					for (var i = 0; i < res.data.length; i++) {
						this.tabList.push({
							id: `tab0${i+1}`,
							cid: res.data[i].id,
							name: res.data[i].name
						})
					}
				}
			})
			console.log(this.tabList)
			for (var i = 0; i < this.tabList.length; i++) {
				let item = this.$refs['page' + i]
				if (Array.isArray(item)) {
					console.log(12312)
					this.pageList.push(item[0])
				} else {
					console.log(22222)
					this.pageList.push(item)
				}
			}
			this.changeSwitchTab(this.tabIndex)

		},
		onShow() {
			/* 播放视频 */
			this.$nextTick(() => {
				if (this.pageList.length > 0 && this.pageList[this.tabIndex].firstLoaded && this.tabIndex != 0) {
					this.pageList[this.tabIndex].showPlay()
				}
			})
		},
		onHide() {
			/* 暂停视频 */
			this.$nextTick(() => {
				if (this.pageList.length > 0 && this.pageList[this.tabIndex].firstLoaded && this.tabIndex != 0) {
					this.pageList[this.tabIndex].hidePause()
				}
			})
		},
		onUnload() {
			uni.setStorageSync('isbottom', false);
			uni.removeStorageSync('lookUser');
		},
		methods: {
			tabChange(index) {
				this.tabIndex = index
				this.tapOpen = true
				this.changeSwitchTab(index)
			},
			changeSwitchTab(index) {
				this.$nextTick(() => {
					this.tapOpen = false
					if (!this.pageList[this.tabIndex].firstLoaded) {
						this.pageList[this.tabIndex].loadTabData()
					}
				})
			},
			/* 必须存在，否则animationfinish的下标会失效 */
			onswiperchange(e) {
				let index = e.detail.current
				this.tabIndex = index
				console.log(e)
				if (!this.tapOpen) {
					this.changeSwitchTab(index)
				}
			},
			onswiperscroll(e) {
				// this.$refs.tabRef.onswiperscroll(e)
			},
			animationfinish(e) {
				// this.$refs.tabRef.animationfinish(e)
			},
			touchmoveSlider(event) {
				this.pageList[this.tabIndex].tabPageTouchmoveSlider(event)
			},
			touchendSlider(event) {
				this.pageList[this.tabIndex].tabPageTouchendSlider(event)
			}
		}
	}
</script>
<style scoped lang="scss">
	.tabs {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: #000000;
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: column;
		overflow: hidden;
	}

	.head {
		position: absolute;
		top: 0;
		flex-direction: row;
		justify-content: center;

		&-item {
			position: relative;
			height: 75rpx;
			flex-direction: column;
			justify-content: center;
			align-items: center;

			&-text {
				color: #eee;
				font-size: 32rpx;
				margin: 0 20rpx;
				font-weight: bold;
			}

			&-textActive {
				color: #ffffff;
				font-size: 34rpx;
			}

			&-sign {
				position: absolute;
				bottom: 0;
				left: 30rpx;
				width: 50rpx;
				height: 5rpx;
				background-color: #ffffff;
			}
		}
	}

	.tab-box {
		flex: 1;
	}

	.swiper-item {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex: 1;
		flex-direction: column;
	}

	.page-item {
		flex: 1;
		flex-direction: row;
		position: absolute;
		left: 0;
		top: 0;
		right: 0;
		bottom: 0;
	}

	.vodProgress {
		position: absolute;
		bottom: 0;
		height: 18px;
		width: 750rpx;
	}

	.f-lv-c {
		display: flex;
		justify-content: space-around;
	}
</style>