<template>
	<view class="group">
		<view class="group_box">
			<scroll-view class="scroll-view" scroll-x="true">
				<view class="avatar_box">
					<u-avatar class="avatar" v-for="(item,index) in groupList" :key="index"
						:src="$fun.imgUrl(item.avatar)"></u-avatar>
				</view>
			</scroll-view>
			<view class="input_search">
				<u-search @change="searchUser" :show-action="false" placeholder="搜索" v-model="keyword"></u-search>
			</view>
		</view>
		<view style="height: 30rpx;background: #f6f6f6;">

		</view>
		<view class="group_list">
			<u-checkbox-group @change="checkboxGroupChange">
				<block v-for="(item,index) in list" :key="index">
					<u-checkbox v-model="item.checked" :disabled="item.disabled" :name="item.id" v-if="item.search==1">
						<chatItem :type="5" :list="item"></chatItem>
					</u-checkbox>
				</block>
			</u-checkbox-group>
		</view>
	</view>
</template>

<script>
	import chatItem from '../../components/chat-item/chat-item.vue';
	export default {
		components: {
			chatItem
		},
		data() {
			return {
				keyword: '',
				list: [],
				groupList: [],
				groupListId: [],
				isClick: false,
				id: ''
			};
		},
		onLoad(option) {
			this.id = option.id;
			this.getList(option.id);
		},
		onNavigationBarButtonTap(e) {
			if (this.isClick) {
				return
			}
			let uids = "";
			for (var i = 0; i < this.groupList.length; i++) {
				if (i == this.groupList.length - 1) {
					uids += this.groupList[i].addId;
				} else {
					uids += this.groupList[i].addId + ',';
				}
			}
			this.isClick = true;
			this.$fun.ajax.post(`chat/addGroup`, {
				uids,
				id: this.id ? this.id : ''
			}).then(res => {
				if (res.status == 1) {
					this.$fun.msg(res.msg);
					this.$fun.jump('', 4, 1200);
					setTimeout(() => {
						this.isClick = false;
					}, 1200)
				} else {
					this.isClick = false;
				}
			})
		},

		methods: {
			searchUser(e) {
				this.getList(this.id, e)
			},
			getList(id = '', search = '') {
				this.$fun.ajax.post(`chat/userList`, {
					id,
					search
				}).then(res => {
					if (res.status == 1) {
						this.list = res.data;
						// for (let i = 0; i < this.list.length; i++) {
						// 	this.list[i].checked = this.list[i].checked ? true : false;
						// 	this.list[i].disabled = this.list[i].disabled ? true : false;
						// 	if (this.list[i].inChecked) {
						// 		this.list[i].checked = true;
						// 		this.list[i].disabled = true;
						// 		this.groupList.push(this.list[i])
						// 	}
						// }
						let list = [];
						for (let i = 0; i < this.list.length; i++) {
							this.list[i].checked = this.list[i].checked ? true : false;
							this.list[i].disabled = this.list[i].disabled ? true : false;
							if (this.list[i].inChecked) {
								this.list[i].checked = true;
								this.list[i].disabled = true;
							}
							for (let a = 0; a < this.groupList.length; a++) {
								if (this.list[i].id == this.groupList[a].id) {
									this.list[i].checked = true;
								}
							}
						}
						for (let i = 0; i < this.list.length; i++) {
							if (this.list[i].checked) {
								list.push(this.list[i]);
							}
						}
						this.groupList = list;
					}
					document.querySelector('.uni-page-head-ft .uni-page-head-btn .uni-btn-icon').innerHTML =
						`完成(${this.groupList.length})`;
				})
			},
			checkboxGroupChange(e) {
				console.log(e);
				this.groupList = [];
				this.keyword = '';
				for (let i = 0; i < this.list.length; i++) {
					if (this.list[i].checked) {
						this.groupList.push(this.list[i])
					}
				}
				document.querySelector('.uni-page-head-ft .uni-page-head-btn .uni-btn-icon').innerHTML =
					`完成(${this.groupList.length})`;
				// this.getList();
			}
		}
	}
</script>

<style lang="scss">
	page {
		background: #FFFFFF;
		padding: 32rpx 0;

		.group_box {
			padding: 0 32rpx;
			width: 100vw;
			display: flex;
			align-items: center;
			box-sizing: border-box;

			.scroll-view {
				white-space: nowrap;
				width: fit-content;
				max-width: calc(100% - 200rpx);
				height: 100rpx;

				.avatar_box {
					height: 100rpx;

					.avatar {
						margin-right: 5rpx;
					}
				}
			}

			.input_search {
				margin-left: 20rpx;
				width: 150rpx;
			}
		}

		.group_list {
			padding: 0 32rpx;
			box-sizing: border-box;
		}
	}
</style>