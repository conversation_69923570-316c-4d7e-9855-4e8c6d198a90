<template>
	<view class="page">

		<view class="logo">
			<view class="login_title">
				<view>
					您好，
				</view>
				<view>
					欢迎登录
				</view>
			</view>
			<image :src="$fun.imgUrl('/static/login_top.png')" mode="widthFix"></image>
		</view>
		<view style="height:430rpx ;">

		</view>
		<view class="login_content">
			<!-- 填写区 -->
			<view class="input-info" v-if="confogInfo.login.indexOf('user')!=-1">
				<view class="title" style="  margin-top: 0;">
					账号
				</view>
				<view class="info">

					<input type="text" v-model="form.phone" placeholder="请输入您的账号">
					<view class="more"></view>
				</view>
				<view class="title" :style="isLoginWay?'':'display: none'">
					验证码
				</view>
				<view class="info" :style="isLoginWay?'':'display: none'">
					<input type="tel" v-model="form.code" maxlength="6" placeholder="请输入您的验证码">
					<view class="more">
						<text class="mo">获取验证码</text>
						<text class="mo" style="display: none">59秒后重试</text>
					</view>
				</view>
				<view class="title" :style="isLoginWay?'display: none':''">
					密码
				</view>
				<view class="info" :style="isLoginWay?'display: none':''">
					<input :password='!isPassword' v-model="form.password" maxlength="26" placeholder="请输入您的密码">
					<!-- <view class="more">
						<text class="iconfont" :class="isPassword?'icon-eye-on':'icon-eye-off'"
							@click="isPassword = !isPassword"></text>
						<text class="mo" @click="$fun.jump('../forget/forget')">忘记密码</text>
					</view> -->
				</view>
			</view>
			<!-- 操作 -->
			<view class="operation" v-if="confogInfo.login.indexOf('user')!=-1">
				<text></text>
				<text @click="$fun.jump('../forget/forget')">忘记密码?</text>
			</view>
			<!-- 按钮 -->
			<view class=" btn-info" v-if="confogInfo.login.indexOf('user')!=-1">
				<view class="btn" @click=" onLogin()">
					<text>登录</text>
				</view>
			</view>
			<view class=" btn-info" style="background: #ECF0FB;color: #000000;margin-top: 24rpx;"
				v-if="confogInfo.login.indexOf('user')!=-1">
				<view class="btn" @click="$fun.jump('../register/register')">
					<text>注册</text>
				</view>
			</view>

			<!-- 其他方式登录 -->
			<!-- 	<view class="other-ways" v-if="confogInfo.login.length>=2">
				<text>其他登录方式</text>
			</view> -->
			<view style="height: 200rpx;" v-if="confogInfo.login.indexOf('user')==-1">

			</view>
			<!-- 登录方式 -->
			<!-- <view class="login-way"> -->
			<!-- #ifdef H5 -->
			<!-- <view class="way" @click="wxLogin" v-if="confogInfo.login.indexOf('wechat')!=-1">
					<image :src="$fun.imgUrl('/static/wx_ico.png')" mode=""></image>
					<text>微信登录</text>
				</view> -->
			<!-- #endif -->
			<!-- #ifdef MP-WEIXIN -->
			<view class="way" @click="wxLogin"
				style="display: flex;justify-content: center;flex-direction: column;align-items: center;"
				v-if="confogInfo.login.indexOf('wechatmini')!=-1">
				<image :src="$fun.imgUrl('/static/wx_ico.png')" style="width: 100rpx;" mode="widthFix"></image>
				<text>微信登录</text>
			</view>
			<!-- #endif -->

			<!-- </view> -->


		</view>
		<view class="position: fixed;  bottom: 20rpx;background:#FFFFFF">
			<view style="display: flex;justify-content: center;">
				<u-checkbox-group @change="checkboxGroupChange">
					<u-checkbox active-color="#310FFF" v-model="checked">

					</u-checkbox>
				</u-checkbox-group>
				<view class="zc">
					<text> 我已同意 </text>
					<view class="t" @click="$fun.jump(`/pages/home/<USER>">
						隐私政策
					</view>

					<text> 和 </text>
					<view class="t" @click="$fun.jump(`/pages/home/<USER>">
						用户协议
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				isLogin: false,
				isLoginWay: false,
				isPassword: false,
				// 表单
				form: {
					phone: '',
					code: '',
					password: '',

				},
				userConfig: {
					login: []
				},
				confogInfo: {
					login: []
				},
				checked: false,
				invitation: '',

			};
		},
		// #ifdef MP-WEIXIN || H5 || APP-PLUS
		onLoad(option) {
			if (option.scene) {
				this.$fun.saveScene(option.scene)
			}
			if (option.invitation) {
				this.invitation = option.invitation ? option.invitation : '';
			}
			if (option.account && option.pwd) {

				this.form.phone = option.account
				this.form.password = option.pwd
				this.onLogin()
			}
		},
		// #endif
		onShow() {

			this.getLoginConfig()
		},
		methods: {
			checkboxGroupChange() {
				console.log(this.checked)
			},
			getLogin() {
				//  if (this.$fun.getUrlCode().code && !uni.getStorageSync('userInfo')) {
				//   let prams = this.$fun.getUrlCode()
				//  	this.$fun.ajax.post('user/login', prams).then(res => {
				//  		if (res.status == 1) {
				//  			this.$store.commit('loginStatus', true)
				//  			uni.setStorageSync('token', res.data.token);
				//  			uni.setStorageSync('userinfo', res.data.userinfo);
				//  			this.$fun.jump('/pages/home/<USER>',3)
				//  		}
				//  	})
				// }
				// uni.clearStorage()
				if (!this.$fun.getUrlCode().code) {
					let url = this.$fun.wxLogin()
					if (this.invitation) {
						url += `?invitation=${this.invitation}&t=user`
					} else {
						url += `?t=user`
					}
					location.href = url;
				}
			},
			// 第三方注册 微信
			wxLogin() {
				if (!this.checked) {
					this.$fun.msg('请先阅读隐私政策和注册协议');
					return
				}
				uni.showLoading({
					title: '加载中...'
				})
				let _that = this
				// #ifdef H5
				location.href = this.$fun.wxLogin()
				// #endif
				// #ifdef MP-WEIXIN
				uni.getUserProfile({
					desc: 'weixin',
					success: function(infoRes) {
						console.log(infoRes)
						uni.login({
							provider: 'weixin',
							success: function(loginRes) {
								let prams = {
									platform: 'wechatmini',
									code: loginRes.code,
									...infoRes.userInfo,
									scene: uni.getStorageSync('saveScene') ? uni
										.getStorageSync('saveScene') : ''
								}
								_that.$fun.ajax.post('user/login', prams).then(res => {
									uni.hideLoading()
									if (res.status == 1) {
										_that.$fun.msg('登录成功')
										_that.$store.commit('loginStatus', true)
										uni.setStorageSync('token', res.data.token);
										uni.setStorageSync('userinfo', res.data.userinfo);
										uni.setStorageSync('openid', res.data.openid);
										if (!res.data.userinfo.isup) {
											_that.$fun.jump(
												'/pages/my/set/setUserInfo?type=2',
												3, 0)
										} else {
											_that.$fun.jump(
												'/pages/my/my', 3, 0)
										}

									}
								})
							},
							fail: function() {

							}
						})
					},
					fail: function() {
						_this.btnLoading = false;
					}
				})
				// #endif
			},
			/**
			 * 获取登录信息  
			 */
			getLoginConfig() {
				this.$fun.ajax.post('config/index', {}).then(res => {
					if (res.status == 1) {
						this.confogInfo = res.data
						if (res.data.login.indexOf('wechat') != -1) {
							// #ifdef H5
							this.getLogin()
							// #endif
						}
					}
				})
			},
			onRegister() {
				uni.navigateTo({
					url: '/pages/register/register'
				})
			},
			/**
			 * 登录切换
			 */
			onLoginCut() {
				this.isLoginWay = !this.isLoginWay;
				// 验证码
				if (this.isLoginWay) {
					this.isLogin = this.form.code && this.form.phone ? true : false;
				}
				// 账号密码
				if (!this.isLoginWay) {
					this.isLogin = this.form.password && this.form.phone ? true : false;
				}
			},
			/**
			 * 登录点击
			 */
			onLogin() {
				let data = {
					account: this.form.phone,
					password: this.form.password
				}
				if (!this.checked) {
					this.$fun.msg('请先阅读隐私政策和注册协议');
					return
				}
				let _this = this;
				// #ifdef MP-WEIXIN
				uni.login({
					provider: 'weixin',
					success: function(loginRes) {
						_this.$fun.ajax.post('user/login', {
							...data,
							// platform:'wechatmini',
							code: loginRes.code
						}).then(res => {
							_this.$fun.msg(res.msg)
							if (res.status == 1) {
								uni.setStorageSync('token', res.data.token);
								uni.setStorageSync('userinfo', res.data.userinfo);
								uni.setStorageSync('openid', res.data.openid);
								setTimeout(() => {
									_this.$fun.jump('/pages/my/my', 3)
								}, 1500);
							}
						})
					}
				})
				return
				// #endif
				data = {
					account: this.form.phone,
					password: this.form.password
						// #ifdef H5
						,
					code: this.$fun.getUrlCode().code ? this.$fun.getUrlCode().code : ''
					// #endif
				}
				this.$fun.ajax.post('user/login', {
					...data
				}).then(res => {
					this.$fun.msg(res.msg)
					if (res.status == 1) {
						uni.setStorageSync('token', res.data.token);
						uni.setStorageSync('userinfo', res.data.userinfo);
						// #ifdef H5
						uni.setStorageSync('openid', res.data.openid);
						setTimeout(() => {
							location.href = this.$fun.imgUrl('/#/pages/my/my')
						}, 1200);
						return;
						// #endif
						setTimeout(() => {
							this.$fun.jump('/pages/my/my', 3)
						}, 1500);
					}
				})
			}
		},
		watch: {
			form: {
				handler(newValue, oldValue) {
					// 验证码
					if (this.isLoginWay) {
						this.isLogin = newValue.code && newValue.phone ? true : false;
					}
					// 账号密码
					if (!this.isLoginWay) {
						this.isLogin = newValue.password && newValue.phone ? true : false;
					}
				},
				deep: true
			}
		}
	}
</script>

<style scoped lang="scss">
	@import 'login.scss';

	.zc {
		display: flex;
		font-size: 28rpx;
		align-items: center;
		color: #000000;


		.t {
			display: inline-block;
			margin: 0 10rpx;
			color: #310FFF;
		}
	}
</style>