<template>
	<view>
		<view class="head-info">
			<image class="bg" src="/static/head_bg1.png"></image>
			<!-- 搜索 -->
			<view class="head-search" >
				<view class="icon-info" style="width: 50px;" @click="$fun.jump(``,5,0)">
					<text class="iconfont icon-more" style="transform: rotate(180deg);font-size: 16px;"></text>
				</view>
				<view class="search">
					流量套餐充值
				</view>
				<view class="icon-info " style="width: 50px;color:#FFFFFF;font-size:12px"  @click="$fun.jump(`./recharge_list`,0,0)">
					充值记录
					<!-- <text class="iconfont icon-fukuanma"></text> -->
					<!-- <image src="/static/fkm_ico.png" mode=""></image> -->
				</view>
			</view>
		</view>
		<!-- 		<view class="cont_positon">
			
		</view> --> 
		<view class="imgPosition">
			<image :src="$fun.imgUrl('/static/head_bg2.png')" style="width: 100%;" mode="widthFix"></image>
		</view>
		<view class="viewPosition">
			<view class="input_box">
				<view class="title">
					卡号：
				</view>
				<input type="number" v-model="code" maxlength="11" placeholder-style="font-size:30rpx" placeholder="请输入热推云卡卡号">
			</view>
			<view class="select_box">
				<view :class="activeIndex==index?'select_item active':'select_item'" @click="clickItem(item,index)"
					v-for="(item,index) in rechargeList.moneyjson">
					<text>{{item.sign}}</text>
					<text>{{item.money}}</text>
				</view>
			</view>
		</view>
		<view style="margin-top: 200rpx;padding: 0 20rpx;" v-html="rechargeList.content">
			
		</view>
		<view class="footer-btn">
			<view class="btn" @click="recharge()">
				<text>确认支付</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				rechargeList: {
					moneyjson: []
				},
				activeIndex: null,
				code:''
			}
		},
		onLoad() {
			this.getRechargeList()
		},
		methods: {
			// 充值列表
			getRechargeList() {
				this.$fun.ajax.post('bill/getbill', {}).then(res => {
					if (res.status == 1) {
						this.rechargeList = res.data
					}
				})
			},
			// 选择套餐
			clickItem(item, index) {
				this.activeIndex = index
			},
			recharge(){
				if(!this.code){
					this.$fun.msg('请输入热推云卡卡号')
					return
				}
				if(this.activeIndex==null){
					this.$fun.msg('请选择套餐')
					return
				}
				this.$fun.ajax.post('bill/setbill', {code:this.code,id:this.activeIndex}).then(res => {
					if (res.status == 1) {
						this.$fun.jump(`/pages/home/<USER>
					}
				})
			}
		}
	}
</script>

<style lang="scss">
	page {
		background: #FFFFFF
	}

	.cont_positon {
		padding: calc(20rpx + var(--status-bar-height)) 25rpx 0;
		height: calc(100rpx + var(--status-bar-height));
	}

	.head-info {
		position: fixed;
		left: 0;
		top: 0;
		z-index: 10;
		width: 100%;
		/* #ifdef APP-PLUS||H5 */
		padding: calc(20rpx + var(--status-bar-height)) 25rpx 0;
		height: calc(100rpx + var(--status-bar-height));
		/* #endif */
		/* #ifdef MP */
		padding: 0 25rpx;
		height: 170rpx;
		/* #endif */
		// background: url(/static/head_bg1.png) no-repeat;
		// background-size: 100% 433rpx;
		background-color: #FFFFFF;
		overflow: hidden;
		position: relative;

		.bg {
			position: absolute;
			top: 0;
			left: 0;
			z-index: 10;
			width: 100vw;
			height: calc(100rpx + var(--status-bar-height));
		}

		.head-search {
			display: flex;
			align-items: center;
			justify-content: space-between;

			.icon-info {
				display: flex;
				align-items: center;
				height: 100%;
				z-index: 11;

				text {
					font-size: 52rpx;
					color: #f6f6f6;
				}

				image {
					width: 42rpx;
					height: 43rpx;
				}
			}

			.search {
				display: flex;
				align-items: center;
				// width: 100%;
				padding: 0 20rpx;
				height: 65rpx;
				text-align: center;
				justify-content: center;
				z-index: 11;
				// background-color: rgba(255,255,255,0.3);
				// border-radius: 10rpx;
				color: #FFFFFF;

				.icon {
					display: flex;
					align-items: center;
					margin-right: 20rpx;

					image {
						width: 27rpx;
						height: 29rpx;
					}
				}

				.hint {
					display: flex;
					align-items: center;

					.max {
						font-size: 30rpx;
						font-weight: bold;
						color: #FFFFFF;
					}

					.min {
						font-size: 24rpx;
						color: #F6f6f6;
						margin-left: 10rpx;
					}
				}
			}
		}

		.classify-list {
			white-space: nowrap;
			width: 100%;
			height: 100rpx;
			overflow-x: auto;
			overflow-y: hidden;

			.list {
				position: relative;
				display: inline-block;
				width: 20%;
				height: 100%;
				line-height: 100rpx;
				text-align: center;

				text {
					font-size: 28rpx;
					color: #FFFFFF;
					opacity: 0.8;
				}

				.line {
					position: absolute;
					left: 50%;
					bottom: 20rpx;
					width: 60%;
					height: 8rpx;
					background: linear-gradient(to right, #f8f893, #fe9d00);
					border-radius: 10rpx;
					transform: translate(-50%, 0);
				}
			}

			.action {
				text {
					font-size: 32rpx;
					opacity: 1;
				}
			}
		}
	}

	.viewPosition {
		position: absolute;
		top: calc(100rpx + var(--status-bar-height) + 50rpx);
		margin: 0 30rpx;
		border-radius: 20rpx;
		width: calc(100vw - 60rpx);
		min-height: 500rpx;
		background: #FFFFFF;
		padding: 20rpx;
	
	.input_box {
			display: flex;
			align-items: center;
			padding-bottom: 30rpx;
			border-bottom: 1px solid #EEEEEE;

			.title {
				font-size: 30rpx;
				font-family: Adobe Heiti Std;
				font-weight: normal;
				color: #333333;
			}

			input {
				font-weight: bold;
				font-size: 42rpx;
				font-family: FZLanTingHeiS-B-GB;
				font-weight: 400;
			}
		}

		.select_box {
			margin-top: 54rpx;
			display: flex;
			flex-wrap: wrap;

			.select_item {
				margin-right: 29rpx;
				margin-bottom: 28rpx;
				width: 197rpx;
				height: 130rpx;
				border: 2rpx solid #EEEEEE;
				border-radius: 10rpx;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;

				text:nth-child(1) {
					font-size: 28rpx;
					font-family: FZLanTingHeiS-B-GB;
					font-weight: bold;
					color: #310FFF;
				}

				text:nth-child(2) {
					font-size: 22rpx;
					font-family: Microsoft YaHei UI;
					font-weight: bold;
					color: #1B85FB;
				}
			}

			.select_item:nth-child(3n+3) {
				margin-right: 0;
				width: 197rpx;
				height: 130rpx;
				border-radius: 10rpx;
			}

			.active {
				border: 2rpx solid #310FFF;
				background: rgba(255, 76, 21, .2);
			}
		}
	}

	.footer-btn {
		position: fixed;
		left: 0;
		bottom: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 100rpx;
		background-color: #FFFFFF;

		.btn {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 80%;
			height: 70rpx;
			background: linear-gradient(to right, $base, $change-clor);
			border-radius: 70rpx;
			box-shadow: 0 10rpx 10rpx $base;

			text {
				font-size: 28rpx;
				color: #FFFFFF;
			}
		}
	}
</style>
