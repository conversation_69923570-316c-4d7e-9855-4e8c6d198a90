import Vue from 'vue';
import App from './App'; // 弹出框

import DialogBox from './components/DialogBox/DialogBox';
import backHome from '@/components/rf-back-home';
import store from "./store";
Vue.prototype.$store = store;
Vue.config.productionTip = false;
import fun from './util/fun.js'; 
import env from "./util/env.js";
// import Vconsole from 'vconsole';
// new Vconsole()
let baseUrl;

if (process.env.NODE_ENV === 'development') {
	// 线上环境
	baseUrl = env.dev.baseUrl;
} else {
	// 开发环境
	baseUrl = env.prod.baseUrl;
} // 全局组件
// #ifdef H5
let ua = window.navigator.userAgent.toLowerCase()
if (ua.match(/MicroMessenger/i) == 'micromessenger') {
	// do something
	// document.getElementsByTagName('uni-page-head')[0].style.display = 'none'  
}
// #endif
Vue.component('DialogBox', DialogBox); //mescroll
Vue.component('backHome', backHome); //mescroll

import uView from 'uview-ui';
Vue.use(uView);
import passkeyborad from '@/components/yzc-paykeyboard/yzc-paykeyboard.vue';
import shoproEmpty from '@/components/shopro-empty/shopro-empty.vue';
Vue.component('passkeyborad', passkeyborad);
Vue.component("shoproEmpty", shoproEmpty);
import MescrollBody from "@/components/mescroll-uni/mescroll-body.vue";
import MescrollUni from "@/components/mescroll-uni/mescroll-uni.vue";
import uSticky from '@/components/f-sticky/f-sticky.vue';
Vue.component('mescroll-body', MescrollBody);
Vue.component('mescroll-uni', MescrollUni);
Vue.component('u-sticky', uSticky);
Vue.prototype.$fun = fun;
Vue.prototype.$baseUrl = baseUrl;
App.mpType = 'app';
const app = new Vue({
	store,
	...App
});
app.$mount();