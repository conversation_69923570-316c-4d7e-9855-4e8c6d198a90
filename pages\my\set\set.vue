<template>

	<view class="page">
		<mescroll-body ref="mescrollRef" @down="downCallback" @up="upCallback" :down="downOption" :up="upOption"
			:top="0">
			<!-- 用户信息 -->
			<view class="user-info">
				<view class="user-data" @click="$fun.jump('./setUserInfo')">
					<view class="portrait-nickname">
						<view class="portrait">
							<image :src="$fun.imgUrl(userInfo.avatar)" mode=""></image>
						</view>
						<view class="nickname">
							<text>{{userInfo.nickname}}</text>
						</view>
					</view>
					<view class="more">
						<text class="iconfont icon-more"></text>
					</view>
				</view>
				<view class="address" @click="$fun.jump('./setUserInfo')">
					<view class="title">
						<text>个人中心</text>
					</view>
					<view class="more">
						<text class="iconfont icon-more"></text>
					</view>
				</view>
			</view>
			<!-- 设置列表 -->
			<view class="setting-list">
				<view class="list" @click="$fun.jump('../addressOperation/addressList')">
					<view class="title">
						<text>地址管理</text>
					</view>

					<view class="more-content">
						<text class="content"></text>
						<text class="iconfont icon-more more"></text>
					</view>
				</view>
			<!-- 	<view class="list" @click="$fun.jump(`./setPwd?sms_status=${userConfig.sms_arr_pwd}`)">
					<view class="title">
						<text>修改登录密码</text>
					</view>
					<view class="more-content">
						<text class="content"></text>
						<text class="iconfont icon-more more"></text>
					</view>
				</view> -->
				<view class="list" @click="$fun.jump(`./setPayPwd?sms_status=${userConfig.sms_arr_pay}`)">
					<view class="title">
						<text>修改支付密码</text>
					</view>
					<view class="more-content">
						<text class="iconfont icon-more more"></text>
					</view>
				</view>
				<view class="list" v-if="userConfig.pay_type.length>0" @click="$fun.jump(`./payment`)">
					<view class="title">
						<text>收款方式</text>
					</view>
					<view class="more-content">
						<text class="content"></text>
						<text class="iconfont icon-more more"></text>
					</view>
				</view>
				<view class="list" @click="$fun.jump(`/pages/home/<USER>">
					<view class="title">
						<text>用户协议</text>
					</view>
					<view class="more-content">
						<text class="content"></text>
						<text class="iconfont icon-more more"></text>
					</view>
				</view>
				<view class="list" @click="$fun.jump(`/pages/home/<USER>">
					<view class="title">
						<text>隐私协议</text>
					</view>
					<view class="more-content">
						<text class="content"></text>
						<text class="iconfont icon-more more"></text>
					</view>
				</view>
				<view class="list" v-if="userInfo.level_id>1" @click="$fun.jump(`/pages/my/register/register?invitation=${userInfo.invitation}&type=2`)">
					<view class="title">
						<text>注册会员</text>
					</view>
					<view class="more-content">
						<text class="content"></text>
						<text class="iconfont icon-more more"></text>
					</view>
				</view>
				<!-- <view class="list" @click="onQuitLogin1()">
					<view class="title">
						<text>注销账号</text>
					</view>
					<view class="more-content">
						<text class="content"></text>
						<text class="iconfont icon-more more"></text>
					</view>
				</view> -->
				<!-- <view class="list" v-if="userInfo.fid==0" @click="modalControl=!modalControl">
					<view class="title">
						<text>绑定服务中心</text>
					</view>
					<view class="more-content">
						<text class="content"></text>
						<text class="iconfont icon-more more"></text>
					</view>
				</view> -->
			</view>
			<!-- 设置列表 -->
			<view class="setting-list">
				<!-- #ifndef H5 -->
				<!-- <view class="list" @click="onSetting('common')">
					<view class="title">
						<text>通用</text>
					</view>
					<view class="more-content">
						<text class="content">清除本地缓存等</text>
						<text class="iconfont icon-more more"></text>
					</view>
				</view> -->
				<!-- #endif -->
				<!-- <view class="list" @click="onSetting('about')">
				<view class="title">
					<text>关于我们</text>
				</view>
				<view class="more-content">
					<text class="content"></text>
					<text class="iconfont icon-more more"></text>
				</view>
			</view> -->
			</view>
		</mescroll-body>
		<!-- 退出 -->
		<view class="quit-login"  @click="onQuitLogin()">
			<text>退出登录</text>
		</view>
		<!-- 提示框 -->
		<DialogBox ref="DialogBox"></DialogBox>
		<z-modal :show="modalControl" :btnGroup="btnGroup" :contentType="2" :titleText="'绑定服务中心'" @cancle="cancle"
			@sure="sure"></z-modal>
	</view>
</template>

<script>
	import MescrollMixin from "@/components/mescroll-uni/mescroll-mixins.js";
	import zModal from '@/components/z-modal/z-modal.vue'
	export default {
		mixins: [MescrollMixin], // 使用mixin
		components: {zModal},
		data() {
			return {
				userInfo: {},
				userConfig: {
					pay_type: []
				},
				mescroll: null, // mescroll实例对象 (此行可删,mixins已默认)
				// 下拉刷新的配置(可选, 绝大部分情况无需配置)
				downOption: {},
				// 上拉加载的配置(可选, 绝大部分情况无需配置)
				upOption: {
					use: false
				},
				btnGroup: [
						{
						text: '取消',
						color: '#FFFFFF',
						bgColor: '#999999',
						width: '150rpx',
						height: '80rpx',
						shape: 'fillet',
						eventName: 'cancle'
					}, 
					{
						text: '确定',
						color: '#FFFFFF',
						bgColor: '#007AFF',
						width: '150rpx',
						height: '80rpx',
						shape: 'fillet',
						eventName: 'sure'
					}
				],
				modalControl:false
			};
		},
		methods: {
			/**
			 * 初始化
			 */
			async init() {
				await this.getUserInfo()
				this.mescroll.endSuccess();
			},
			/*下拉刷新的回调, 有三种处理方式:*/
			downCallback() {
				this.init()
			},
			/*上拉加载的回调*/
			upCallback(page) {
				this.init()
			},
			/**
			 * 获取用户详情和配置
			 */
			getUserInfo() {
				this.$fun.ajax.post('user/index', {}).then(res => {
					if (res.status == 1) {
						this.userInfo = res.data;
					}
				})
				this.$fun.ajax.post('config/index', {}).then(res => {
					if (res.status == 1) {
						this.userConfig = res.data
					}
				})
			},
			sure(e) {
				if (e.inputText == '') {
					this.$fun.msg('请输入服务中心邀请码')
					return;
				}
				this.$fun.ajax.post('user/setFid', {
					invitation: e.inputText
				}).then(res => {
					if (res.status == 1) {
						this.$fun.msg(res.msg);
						this.modalControl = false
						setTimeout(() => {
							this.init()
						}, 1200)
					}
				})
			},
			cancle(e) {
				this.modalControl = false
			},
			/**
			 * 退出点击
			 */
			onQuitLogin() {
				this.$refs['DialogBox'].confirm({
					title: '提示',
					content: '是否要退出登录?',
					DialogType: 'inquiry',
					animation: 0
				}).then(() => {
					this.$fun.ajax.post('user/logout', {}).then(res => {
						if (res.status == 1) {
							uni.clearStorageSync();
							this.$fun.msg('退出登录成功');
							this.$fun.jump('/pages/my/login/login', 4, 1000)
						}
					})
				})
			},
			/**
			 * 退出点击
			 */
			onQuitLogin1() {
				this.$refs['DialogBox'].confirm({
					title: '提示',
					content: '是否要注销该账号吗?',
					DialogType: 'inquiry',
					animation: 0
				}).then(() => {
					this.$fun.ajax.post('user/logout', {}).then(res => {
						if (res.status == 1) {
							uni.clearStorageSync();
							this.$fun.msg('注销成功');
							this.$fun.jump('/pages/my/login/login', 4, 1000)
						}
					})
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	.page {
		position: absolute;
		left: 0;
		bottom: 0;
		width: 100%;
		height: 100%;
		background-color: #f6f6f6;
	}

	/* 用户信息 */
	.user-info {
		width: 100%;
		height: 300rpx;
		background-color: #FFFFFF;
		border-radius: 0 0 20rpx 20rpx;

		.user-data {
			display: flex;
			align-items: center;
			padding: 0 4%;
			height: 200rpx;

			.portrait-nickname {
				display: flex;
				align-items: center;
				width: 100%;
				height: 90%;

				.portrait {
					width: 140rpx;
					height: 140rpx;

					image {
						width: 100%;
						height: 100%;
						border-radius: 100%;
					}
				}

				.nickname {
					display: flex;
					align-items: center;
					margin-left: 20rpx;
					height: 140rpx;

					text {
						font-size: 28rpx;
						color: #222222;
					}
				}
			}

			.more {
				display: flex;
				align-items: center;

				text {
					color: #959595;
					font-size: 28rpx;
				}
			}
		}

		.address {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 4%;
			height: 100rpx;
			border-top: 2rpx solid #f6f6f6;

			.title {
				display: flex;
				align-items: center;

				text {
					font-size: 28rpx;
					color: #555555;
				}
			}

			.more {
				display: flex;
				align-items: center;

				text {
					color: #959595;
					font-size: 28rpx;
				}
			}
		}
	}

	/* 设置列表 */
	.setting-list {
		padding: 0 4%;
		background-color: #FFFFFF;
		border-radius: 20rpx;
		margin: 20rpx auto;

		.list {
			display: flex;
			align-items: center;
			justify-content: space-between;
			width: 100%;
			height: 100rpx;
			border-bottom: 2rpx solid #f6f6f6;

			.title {
				display: flex;
				align-items: center;

				text {
					font-size: 28rpx;
					color: #222222;
				}
			}

			.more-content {
				display: flex;
				align-items: center;

				.content {
					font-size: 28rpx;
					color: #959595;
				}

				.more {
					font-size: 24rpx;
					color: #959595;
					margin-left: 20rpx;
				}
			}
		}
	}

	/* 退出登录 */
	.quit-login {
		position: fixed;
		left: 0;
		bottom: 0;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 100rpx;
		background: #FFFFFF;
		border-radius: 20rpx;

		text {
			color: #222222;
			font-size: 28rpx;
		}
	}
</style>