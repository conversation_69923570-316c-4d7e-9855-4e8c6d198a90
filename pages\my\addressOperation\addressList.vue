<template>
	<view class="page">
		<mescroll-body ref="mescrollRef" @init="mescrollInit" @down="downCallback" @up="upCallback" :down="downOption"
			:up="upOption" :top="0">
			<view class="address-list">
				<view class="list" v-for="(item,index) in cartList" :key="index" @click="pitchItem(item)">
					<view class="name-phone">
						<view class="name">
							<text class="one-omit">{{item.name}}</text>
						</view>
						<view class="phone">
							<text>{{item.mobile}}</text>
							<text class="tag" v-if="item.status==1">默认</text>
						</view>
					</view>
					<view class="address-edit">
						<view class="address">
							<text>{{item.pro}}{{item.city}}{{item.area}}{{item.address}}</text>
						</view>
						<view class="edit" @click.stop="$fun.jump(`./addressEdit?id=${item.id}&type=1
						`)">
							<text class="iconfont icon-edit1"></text>
						</view>
					</view>
				</view>
			</view>
			
		</mescroll-body>
		<!-- 添加地址 -->
		<view class="add-address">
			<view class="btn" @click="$fun.jump(`./addressEdit?isEdit=false
						`)">
				<text>新建收货地址</text>
			</view>
		</view>
	</view>
</template>

<script>
	// 引入mescroll-mixins.js
	import MescrollMixin from "@/components/mescroll-uni/mescroll-mixins.js";
	export default {
		mixins: [MescrollMixin], // 使用mixin
		data() {
			return {
				mescroll: null, // mescroll实例对象 (此行可删,mixins已默认)
				// 下拉刷新的配置(可选, 绝大部分情况无需配置)
				downOption: {},
				// 上拉加载的配置(可选, 绝大部分情况无需配置)
				upOption: {},
				cartList: [],
			}
		},
		onLoad(option) {
			if(option.type){
				this.type = option.type
			}
		},
		onShow() {
			this.cartList = []
			this.mescroll.resetUpScroll(false);
		},
		methods: {
			pitchItem(item){
				if(this.type==1){
					uni.setStorageSync('address',JSON.stringify(item))
					uni.navigateBack({
						
					})
				}
			},
			/*下拉刷新的回调, 有三种处理方式:*/
			downCallback() {
				this.cartList = []
				this.mescroll.resetUpScroll(false);
			},
			/*上拉加载的回调*/
			upCallback(e) {
				this.mescroll.removeEmpty();
				const data = {
					page: e.num,
					pagenum: e.size,
				};
				this.$fun.ajax.post('address/list', data).then(res => {
					if (res.status == 1) {
						const curList = res.data;
						if (e.num === 1) {
							this.cartList = [];
							//第一页清空数据重载
							if (curList.length > 0) {
								uni.pageScrollTo({
									scrollTop: 0,
									duration: 200
								});
							}
						}
						this.cartList = this.cartList.concat(curList); //追加新数据
						console.log(this.cartList)
						this.mescroll.endSuccess(curList.length); //结束加载状态
					}
				})
			},
		}
	}
</script>

<style scoped lang="scss">
	.page{
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		background-color: #FFFFFF;
	}
	
	/* 地址列表 */
	.address-list{
		width: 100%;
		background-color: #FFFFFF;
		padding-bottom: 120rpx;
		.list{
			padding: 0 4%;
			height: 160rpx;
			border-bottom: 2rpx solid #f6f6f6;
			.name-phone{
				display: flex;
				align-items: center;
				width: 100%;
				height: 80rpx;
				.name{
					display: flex;
					align-items: center;
					width: 30%;
					height: 100%;
					text{
						width: 100%;
						font-size: 26rpx;
						font-weight: bold;
						color: #222222;
					}
				}
				.phone{
					display: flex;
					align-items: center;
					width: 70%;
					height: 100%;
					text{
						font-size: 28rpx;
						font-weight: bold;
						color: #222222;
					}
					.tag{
						padding: 4rpx 8rpx;
						font-size: 24rpx;
						color: #FFFFFF;
						background-color: $base;
						border-radius: 4rpx;
						margin-left: 20rpx;
					}
					.blue{
						background-color: #0099FF;
					}
				}
			}
			.address-edit{
				display: flex;
				align-items: center;
				justify-content: space-between;
				width: 100%;
				height: 60rpx;
				.address{
					display: flex;
					align-items: center;
					width: 90%;
					height: 100%;
					text{
						font-size: 26rpx;
						color: #959595;
					}
				}
				.edit{
					display: flex;
					align-items: center;
					justify-content: flex-end;
					width: 10%;
					height: 100%;
					text{
						font-size: 38rpx;
						color: #555555;
					}
				}
			}
		}
	}
	
	/* 添加地址 */
	.add-address{
		position: fixed;
		left: 0;
		bottom: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 100rpx;
		.btn{
			display: flex;
			align-items: center;
			justify-content: center;
			width: 80%;
			height: 70rpx;
			background: linear-gradient(to right,$base,$change-clor);
			border-radius: 70rpx;
			box-shadow: 0 10rpx 10rpx $base;
			text{
				font-size: 28rpx;
				color: #FFFFFF;
			}
		}
	}
</style>
