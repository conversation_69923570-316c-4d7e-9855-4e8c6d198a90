<template>
	<view class="page">
		<!-- 文章数据 -->
		<mescroll-body ref="mescrollRef" @init="mescrollInit" @down="downCallback" @up="upCallback" :down="downOption"
			:up="upOption" :top="0">
			<view class="bus-top">
				<view class="b" v-for="(item, index) in mList" :key='index'>
					<image :src="(index+1)%2==0?'/static/11.png':'/static/22.png'" mode="heightFix"></image>
					<view class="c">
						<view class="v" :style="{color:(index+1)%2==0?'red':'#0054B7'}">
							<text>{{ item.value }}</text>
							<text>家</text>
						</view>
						<view class="n">{{ item.name }}</view>
					</view>
				</view>
			</view>
			<view class="menu-nav" v-if="categoryList.length>0">
				<scroll-view scroll-x @scroll="ScrollMenu" class="nav-list">
					<view :style="{display: 'flex',width:`calc(100vw*${ListLength})`}">
						<view class="nav" ref="nav" :style="'flex-direction:column'"
							v-for="(item,index) in categoryList" :key="index">

							<view class="list" style="position: relative;" v-for="(item2,index2) in item"
								@click="$fun.jump(`/pages/discover/discoverList?id=${item2.id}&name=${item2.name}`)"
								:key="index2">
								<image :src="$fun.imgUrl(item2.image)" mode=""></image>
								<text>{{item2.name}}</text>
								<!-- #ifdef MP-WEIXIN -->
								<button class="leftBox" v-if="item2.keywords=='kf'" type="default" plain="true"
									open-type="contact">

								</button>
								<!-- #endif -->
							</view>
						</view>
					</view>
				</scroll-view>
				<view class="indicator" v-if="categoryList.length>1">
					<view class="plan">
						<view class="bar" :style="'left:'+slideNum+'%'"></view>
					</view>
				</view>
			</view>
			<view class="goods-list1">
				<view :class="'list-li'" v-for="(item,index) in goodsList"
					@click="$fun.jump(`/pages/discover/discoverDetails?id=${item.id}`)" :key="index">
					<view class="thumb">
						<image :src="$fun.imgUrl(item.logoimage)"></image>
					</view>
					<view class="item">
						<view class="title">
							<text class="two-omit2">{{item.name}}</text>
						</view>
						<view class="title">
							<text class="two-omit1">联系方式:</text>
							<text class="two-omit2">{{item.mobile}}</text>
						</view>
						<view class="title">
							<text class="two-omit1">地区:</text>
							<text class="two-omit2">{{item.address}}</text>
						</view>
					</view>
				</view>
			</view>
		</mescroll-body>
		<!-- tabbar -->
		<TabBar :tabBarShow="2"></TabBar>
	</view>
</template>

<script>
	import TabBar from '@/components/TabBar.vue';
	// 引入mescroll-mixins.js
	import MescrollMixin from "@/components/mescroll-uni/mescroll-mixins.js";
	export default {
		mixins: [MescrollMixin], // 使用mixin
		components: {
			TabBar,
		},
		data() {
			return {
				mescroll: null, // mescroll实例对象 (此行可删,mixins已默认)
				// 下拉刷新的配置(可选, 绝大部分情况无需配置)
				downOption: {},
				// 上拉加载的配置(可选, 绝大部分情况无需配置)
				upOption: {},
				swiperList: [],
				categoryList: [],
				goodsList: [],
				mList: [],
				slideNum: 0,
				ListLength: 1
			};
		},
		onReady() {
			uni.hideTabBar();
		},
		methods: {
			/**
			 * 初始化
			 */
			async init() {
				await this.getSwiper()
				await this.getCategory()
			},
			/**
			 * 菜单导航滚动
			 */
			ScrollMenu(e) {
				let scrollLeft = e.target.scrollLeft;
				const query = uni.createSelectorQuery().in(this);
				query.select('.nav').boundingClientRect(data => {
					let wid = e.target.scrollWidth - data.width - (data.left * 2 + 5);
					this.slideNum = (scrollLeft / wid * 300) / 2;
				}).exec();
			},
			/**
			 * 获取轮播图
			 */
			getSwiper() {
				this.$fun.ajax.post('nearby/getnum', {
					type: 'nearby'
				}).then(res => {
					console.log(res)
					if (res.status == 1) {
						this.mList = res.data
					}
				})
			},
			/**
			 * 获取分类
			 */
			getCategory() {
				this.$fun.ajax.post('category/list', {
					type: 'nearby'
				}).then(res => {
					console.log(res)
					if (res.status == 1) {
						if (res.status == 1) {
							this.categoryList = []
							let data = res.data
							let l = res.data.length / 10 < 1 ? 1 : Math.ceil(res.data.length / 10)
							this.ListLength = l
							let index = 0
							for (var i = 0; i < l; i++) {
								let o = res.data.slice(i * 10, i * 10 + 10)
								this.categoryList.push(o)
							}
						}
					}
				})
			},
			/*下拉刷新的回调, 有三种处理方式:*/
			downCallback() {
				this.mescroll.endSuccess();
			},
			/*上拉加载的回调*/
			async upCallback(e) {
				this.mescroll.removeEmpty();
				const data = {
					page: e.num,
					pagenum: e.size,
				};
				await this.getSwiper()
				await this.getCategory()
				this.$fun.ajax.post('nearby/index', data).then(res => {
					if (res.status == 1) {
						const curList = res.data.data;
						if (e.num === 1) {
							this.goodsList = [];
							//第一页清空数据重载
							if (curList.length > 0) {
								uni.pageScrollTo({
									scrollTop: 0,
									duration: 200
								});
							}
						}
						this.goodsList = this.goodsList.concat(curList); //追加新数据
						this.mescroll.endSuccess(curList.length); //结束加载状态
					}
				})
			},
			/**
			 * 文章点击
			 */
			onArticle() {
				uni.navigateTo({
					url: '/pages/ArticleDetails/ArticleDetails',
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	@import 'discover.scss';

	.bus-top {
		// background: #FFFFFF;
		width: 100%;
		display: flex;
		flex-wrap: wrap;
		box-sizing: border-box;

		.b {
			width: 50%;
			display: flex;
			justify-content: center;
			flex-direction: column;
			// align-items: center;
			flex-wrap: wrap;
			color: #666666;
			font-size: 30rpx;
			margin: 30rpx 0;
			box-sizing: border-box;
			display: flex;
			position: relative;
			justify-content: center;
			height: 186rpx;

			image {
				position: absolute;
				left: calc( (50% - 99rpx) / 4);
				top: 0;
				height: 186rpx;
			}

			.c {
				margin-left: 50rpx;
				margin-top: 50rpx;
				width: max-content;
				display: flex;
				justify-content: center;
				flex-direction: column;
				align-items: center;

				.v {
					width: max-content;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					font-size: 36rpx;
					color: #008467;
					margin-bottom: 10rpx;

					text:nth-child(2) {
						font-size: 20rpx;
					}
				}

				.n {
					width: max-content;
					color: #666666;
					font-size: 28rpx;
				}
			}

		}

		// .b {
		// 	border-right: 1px solid #E2E2E2;
		// }

		// .b:nth-child(3) {
		// 	border-right: none;
		// }
	}
</style>