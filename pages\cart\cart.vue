<template>
	<view class="page">
		<view class="car_top">
			<view class="title_box">
				<view class="title">
					购物车（{{cartList.length}}）
				</view>
				<view class="edit" @click="isEdit = !isEdit">
					{{isEdit?'完成':'管理'}}
				</view>
			</view>
		</view>
		<view style="height: 100rpx;">

		</view>
		<mescroll-body ref="mescrollRef" @init="mescrollInit" @down="downCallback" @up="upCallback" :down="downOption"
			:up="upOption" :top="20" :height="heightBody">
			<view class="cart-box" v-if="cartList.length>0">
				<view class="cart-list" style=" 
		padding: 20rpx 0; margin: 0 32rpx 0 32rpx;" v-if="cartList.length>0">

					<view class="list" v-for="(item,index) in cartList" :key="index">
						<view class="check" @click="isSelection(index,item.goods_id,cartList)">
							<text :class="item.icon?'iconfont icon-checked':'iconfont icon-check'"></text>
						</view>
						<view class="goods">
							<view class="thumb">
								<image :src="$fun.imgUrl(item.image)" mode=""></image>
							</view>
							<view class="item">
								<view class="title">
									<text class="two-omit">{{item.name}}</text>
								</view>
								<view class="attribute" v-if="item.sign">
									<view class="attr">
										<text>{{item.sign}}</text>
										<!-- <text class="more"></text> -->
									</view>
								</view>
								<view class="price-num">
									<view class="price">
										<text class="min">￥</text>
										<text class="max">{{item.money}} <text style="margin-left:10rpx;"
												v-if="item.dikou!=0">+ {{item.dikou}}</text></text>
									</view>
									<view class="num">
										<uni-number-box :index="index" :step="item.xgnum>0?item.xgnum:1"
											:value="item.num" :idcode='item.id' :min="1"
											v-on:change="numberChange($event,index)"
											:max="item.maxnum>0?item.maxnum : 10000">
										</uni-number-box>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>


			</view>
		</mescroll-body>
		<!-- 结算 -->
		<view class="close-account" v-if="cartList.length>0">
			<view class="check-total">
				<view class="check" @click="allElection">
					<text :class="allEle?'iconfont icon-checked':'iconfont icon-check'"></text>
					<text class="all">全选</text>
				</view>

			</view>
			<view class="account">
				<view class="total">
					<text class="text">合计：</text>
					<text class="price" style="display: flex;">￥{{totalMoney*1}} <text class="price"
							style="margin-left: 10rpx;" v-if="totaldk!=0">+ {{totaldk*1}}积分</text> </text>
				</view>
				<view class="btn-calculate" @click="goBuy()" v-if="!isEdit">
					<text>去结算({{arrLength}})</text>
				</view>
				<view class="btn-del" @click="delCart()" v-else>
					<!-- <text class="attention">移入关注</text> -->
					<text class="del">删除</text>
				</view>
			</view>
		</view>
		<!-- tabbar -->
		<!-- <TabBar :tabBarShow="2"></TabBar> -->
	</view>
</template>

<script>
	// 引入mescroll-mixins.js
	import MescrollMixin from "@/components/mescroll-uni/mescroll-mixins.js";
	import uniNumberBox from '@/components/uni-number-box.vue'
	export default {
		mixins: [MescrollMixin], // 使用mixin
		components: {
			uniNumberBox
		},
		data() {
			return {
				mescroll: null, // mescroll实例对象 (此行可删,mixins已默认)
				// 下拉刷新的配置(可选, 绝大部分情况无需配置)
				downOption: {},
				// 上拉加载的配置(可选, 绝大部分情况无需配置)
				upOption: {},
				cartList: [],
				isEdit: false,
				allEle: false,
				totalMoney: '0.00',
				totaldk: 0,
				arrLength: 0,
				heightBody: 0
			}
		},
		onLoad() {
			uni.getSystemInfo({
				success: (res) => {
					this.heightBody = (res.windowHeight - 240) * 2;
				}
			});
			// uni.getSystemInfoSync().screenHeight -144;
		},
		onShow() {
			this.upCallback({
				num: 1,
				size: 25
			});
			// console.log(12313212)
		},
		methods: {
			/*下拉刷新的回调, 有三种处理方式:*/
			downCallback() {
				this.cartList = []
				this.mescroll.resetUpScroll(false);
			},
			/*上拉加载的回调*/
			upCallback(e) {
				// this.mescroll.removeEmpty();
				const data = {
					page: e.num,
					pagenum: e.size,
				};
				this.$fun.ajax.post('goods/getcart', data).then(res => {
					if (res.status == 1) {
						const curList = res.data.data;
						for (var i = 0; i < curList.length; i++) {
							curList[i].icon = false
						}
						if (e.num === 1) {
							this.cartList = [];
							//第一页清空数据重载
							if (curList.length > 0) {
								uni.pageScrollTo({
									scrollTop: 0,
									duration: 200
								});
							}
						}
						this.cartList = this.cartList.concat(curList); //追加新数据
						this.mescroll.endSuccess(curList.length); //结束加载状态
					}
				})
			},
			goBuy() {
				let shopIdList = []
				for (var i = 0; i < this.cartList.length; i++) {
					if (this.cartList[i].icon) {
						shopIdList.push(this.cartList[i].id)
					}
				}
				if (shopIdList.length == 0) {
					this.$fun.msg('请先选择商品')
					return false
				}
				// 确认订单结算
				this.$fun.ajax.post('order/getgoods', {
					cid: shopIdList.join(',')
				}).then(res => {
					if (res.status == 1) {
						this.$fun.jump(`/pages/home/<USER>',')}&type=2`)
					}
				})
			},
			delCart() {
				let isdelectList = []
				for (let k in this.cartList) {

					if (this.cartList[k].icon) {
						isdelectList.push(this.cartList[k].id)
					}
				}
				if (isdelectList.length == 0) {
					this.$fun.mag('请先选择商品')
					return false
				}
				this.$fun.ajax.post('goods/delcart', {
					cid: isdelectList.join(',')
				}).then(res => {
					console.log(res.status)
					if (res.status == 1) {
						this.$fun.msg(res.msg)
						this.cartList = []
						this.mescroll.resetUpScroll(); // 刷新列表数据
					}
				})
			},
			numberChange(num, index) {
				let data = {
					type: 'update',
					cid: this.cartList[index].id,
					num: num
				}
				this.$fun.ajax.post('goods/cart', data).then(res => {
					this.$fun.msg(res.msg)
					if (res.status == 1) {
						this.cartList[index].num = num
						this.countTotoal();
					} else {
						this.cartList[index].num = num - 1
					}
				})
			},
			// 复选框
			isSelection(index, shopId) {
				this.cartList[index].icon = !this.cartList[index].icon
				let isAll = true;
				for (var i = 0; i < this.cartList.length; i++) {
					if (!this.cartList[i].icon) {
						isAll = false
					}
				}
				this.allEle = isAll
				this.countTotoal()

			},
			allElection() {
				// 全选
				this.allEle = !this.allEle
				for (var i = 0; i < this.cartList.length; i++) {
					this.cartList[i].icon = this.allEle
				}
				this.countTotoal()
			},
			//计算总计函数
			countTotoal: function() {
				var total = 0;
				var totaldk = 0;
				var arrL = 0;
				for (let k in this.cartList) {
					if (this.cartList[k].icon) {
						arrL += 1
						total += (this.cartList[k].money) * Number(this.cartList[k].num);
						totaldk += (this.cartList[k].dikou) * Number(this.cartList[k].num);
					}
				}
				this.arrLength = arrL
				this.totalMoney = total.toFixed(2);
				this.totaldk = totaldk.toFixed(2);
			},
			/**
			 * 返回点击
			 */
			onBack() {
				uni.navigateBack();
			},
		}
	}
</script>

<style scoped lang="scss">
	@import 'cart.scss';
</style>