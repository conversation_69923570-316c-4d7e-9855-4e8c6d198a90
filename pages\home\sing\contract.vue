<template>
	<view>
		<view class="contract">
			<view v-html="contractInfo" class="contract_html">

			</view>
			<view class="sing_box_t">
				<view class="l">
					<view class="l_title">
						甲方:
					</view>
					<view class="l_image" style="width:200rpx;">
						<view class="footer-btn" v-if="!imgBase64">
							<view class="btn" @click="saveAddress()">
								<text>{{'去签名'}}</text>
							</view>
						</view>
						<image v-else class="user_sing" @click="$fun.jump(`./sing`)" :src="imgBase64" mode="heightFix"
							style="transform: rotate(270deg);"></image>
					</view>
				</view>
				<view class="r">
					<view class="l_title">
						乙方:
					</view>
					<view class="l_image">
						<image  class="gzimg"src="/static/gsz.png" mode="widthFix"></image>
					</view>
				</view>
			</view>
			<view class="sing_box_t">
				<view class="l">
					<view class="l_title">
						日期:
					</view>
					<view class="l_image">
						{{NewDate}}
					</view>
				</view>
				<view class="r">
					<view class="l_title">
						日期:
					</view>
					<view class="l_image">
						{{NewDate}}
					</view>
				</view>
			</view>
			<!-- <view class="sing_box">
				<view class="name">
					签名
				</view>

				<view class="img_box" v-if="!imgBase64">
					<view class="footer-btn">
						<view class="btn" @click="saveAddress()">
							<text>{{'去签名'}}</text>
						</view>
					</view>
				</view>
				<view class="img_box" v-else>
					<image @click="$fun.jump(`./sing`)" :src="imgBase64" mode="heightFix"
						style="height: (100vw / 2);transform: rotate(90deg);"></image>
				</view>
			</view> -->
			<view class="footer-btn" v-if="imgBase64">
				<view class="btn" @click="saveAddress()">
					<text>{{'提交'}}</text>
				</view>
			</view>
			<view style="height: 30rpx;">

			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				contractInfo: '',
				imgBase64: ''
			};
		},
		onLoad(option) {
			this.init(option);
			this.getDate();
		},
		onShow() {
			if (uni.getStorageSync('singImg')) {
				this.imgBase64 = uni.getStorageSync('singImg');
			}
		},
		methods: {
			getDate(){
				let Y = new Date().getFullYear();
				let M = new Date().getMonth()+1;
				let D = new Date().getDate();
				this.NewDate= `${Y}-${M}-${D}`
			},
			saveAddress() {
				if (this.imgBase64) {

					this.$fun.msg('操作成功');
					setTimeout(() => {
						uni.navigateBack()
					}, 500)

				} else {
					this.$fun.jump(`./sing`);
				}
			},
			init(option) {
				let data = {}
				if (uni.getStorageSync('shopPay')) {
					data = JSON.parse(uni.getStorageSync('shopPay'))
				}
				if (uni.getStorageSync('address')) {
					data.aid = JSON.parse(uni.getStorageSync('address')).id
				}
				this.$fun.ajax.post('order/getgoods', {
						...data
					})
					.then(res => {
						if (res.status == 1) {
							this.contractInfo = res.data.jscontent;
						}
					});
			}
		}
	}
</script>

<style lang="scss">
	page,
	body {
		background: #FFFFFF !important;

		.contract {
			.contract_html {
				padding: 30rpx;
			}

			.sing_box {

				display: flex;
				justify-content: flex-end;

				.img_box {
					width: 224px;
				}
			}

			.footer-btn {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 100%;
				height: 100rpx;

				.btn {
					display: flex;
					align-items: center;
					justify-content: center;
					width: 80%;
					height: 70rpx;
					background: linear-gradient(to right, $base, $change-clor);
					border-radius: 70rpx;
					box-shadow: 0 10rpx 10rpx $base;

					text {
						font-size: 28rpx;
						color: #FFFFFF;
					}
				}
			}
		}
	}
	.sing_box_t{
		margin-top:30rpx;
		padding: 30rpx;
		width: 100%;
		display: flex;
		justify-content: space-between;
		.l,.r{
			position: relative;
			width: 50%;
			display: flex;
			.l_title{
				margin-right: 20rpx;
			}
			.l_image{
				image.gzimg{
					transform: rotate(70deg);
					position: absolute;
					top: -100rpx;
					left: 30rpx;
					width: 200rpx;
				}
				.user_sing{
					position: absolute;
					top:-100rpx;
					left: 150rpx;
					height: 250rpx;
				}
			}
		}
	}
</style>