<template>
	<view @click="isMore = false">
		<view class="goods-head" :style="'background:rgba(255,255,255,' + PageScrollTop / 100 + ')'">
			<!-- 返回 -->
			<view class="back" @click="onBack">
				<view class="back-one" :class="{ action: PageScrollTop > 120 }">
					<text></text>
				</view>
			</view>
			<!-- tab切换 -->
			<view class="head-tab" v-if="PageScrollTop > 120">
				<view class="tab" :class="{ 'action': TabShow === 0 }" @click="onTab(0)">
					<text>商品</text>
					<text class="line"></text>
				</view>
				<view class="tab" :class="{ 'action': TabShow === 2 }" @click="onTab(2)">
					<text>详情</text>
					<text class="line"></text>
				</view>
			</view>
		</view>
		<!-- banner，标题 -->
		<view class="banner-title">
			<!-- banner -->
			<view class="banner">
				<swiper class="screen-swiper round-dot" indicator-dots="true" circular="true" autoplay="true"
					interval="5000" duration="500">
					<swiper-item v-for="(item, index) in swiperList" :key="index">
						<image :src="$fun.imgUrl(item)" mode="aspectFill"></image>
					</swiper-item>
				</swiper>
			</view>
			
			<view class="flash-price1" v-if="info.pin_status * 1">
				<view class="price-item">
					<view class="icon-item">
						<text class="iconfont icon-flash-sale"></text>
					</view>
					<view class="price">
						<view class="current-price">
							<text class="min">￥</text>
							<text class="max">{{ info.pin.money }}</text>
						</view>
						<view class="original-price">
							<text>￥{{ info.money }}</text>
						</view>
					</view>
					<view class="tag">
						<text class="iconfont icon-flash-naozhong"></text>
					</view>
				</view>
				<view class="time-item">
					<view class="title">
						<text>距结束还剩：</text>
					</view>
					<view class="time">
						<cz-countdown :startTime="info.pin.addtime * 1000 + ''" :endTime="info.pin.endtime * 1000 + ''"
							@loadingDate="loadingDate"></cz-countdown>
					</view>
				</view>
			</view>
			<!-- 价格 -->
			<view class="price-info" v-show="type == 0">
				<view style="display: flex;">
					<view class="price">
						<text class="min">￥</text>
						<text class="max">{{ info.money }}</text>
						<text class="max" v-if="info.dikou * 1 > 0" style="margin-left: 10rpx;"> + {{ info.dikou
							}}</text>
					</view>
					<view class="price"
						style="margin-left: 20rpx;color: #F5F5F5;text-decoration: line-through;color: gray;">
						<text class="min" style="color: gray;font-weight: normal;">￥</text>
						<text class="max" style="color: gray;font-weight: normal;font-size: 32rpx">{{ info.price
							}}</text>
					</view>
				</view>
			</view>

			<!-- 限时抢购 -->
			<view class="flash-price" v-if="info.business">
				<view class="price-item">
					<view class="icon-item">
						<image :src="$fun.imgUrl(info.business.logo)" mode=""></image>
					</view>
					<view class="price">
						<view class="current-price">
							<text class="min">{{ info.business.buname }}</text>
						</view>
						<view class="original-price" style="margin-top: 10rpx;">
							<text>共有{{ info.business.num }}商品</text>
						</view>
					</view>
					<view class="tag">
						<text class="iconfont icon-flash-naozhong"></text>
					</view>
				</view>
				<view class="time-item">
					<view class="title"
						@click="$fun.jump(`/pages/classify/classify_store?id=${info.business.id}`)">
						<text>进入店铺</text>
					</view>
				</view>
			</view>
			<!-- 标题 -->
			<view class="goods-title">
				<text>{{ info.name }}</text>
			</view>
			<view class="goods-title" style="display: flex;justify-content: space-between;color: gray;">
				<text>销量:{{ info.num }}</text>
				<text>库存:{{ info.stock }}</text>
			</view>

		</view>

		<!-- 商品介绍 -->
		<view class="products-introduction" ref="products">
			<view class="title">
				<text>商品介绍</text>
			</view>
			<view class="content" v-html="$fun.formatRichText(web_content)">
			</view>
		</view>
		<!-- 底部 -->
		<view class="page-footer">
			<view class="footer-fn">
				<view class="list" @click="navto(`/pages/home/<USER>">
					<image :src="$fun.imgUrl('/static/tabBar/tab_01.png')" mode=""></image>
					<text>首页</text>
				</view>
				<view class="list" @click="navto(`/pages/cart/cart`,0)">
					<image :src="$fun.imgUrl('/static/icon/my_icon/5.png')" mode=""></image>
					<text>购物车</text>
				</view>
			</view>
			<view class="footer-buy">
				<view class="buy-at" v-if="!info.pin_status * 1" @click="GoodsAttrAShow(2)">
					<text>加入购物车</text>
				</view>
				<view class="cart-add" @click="GoodsAttrAShow(3)">
					<text>立即购买</text>
				</view>
				<view class="cart-add" v-if="info.pin_status * 1" @click="GoodsAttrAShow(4)">
					<text>发起拼团</text>
				</view>
			</view>
		</view>
		<!-- 服务弹窗 -->
		<!-- <goods-serve ref="GoodsServe"></goods-serve> -->
		<!-- 优惠券 -->
		<!-- <goods-coupon ref="GoodsCoupon"></goods-coupon> -->
		<!-- 属性规格 -->
		<!-- 关注公众号 -->
	<!-- 	<u-modal v-model="modalControl1" :mask-close-able="true" width="100%" :show-title="false"
			:show-confirm-button="false">
			<view class="pinModel">
				<view class="pinModel-title">
					<text>参加{{info.tuanId.nickname}}的拼团</text>
				</view>
				<view class="pin_info">
					<view class="pin_info_item">
						<image :src="$fun.imgUrl(info.tuanId.avatar)" mode="" />
						<view class="tz">拼主</view>
					</view>
					<view class="pin_info_item">

						<view class="add">
							+
						</view>
					</view>
				</view>
				<view class="pin_time">
					<view class="flash-price1" v-if="info.pin_status * 1">
						<view class="price-item">
							<view class="icon-item">
								<text class="iconfont icon-flash-sale"></text>
							</view>
							<view class="price">
								<view class="current-price">
									<text class="min">￥</text>
									<text class="max">{{ info.pin.money }}</text>
								</view>
								<view class="original-price">
									<text>￥{{ info.money }}</text>
								</view>
							</view>
							<view class="tag">
								<text class="iconfont icon-flash-naozhong"></text>
							</view>
						</view>
						<view class="time-item">
							<view class="title">
								<text>距结束还剩：</text>
							</view>
							<view class="time">
								<cz-countdown :startTime="info.pin.addtime * 1000 + ''"
									:endTime="info.pin.endtime * 1000 + ''" @loadingDate="loadingDate"></cz-countdown>
							</view>
						</view>
					</view>
					<view class="pin_info_btn"   @click="GoodsAttrAShow(4)">
						参与拼团
					</view>
				</view>

			</view>
		</u-modal>
		 --><!-- 关注公众号 -->
		<u-modal v-model="modalControl" width="100%" :show-title="false" :show-confirm-button="false">
			<view class="showGz">
				<view class="imgbox">
					<image :src="$fun.imgUrl('/static/bingcode.png')" mode="widthFix"></image>
					<view class="inputbox">
						<input type="text" v-model="invitation" placeholder="请输入推荐码" />
					</view>
					<view class="btn">
						<view class="btn1" @click="cancle">
							取消
						</view>
						<view class="btn2 g" @click="sure">
							确认
						</view>
					</view>
				</view>
			</view>
		</u-modal>

		<goods-attr ref="GoodsAttr" :AttrSizeList="info" :arrS="info.json1"></goods-attr>

	</view>
</template>

<script>
import czCountdown from '@/components/cz-countdown/cz-countdown.vue';
import GoodsServe from '@/components/GoodsServe/GoodsServe.vue';
import GoodsCoupon from '@/components/GoodsCoupon/GoodsCoupon.vue';
import GoodsAttr from '@/components/GoodsAttr/GoodsAttr.vue';
import zModal from '@/components/z-modal/z-modal.vue'
export default {
	components: {
		GoodsServe,
		GoodsCoupon,
		GoodsAttr,
		zModal,
		czCountdown
	},

	data() {
		return {
			TabShow: 0,
			isMore: false,
			AttentionShow: 0,
			swiperList: [],
			info: {},
			info1: {},
			web_content: '',
			PageScrollTop: 0,
			type: 0,
			id: '',
			modalControl: false,
			modalControl1: false,
			invitation: '',


			btnGroup: [{
				text: '取消',
				color: '#FFFFFF',
				bgColor: '#999999',
				width: '150rpx',
				height: '80rpx',
				shape: 'fillet',
				eventName: 'cancle'
			},
			{
				text: '确定',
				color: '#FFFFFF',
				bgColor: '#007AFF',
				width: '150rpx',
				height: '80rpx',
				shape: 'fillet',
				eventName: 'sure'
			}
			],
			tuanId: 0
		};
	},

	onLoad(params) {
		this.type = params.type || 0;
		this.id = params.id;
		if (params.tuanId) {
			this.tuanId = params.tuanId
		}
	},
	mounted() {
		this.init()
	},
	onPageScroll(e) {
		this.PageScrollTop = e.scrollTop;
	},
	methods: {
		navto(url,type){
			this.$fun.jump(url,type)
		},
		loadingDate() {
			this.init()
		},
		sure(e) {
			if (this.invitation == '') {
				this.$fun.msg('请输入邀请码/手机号')

				return;
			}
			this.$fun.ajax.post('user/setPid', {
				invitation: this.invitation
			}).then(res => {
				if (res.status == 1) {
					this.$fun.msg(res.msg);
					this.modalControl = false
					setTimeout(() => {
						this.init()
					}, 1200)
				}
			})
		},
		cancle(e) {
			this.modalControl = false
		},
		GoodsAttrAShow(num) {
			
			console.log(this.info.pid)
			console.log(this.info.pid)
			console.log(this.info.pid)
			console.log(this.info.pid)
			console.log(this.info.pid)
			if (this.info.pid == 0) {
				this.modalControl = true
				return
			}
			if (num == 4) {
				this.modalControl1 = false
				this.info.money = this.info.pin.money
				this.info.money1 = this.info.pin.money
			} else {
				this.info.money = this.info1.money
				this.info.money1 = this.info1.money1
			}
			this.$refs['GoodsAttr'].show(num, this.tuanId)
			this.$refs['GoodsAttr'].getMoney()



		},
		/**

		 * 初始化
		 */
		init(option) {
			this.getGoodsDetails(this.id)
		},
		getGoodsDetails(id) {
			this.$fun.ajax.post('goods/content', {
				id,
				type: this.type,
				tuanId: this.tuanId
			}).then(res => {
				if (res.status == 1) {
					this.swiperList = res.data.images
					this.info = res.data
					this.info1 = res.data
					this.info.money1 = res.data.money;
					this.info1.money1 = res.data.money;
					this.web_content = res.data.content
					// 在调用ref之前先判断是否存在
			
					if (res.data.tuanId) {
						this.modalControl1 = true
					}

				}
			})
		},
		/**
		 * 返回
		 */
		onBack() {
			uni.navigateBack();
		},
		/**
		 * tab
		 */
		onTab(type) {
			this.TabShow = type;
			switch (type) {
				case 0:
					uni.pageScrollTo({
						scrollTop: 0,
						duration: 300
					});
					break;
				case 1:
					uni.createSelectorQuery().select(".evaluate-data").boundingClientRect((data) => { //data - 各种参数
						uni.pageScrollTo({
							scrollTop: this.PageScrollTop + data.top - 50,
							duration: 300
						});
					}).exec()
					break;
				case 2:
					uni.createSelectorQuery().select(".products-introduction").boundingClientRect((
						data) => { //data - 各种参数
						uni.pageScrollTo({
							scrollTop: this.PageScrollTop + data.top - 50,
							duration: 300
						});
					}).exec()
					break;
			}
		},
		/**
		 * 去购物车
		 */
		onToCart() {
			uni.switchTab({
				url: '/pages/cart/cart'
			})
		},
		/**
		 * 降价通知点击
		 */
		onDepreciate() {
			uni.showToast({
				title: '降价通知提醒成功',
				icon: 'success'
			})
		},
		/**
		 * 关注点击
		 */
		onAttention() {
			if (this.AttentionShow === 0) {
				this.AttentionShow = 1;
				uni.showToast({
					title: '关注成功',
					icon: 'none'
				})
			} else {
				this.AttentionShow = 0;
				uni.showToast({
					title: '取消成功',
					icon: 'none'
				})
			}

		},
		/**
		 * 评价点击
		 */
		onEvaluate() {
			uni.navigateTo({
				url: '/pages/GoodsEvaluateList/GoodsEvaluateList'
			})
		}
	}
};
</script>

<style scoped lang="scss">
.page {
	position: absolute;
	width: 100%;
	// height: 100%;
	background: #f6f6f6;
	overflow-x: hidden;
	// overflow-y: auto;
}

.goods-head {
	position: fixed;
	left: 0;
	top: 0;
	z-index: 1000;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;
	height: 100rpx;
	background: rgba(255, 255, 255, 0);
	/* #ifdef APP-PLUS */
	height: calc(100rpx + var(--status-bar-height));
	/* #endif */
	/* #ifdef MP */
	height: 200rpx;

	/* #endif */
	.back {
		position: absolute;
		left: 0;
		top: 0;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100rpx;
		height: 100%;
		/* #ifdef APP-PLUS */
		padding-top: 50rpx;
		/* #endif */
		/* #ifdef MP */
		padding-top: 100rpx;

		/* #endif */
		// 返回
		.back-one {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 50rpx;
			height: 50rpx;
			background-color: rgba(0, 0, 0, 0.3);
			border-radius: 100%;

			text {
				display: flex;
				width: 20rpx;
				height: 20rpx;
				border-left: 2rpx solid #FFFFFF;
				border-bottom: 2rpx solid #FFFFFF;
				transform: rotate(45deg);
			}
		}

		.action {
			background-color: transparent;

			text {
				border-color: #555555;
			}
		}
	}

	// tab切换
	.head-tab {
		display: flex;
		align-items: center;
		height: 100%;
		/* #ifdef APP-PLUS */
		padding-top: 50rpx;
		/* #endif */
		/* #ifdef MP */
		padding-top: 100rpx;

		/* #endif */
		.tab {
			position: relative;
			margin: 0 20rpx;
			padding: 0 10rpx;

			text {
				color: #555555;
				font-size: 26rpx;
			}
		}

		.action {
			text {
				color: #212121;
				font-size: 28rpx;
			}

			.line {
				position: absolute;
				left: 0;
				bottom: -10rpx;
				width: 100%;
				height: 6rpx;
				background: linear-gradient(to right, $base, rgba(255, 255, 255, 0.3));
			}
		}
	}

	// 分享更多
	.share-more {
		position: absolute;
		right: 0;
		top: 0;
		width: 140rpx;
		height: 100%;
		/* #ifdef APP-PLUS */
		padding-top: 50rpx;
		/* #endif */
		/* #ifdef MP */
		padding-top: 100rpx;

		/* #endif */
		.share-more-one {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding-right: 20rpx;
			height: 100%;

			.list {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 50rpx;
				height: 50rpx;
				background-color: rgba(0, 0, 0, 0.3);
				border-radius: 100%;

				text {
					font-size: 28rpx;
					color: #FFFFFF;
				}
			}
		}

		.action {
			.list {
				background-color: transparent;

				text {
					color: #555555;
				}
			}
		}

		.mroe-list {
			position: fixed;
			right: 20rpx;
			top: 100rpx;
			/* #ifdef MP */
			top: 180rpx;
			/* #endif */
			width: 200rpx;
			background-color: rgba(255, 255, 255, 0.9);
			border-radius: 10rpx;

			.list {
				display: flex;
				align-items: center;
				width: 90%;
				height: 80rpx;
				margin: 0 auto;
				border-bottom: 2rpx solid #C8C7CC;
				padding: 0 4%;

				.icon {
					display: flex;
					align-items: center;
					width: 60rpx;

					text {
						font-size: 34rpx;
					}
				}

				.title {
					display: flex;
					align-items: center;

					text {
						font-size: 26rpx;
					}
				}
			}
		}
	}
}

/* banner 标题 */
.banner-title {
	background-color: #FFFFFF;
	padding-bottom: 20rpx;
}

/* banner */
.banner {
	width: 100%;
	height: 750rpx;

	.screen-swiper {
		width: 100%;
		height: 100%;
	}
}

/* 价格 */
.price-info {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 4%;
	height: 120rpx;

	.price {
		display: flex;
		align-items: center;

		.min {
			color: #310FFF;
			font-size: 28rpx;
			font-weight: bold;
		}

		.max {
			color: #310FFF;
			font-size: 48rpx;
			font-weight: bold;
		}
	}

	.info {
		display: flex;
		align-items: center;
		height: 100%;

		.list {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			padding: 0 20rpx;

			text {
				font-size: 24rpx;
				color: #555555;
			}

			.iconfont {
				font-size: 34rpx;
				margin-bottom: 10rpx;
				color: #555555;
			}

			.action {
				color: $base;
			}
		}
	}
}

/* 限时抢购 */
.flash-price1 {
	display: flex;
	width: 100%;
	height: 100rpx;
	background-color: #FFFFFF;
	// border-radius: 20rpx;
	overflow: hidden;

	.price-item {
		position: relative;
		display: flex;
		width: 70%;
		height: 100%;
		background: #FB3C0A;
		padding: 0 20rpx;
		overflow: hidden;

		.icon-item {
			display: flex;
			align-items: center;
			height: 100%;

			text {
				font-size: 42rpx;
				color: #FFFFFF;
			}
		}

		.price {
			display: flex;
			flex-direction: column;
			justify-content: center;
			margin-left: 20rpx;

			.current-price {
				display: flex;
				align-items: center;

				// height: 60rpx;
				text {
					color: #FFFFFF;
					font-weight: bold;
				}

				.min {
					font-size: 28rpx;
				}

				.max {
					font-size: 38rpx;
				}
			}

			.original-price {
				display: flex;
				align-items: center;

				text {
					font-size: 24rpx;
					color: #FFFFFF;
					opacity: 0.7;
					text-decoration: line-through;
				}
			}
		}

		.tag {
			position: absolute;
			right: -20rpx;
			bottom: -20rpx;
			transform: rotate(-45deg);

			text {
				font-size: 68rpx;
				color: rgba(0, 0, 0, 0.2);
			}
		}
	}

	.time-item {
		display: flex;
		flex-direction: column;
		justify-content: center;
		width: 30%;
		height: 100%;
		background-color: #FEEEA8;

		// opacity: 0.5;
		.title {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100%;

			text {
				color: #FB3C0A;
				font-size: 24rpx;
			}
		}

		.time {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100%;
			height: 50rpx;

			.num {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 40rpx;
				height: 40rpx;
				font-size: 24rpx;
				color: #FB3C0A;
				background-color: $base;
				border-radius: 10rpx;
			}

			.dot {
				font-size: 24rpx;
				color: $base;
				margin: 0 5rpx;
			}
		}
	}
}


/* 限时抢购 */
.flash-price {
	padding: 20rpx;
	margin-top: 20rpx;
	display: flex;
	width: 100%;
	justify-content: space-between;
	background-color: #FFFFFF;
	// border-radius: 20rpx;
	overflow: hidden;

	.price-item {
		position: relative;
		display: flex;

		height: 100%;
		// background: linear-gradient(to left, $base, $assist-clor);
		overflow: hidden;

		.icon-item {
			display: flex;
			align-items: center;
			height: 100%;

			image {
				width: 100rpx;
				height: 100rpx;
				border-radius: 20rpx;
			}

			text {
				font-size: 42rpx;
				color: #FFFFFF;
			}
		}

		.price {
			display: flex;
			flex-direction: column;
			justify-content: center;
			margin-left: 20rpx;

			.current-price {
				width: calc(100vw - 350rpx);
				display: flex;
				align-items: center;
				font-weight: bold;

				// height: 60rpx;
				text {
					color: #222;
				}

				.min {
					font-size: 28rpx;
				}

				.max {
					font-size: 38rpx;
				}
			}

			.original-price {
				display: flex;
				align-items: center;

				text {
					font-size: 24rpx;
					color: #828282;
				}
			}
		}

		.tag {
			position: absolute;
			right: -20rpx;
			bottom: -20rpx;
			transform: rotate(-45deg);

			text {
				font-size: 68rpx;
				color: rgba(0, 0, 0, 0.2);
			}
		}
	}

	.time-item {
		width: 150rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;

		// opacity: 0.5;
		.title {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100%;
			padding: 0 16rpx;
			height: 54rpx;
			background-color: #fe3b0f;
			border-radius: 27rpx;

			text {
				color: #FFFFFF;
				font-size: 24rpx;
			}
		}

		.time {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100%;
			height: 50rpx;

			.num {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 40rpx;
				height: 40rpx;
				font-size: 24rpx;
				color: #FFFFFF;
				background-color: $base;
				border-radius: 10rpx;
			}

			.dot {
				font-size: 24rpx;
				color: $base;
				margin: 0 5rpx;
			}
		}
	}
}



/* 标题 */
.goods-title {
	padding: 0 4%;
	margin: 20rpx auto;

	text {
		font-size: 28rpx;
		color: #212121;
	}
}

/* 开通会员 */
.dredge-vip {
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 90%;
	height: 80rpx;
	margin: 20rpx auto;
	background-color: #F5F5DC;
	border-radius: 20rpx;
	overflow: hidden;

	.title {
		display: flex;
		align-items: center;
		height: 100%;
		padding: 0 4%;

		text {
			font-size: 26rpx;
			color: #333333;

			.col {
				color: $base;
				font-weight: bold;
			}
		}

		.iconfont {
			font-size: 34rpx;
			color: #333333;
			margin-right: 20rpx;
		}
	}

	.dredge {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		width: 100rpx;
		height: 80rpx;
		background-color: #464C5B;

		text {
			font-size: 24rpx;
			color: #F5F5DC;
			text-align: center;
		}
	}
}

/* 优惠 */
.goods-discounts {
	padding: 0 4%;
	background-color: #FFFFFF;
	border-radius: 20rpx;
	overflow: hidden;
	margin: 20rpx auto;

	.list {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 4%;
		height: 100rpx;
		border-bottom: 2rpx solid #f6f6f6;

		.title {
			display: flex;
			align-items: center;
			width: 15%;
			height: 100%;
			font-size: 24rpx;
			color: #212121;
		}

		.content {
			display: flex;
			align-items: center;
			width: 80%;
			height: 100%;

			>text {
				font-size: 24rpx;
				color: #555555;
			}

			.serve {
				display: flex;
				align-items: center;
				margin-right: 20rpx;

				text {
					font-size: 24rpx;
					color: #555555;
				}

				.iconfont {
					font-size: 26rpx;
					color: $base;
					margin-right: 10rpx;
				}
			}

			.coupon-list {
				position: relative;
				display: flex;
				align-items: center;
				// width: 100rpx;
				height: 30rpx;
				border: 2rpx solid $base;
				border-radius: 6rpx;
				margin-right: 20rpx;

				view {
					display: inline-block;
					padding: 0 5rpx;
					color: $base;
					font-size: 24rpx;
					transform: scale(0.8);
				}
			}

			.coupon-list:before {
				position: absolute;
				left: -10rpx;
				top: 50%;
				content: "";
				width: 12rpx;
				height: 12rpx;
				background-color: #fff;
				border-right: 2rpx solid $base;
				border-radius: 100%;
				transform: translate(0, -50%);
			}

			.coupon-list:after {
				position: absolute;
				right: -10rpx;
				top: 50%;
				content: "";
				width: 12rpx;
				height: 12rpx;
				background-color: #fff;
				border-left: 2rpx solid $base;
				border-radius: 100%;
				transform: translate(0, -50%);
			}
		}

		.more {
			display: flex;
			align-items: center;

			text {
				font-size: 24rpx;
				color: #CCCCCC;
			}
		}
	}
}

/* 评价 */
.evaluate-data {
	padding: 0 4%;
	margin: 20rpx auto;
	background-color: #FFFFFF;
	border-radius: 20rpx;
	overflow: hidden;

	.title-more {
		display: flex;
		align-items: center;
		justify-content: space-between;
		width: 100%;
		height: 100rpx;

		.title {
			display: flex;
			align-items: center;
			height: 100%;

			text {
				font-size: 28rpx;
				color: #212121;
				margin-right: 20rpx;
			}

			.num {
				font-size: 24rpx;
			}
		}

		.more {
			display: flex;
			align-items: center;

			text {
				font-size: 26rpx;
				color: #212121;
			}
		}
	}

	.evaluate-list {
		width: 100%;

		.user-info {
			display: flex;
			align-items: center;
			width: 100%;
			height: 80rpx;

			.thumb {
				width: 60rpx;
				height: 60rpx;

				image {
					width: 100%;
					height: 100%;
					border-radius: 100%;
				}
			}

			.nickname-grade {
				height: 60rpx;
				margin-left: 20rpx;

				.nickname {
					display: flex;
					align-items: center;

					text {
						font-size: 24rpx;
						color: #212121;
					}
				}

				.grade {
					display: flex;
					align-items: center;
					margin-top: 6rpx;

					text {
						font-size: 24rpx;
						color: $base;
					}
				}
			}
		}

		.content {
			width: 100%;

			.character {
				display: flex;
				align-items: center;
				padding: 10rpx 0;

				text {
					font-size: 24rpx;
					color: #333333;
				}
			}

			.attr {
				display: flex;
				align-items: center;
				padding: 10rpx 0;

				text {
					font-size: 24rpx;
					color: #CCCCCC;
				}
			}

			.thumb-list {
				display: flex;
				width: 100%;
				height: 200rpx;
				margin: 10rpx 0;

				.list {
					width: 200rpx;
					height: 200rpx;
					margin-right: 3%;

					image {
						width: 100%;
						height: 100%;
					}
				}
			}
		}

		.look-all {
			display: flex;
			align-items: center;
			justify-content: center;
			margin: 20rpx auto;

			text {
				padding: 10rpx 20rpx;
				font-size: 26rpx;
				color: #212121;
				border: 2rpx solid #f6f6f6;
				border-radius: 40rpx;
			}
		}
	}
}

/* 排行榜 */
.ranking-list {
	padding: 0 4%;
	margin: 20rpx auto;
	background-color: #FFFFFF;
	border-radius: 20rpx;
	overflow: hidden;

	.ranking-title {
		display: flex;
		align-items: center;
		width: 100%;
		height: 80rpx;

		.title {
			font-size: 26rpx;
			color: #212121;
		}
	}

	.goods-list {
		display: flex;
		flex-wrap: wrap;
		width: 100%;

		.list {
			width: 32%;
			height: 360rpx;
			border-radius: 10rpx;
			overflow: hidden;
			margin-right: 2%;

			.thumb {
				width: 100%;
				height: 200rpx;

				image {
					width: 100%;
					height: 100%;
				}
			}

			.title {
				display: flex;
				align-items: center;
				width: 100%;
				height: 80rpx;

				text {
					font-size: 24rpx;
					color: #555555;
				}
			}

			.price {
				display: flex;
				align-items: center;
				width: 100%;
				height: 60rpx;

				text {
					color: $base;
					font-size: 24rpx;
					font-weight: bold;
				}
			}
		}

		.list:nth-child(3n) {
			margin-right: 0;
		}
	}
}

/* 商品介绍 */
.products-introduction {
	padding: 0 4% 100rpx;

	.title {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		height: 80rpx;

		text {
			font-size: 28rpx;
			color: #212121;
			margin: 0 20rpx;
		}
	}

	.title:before {
		content: "";
		width: 100rpx;
		height: 2rpx;
		background-color: #c0c0c0;
	}

	.title:after {
		content: "";
		width: 100rpx;
		height: 2rpx;
		background-color: #c0c0c0;
	}

	.content {
		width: 100%;

		image {
			width: 100%;
		}

		img {
			width: 100%;
		}
	}
}

/* 底部 */
.page-footer {
	position: fixed;
	left: 0;
	bottom: 0;
	display: flex;
	width: 100%;
	height: 100rpx;
	background-color: #FFFFFF;
	border-top: 2rpx solid #f6f6f6;
	padding: 0 4%;

	.footer-fn {
		display: flex;
		align-items: center;
		width: 40%;
		height: 100%;

		.list {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			width: 50%;
			height: 100%;

			text {
				font-size: 24rpx;
				color: #555555;
			}

			image {
				width: 44rpx;
				height: 42rpx;
			}

			// .iconfont {
			// 	font-size: 42rpx;
			// 	color: #212121;
			// }
		}
	}

	.footer-buy {
		display: flex;
		align-items: center;
		justify-content: space-between;
		width: 60%;
		height: 100%;

		.cart-add {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 48%;
			height: 70rpx;
			background: linear-gradient(to right, $base, $assist-clor);
			border-radius: 70rpx;

			text {
				font-size: 26rpx;
				color: #FFFFFF;
			}
		}

		.buy-at {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 48%;
			height: 70rpx;
			background: $change-clor;
			border-radius: 70rpx;

			text {
				font-size: 26rpx;
				color: #FFFFFF;
			}
		}
	}
}

// .da{
// 	position: absolute;
// 	    height: 100%;
// 	    width:5px;
// 	    top: 0;
// 	    right: -5px;
// 	    background-image: linear-gradient(to bottom, #eeeeee 5px, transparent 5px, transparent),
// 	    radial-gradient(10px circle at 5px 10px, transparent 5px, #eeeeee 5px);
// 	    background-size: 5px 15px;
// }
::v-deep .u-mode-center-box {
	background: transparent !important;
}

::v-deep .u-model {
	background: transparent !important;
}

::v-deep .u-mode-center-box {
	background: transparent !important;
}
	.showGz {
			display: flex;
			justify-content: center;
	
			.imgbox {
				position: relative;
				display: flex;
				justify-content: center;
				width: 85%;
	
				image {
					width: 100%;
				}
	
				.inputbox {
					position: absolute;
					top: 250rpx;
					left: 0;
					right: 0;
					margin: auto;
					margin: 0 110rpx;
					height: 102rpx;
					border-radius: 10rpx 10rpx 10rpx 10rpx;
					border: 2rpx solid #8BE5ED;
	
					input {
						width: 100%;
						height: 100%;
						border: none;
						outline: none;
						text-align: center;
						font-size: 32rpx;
						color: #8BE5ED;
					}
				}
	
				.btn {
					width: 80%;
					height: 100rpx;
					position: absolute;
					bottom: 90rpx;
					display: flex;
					justify-content: space-around;
	
					.btn1,
					.btn2 {
						width: 200rpx;
						height: 80rpx;
						border-radius: 47rpx 47rpx 47rpx 47rpx;
						border: 2rpx solid #310FFF;
						display: flex;
						align-items: center;
						justify-content: center;
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 500;
						font-size: 33rpx;
						color: #310FFF;
					}
	
					.g {
						background: linear-gradient(to right, #310FFF, #310FFF);
						color: #FFFFFF;
					}
				}
			}
	
		}
	
.pinModel {
	width: 80%;
	margin: 0 auto;
	padding: 20rpx;
	background-color: #FFFFFF;
	border-radius: 20rpx;

	.pinModel-title {
		text-align: center;
		font-size: 28rpx;
		color: #333333;
		font-weight: bold;
	}

	.pin_time {
		margin-top: 20rpx;
	}

	.pin_info {
		margin-top: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;

		.pin_info_item {
			width: 100rpx;
			height: 100rpx;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-right: 30rpx;
			position: relative;

			.add {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 100rpx;
				height: 100rpx;
				border-radius: 50%;
				font-size: 40rpx;
				color: #333333;

				border: 1px dashed rgb(232, 232, 232);

			}

			.tz {
				position: absolute;
				top: 0;
				left: 0;
				font-size: 24rpx;
				color: #333333;
				font-size: 20rpx;
				padding: 2rpx 15rpx;
				border-radius: 20rpx;
				color: #000000;
				background-color: #FEEEA8;

			}
			image {
				width: 100rpx;
				height: 100rpx;
				border: 1px dashed #FEEEA8;
				border-radius: 50%;
			}



		}
	}

	.pin_info_btn {
		margin-top: 20rpx;
		width: 100%;
		height: 80rpx;
		background-color: #FB3C0A;
		border-radius: 80rpx;
		text-align: center;
		line-height: 80rpx;
		font-size: 28rpx;
		color: #FFFFFF;
	}
}
</style>