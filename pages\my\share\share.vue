	<template>

		<view class="swiper-3d" :style="{ width: canvasWidth + 'px', height: canvasHeight + 'px', marginTop: 0 }">
			<swiper :style="{ width: '100%', height: canvasHeight + 'px' }" :circular="true">
				<swiper-item v-for="(item, index) in  imgbox" :key="index"
					@longtap="saveImgToLocal(`firstCanvas${index}`, index)">
					<view class="swiper-item"
						:style="{ width: canvasWidth + 'px', height: canvasHeight + 'px', position: 'relative' }">
						<image :src="$fun.imgUrl(imgbox[index])"
							:style="{ width: canvasWidth + 'px', height: canvasHeight + 'px' }"></image>
						<view
							:style="{ width: list[index].qr_width+60 + 'px', height: list[index].qr_height+60 + 'px', top: canvasHeight * (list[index].y_axis_bl) - 30 + 'px', left: canvasWidth * (list[index].x_axis_bl)-30 + 'px', zIndex: '10', position: 'absolute' }">
							<view
								:style="{ width: list[index].qr_width+10 + 'px', height: list[index].qr_height+10 + 'px', top:15+'px', left: `25px`, zIndex: '9', position: 'absolute',background:'#FFFFFF' }">
								<image v-if="canvasQrPath" class="qr" :src="canvasQrPath"
									:style="{ width: list[index].qr_width + 'px', height: list[index].qr_height + 'px',margin:'5px',zIndex: '11'}">
								</image>
							</view>
						</view>
						<view class="saveName"
							:style="{ width: list[index].qr_width+15 + 'px', top: canvasHeight * (list[index].y_axis_bl) + list[index].qr_height  + 'px', left: canvasWidth * (list[index].x_axis_bl)-10 + 'px', zIndex: '10', position: 'absolute' }">


							{{ invitation }}
						</view>
						<!-- 					<canvas :ref="`firstCanvas${index}`"
							:style="{width: canvasWidth+'px',height:canvasHeight+'px',position:'absolute' }"
							:canvas-id="`firstCanvas${index}`"></canvas> -->
						<view class="save"
							:style="{ top: canvasHeight * (list[index].y_axis_bl) + list[index].qr_height + 20 + 'px' }">
							<!-- #ifdef APP-PLUS -->
							<!-- 		<view class="text" @click="saveImgToLocal(`firstCanvas${index}`, index)">
								保存至相册
							</view> -->
							<!-- #endif -->
						</view>
					</view>

					<canvas :ref="`firstCanvas${index}`"
						:style="{width: canvasWidth+'px',height:canvasHeight+'px',position:'absolute',top:'-10000px',left:'-10000px'}"
						:canvas-id="`firstCanvas${index}`"></canvas>
				</swiper-item>
			</swiper>
			<yz-qr ref="qrPath" :text="text" :size="size" :colorDark="colorDark" @getQrPath="getQrPath"
				:colorLight="colorLight"> </yz-qr>
		</view>
	</template>
	<script>
		import yzQr from "@/components/yz-qr/yz-qr.vue"
		export default {
			components: {
				yzQr
			},
			data() {
				return {
					imgbox: [],
					imgSrc: '',
					canvasQrPath: "",
					codeImg: "",
					text: '',
					size: 150,
					colorDark: '#000000',
					colorLight: '#ffffff',
					list: [],
					canvasHeight: 0,
					canvasWidth: 0,
					invitation: ''
				};
			},
			onLoad(option) {
				// #ifdef MP-WEIXIN
				if (option.scene) {
					this.$fun.saveScene(option.scene)
				}
				// #endif
				this.invitation = uni.getStorageSync('userinfo').invitation
				// this.$api.inviteImg().then(res => {
				// 	let data = []
				// 	res.data.forEach((item) => {
				// 		data.push(this.$fun.imgUrl(item.image))
				// 	})
				// 	this.imgbox = data
				// })
				const res = uni.getSystemInfoSync();
				console.log(res);

				// config/invitation
				this.$fun.ajax.post('/config/invitation', {}).then(res => {
					if (res.status == 1) {
						let data = []
						res.data.forEach((item) => {
							data.push(this.$fun.imgUrl(item.image))
						})
						this.imgbox = data
						this.list = res.data
					}
				})
				// #ifdef MP-WEIXIN
				this.getaccesstoken()
				// #endif
			},
			onShareAppMessage() {
				if (!uni.getStorageSync('token')) {
					this.$fun.msg('请先登录再分享!');
					return false
				} else {
					let shareParams = {
						title: `欢迎来到本商城`,
						path: `/pages/my/login/login?scene=${ uni.getStorageSync('userInfo').invitation}`
					};
					console.log(shareParams)
					return shareParams;
				}

			},
			onShareTimeline: function(res) {
				if (!uni.getStorageSync('token')) {
					this.$fun.msg('请先登录再分享!');
					return false
				} else {
					// console.log({
					// 	title: `欢迎来到本商城`, //字符串  自定义标题
					// 	query: `scene=${ uni.getStorageSync('userInfo').invitation}`, //页面携带参数
					// 	imageUrl: this.imgbox [0] //图片地址
					// })
					return {
						title: `欢迎来到本商城`, //字符串  自定义标题
						query: `scene=${ uni.getStorageSync('userInfo').invitation}`, //页面携带参数
						imageUrl: this.imgbox[0] //图片地址
					}
					// uni.canvasToTempFilePath({
					// 	canvasId: `firstCanvas0`,
					// 	success: function(res1) {
					// 		console.log(res1.tempFilePath)
					// 		return {
					// 			title: `欢迎来到本商城`, //字符串  自定义标题
					// 			query: `scene=${ uni.getStorageSync('userInfo').invitation}`, //页面携带参数
					// 			imageUrl: res1.tempFilePath //图片地址
					// 		}
					// 	}
					// }, this)
				}
			},
			methods: {
				wx_share() {
					uni.showShareMenu()
				},
				// 获取token
				getaccesstoken() {
					let that = this
					that.$fun.ajax.post('/user/miniImage', {}).then(res => {
						uni.getImageInfo({
							src: that.$fun.imgUrl(res.data),
							success(res) {
								that.codeImg = res.path
								that.canvasQrPath = res.path
								that.drawImage()
							}
						})
					})
				},
				/**
				 * @param {Object} shareToken
				 */
				getWxCode(shareToken) { //获取小程序码
					let that = this
					uni.showLoading({
						title: '加载中',
						mask: true
					})
					uni.request({
						url: `https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=${shareToken}`,
						method: "POST",
						data: {
							width: 300,
							page: 'pages/public/login',
							scene: uni.getStorageSync('userInfo').id,
						},
						responseType: 'arraybuffer',
						success: function(res) {
							console.log(res)
							uni.hideLoading();
							let src = uni.arrayBufferToBase64(res.data);
							that.codeImg = 'data:image/png;base64,' + src;

						}
					})
				},
				// 绘制二维码  H5
				getQrPath(tempFilePath, text) {
					this.text = text
					var that = this;
					that.canvasQrPath = tempFilePath
					that.drawImage()
					// this.text =
					// 	`${this.$fun.h5Url()}/#/pages/public/loginType?&promo_code=${uni.getStorageSync('userInfo').invitation}`
					// var that = this;
					// setTimeout(function() {
					// 	that.$refs.batchQRCode.toTempFilePath({
					// 		success: res => {
					// 			console.log(res)
					// 			that.canvasQrPath = res.tempFilePath
					// 			that.drawImage()
					// 		},
					// 		fail: err => {
					// 			uni.showToast({
					// 				icon: 'none',
					// 				title: JSON.stringify(err)
					// 			})
					// 		}
					// 	})
					// }, 800)
				},
				// 保存图片
				saveImgToLocal(canvasId, index) {
					// #ifdef H5
					this.$fun.msg('H5请截图分享或下载APP长按保存');
					return;
					// #endif
					let that = this
					uni.showModal({
						title: '提示',
						content: '确定保存到相册吗',
						success: (res) => {
							if (res.confirm) {
								uni.canvasToTempFilePath({
									canvasId: `${canvasId}`,
									success: function(res1) {
										console.log(res1.tempFilePath)
										uni.saveImageToPhotosAlbum({
											filePath: res1
												.tempFilePath,
											success: function() {
												uni.showToast({
													title: "保存成功",
													icon: "none"
												});
											},
											fail: function() {
												uni.showToast({
													title: "保存失败",
													icon: "none"
												});
											}
										});
									}
								}, this)
							} else if (res.cancel) {

							}
						}
					});
				},
				// 画图
				drawImage() {
					var that = this;
					for (let i = 0; i < this.imgbox.length; i++) {
						that.canvasHeight = uni.getSystemInfoSync().windowHeight
						that.canvasWidth = uni.getSystemInfoSync().windowWidth
						uni.getImageInfo({
							src: this.imgbox[i],
							success(res) {
								console.log(res)
								let ctx = uni.createCanvasContext(`firstCanvas${i}`) // 使用画布创建上下文 图片
								ctx.drawImage(res.path, 0, 0, that.canvasWidth,
									that.canvasHeight) // 设置图片坐标及大小，括号里面的分别是（图片路径，x坐标，y坐标，width，height）
								// x y
								let x = that.canvasWidth * (that.list[i].x_axis_bl);
								let y = that.canvasHeight * (that.list[i].y_axis_bl);
								let x1 = x - 5;
								let y1 = that.canvasHeight * (that.list[i].y_axis_bl) + that.list[i].qr_height + 5;
								// 起始点
								ctx.moveTo(x - 5, y - 5)
								//02 划线  坐标
								ctx.lineTo(x1, y1 + 5)
								ctx.lineTo(x + that.list[i].qr_width + 5, y1 + 5)
								ctx.lineTo(x + that.list[i].qr_width + 5, y - 5)
								ctx.lineTo(x1, y - 5)
								// 以上两行代码只是一个路径，但还没有绘制
								// 03 绘制
								ctx.fillStyle = "#FFFFFF"
								ctx.fill();

								// 起始点
								ctx.moveTo(x - 5, y - 5)
								//02 划线  坐标
								ctx.lineTo(x1, y1)
								ctx.lineTo(x + that.list[i].qr_width + 5, y1)
								ctx.lineTo(x + that.list[i].qr_width + 5, y - 5)
								ctx.lineTo(x1, y - 5)
								// 以上两行代码只是一个路径，但还没有绘制
								// 03 绘制
								// ctx.fillStyle =  "rgb(166,197,179)"
								ctx.fill();



								// #ifdef MP-WEIXIN
								ctx.drawImage(that.codeImg, x, y, that.list[i].qr_width,
									that.list[i].qr_height)
								console.log(that.codeImg);
								ctx.textAlign = 'center'; //字体居中
								ctx.font = 'normal 16px STXingkai'; // 字体
								ctx.setFontSize(16) //设置字体大小，默认10
								ctx.setFillStyle('#ffc387') //文字颜色：默认黑色
								ctx.fillText(uni.getStorageSync('userinfo').invitation, x1 + 42, y1 +
									26); //文字内容、x坐标，y坐标
								// 设置图片坐标及大小，括号里面的分别是（图片路径，x坐标，y坐标，width，height）
								// #endif
								// #ifdef H5 || APP-PLUS
								ctx.drawImage(that.canvasQrPath, that.canvasWidth * (that.list[i].x_axis_bl), that
									.canvasHeight * (that.list[i].y_axis_bl), that.list[i].qr_width,
									that.list[i].qr_height) // 设置图片坐标及大小，括号里面的分别是（图片路径，x坐标，y坐标，width，height）
								ctx.textAlign = 'center'; //字体居中
								ctx.font = 'normal 16px STXingkai'; // 字体
								ctx.setFontSize(16) //设置字体大小，默认10
								ctx.setFillStyle('#ffc387') //文字颜色：默认黑色
								ctx.fillText(uni.getStorageSync('userinfo').invitation, x1 + 82, y1 +
									26); //文字内容、x坐标，y坐标
								// #endif
								ctx.save(); //保存
								ctx.draw() //绘制
							}
						})
					}

				}
			}
		}
	</script>

	<style lang="scss">
		page {
			position: relative;
		}

		.img_box {
			position: fixed;
			bottom: 50rpx;
			width: 80%;
			display: flex;
			justify-content: space-around;
			z-index: 100;
			left: 0;
			right: 0;
			margin: auto;

			image {
				width: 100rpx;
				height: 100rpx;
			}
		}

		.swiper-3d {
			padding: 0upx;
			position: absolute;
			top: 0;
			left: 0;
			bottom: 0;
			right: 0;
			margin: auto;

			.s-container {
				height: 900upx;
				width: 100%;

				.swiper-item {
					// max-width: 630upx;
					height: 90%;
					padding: 0upx 20upx;
					box-sizing: border-box;
					position: relative;

				}

				.item-img {
					// position: absolute;
					margin-top: 30upx;
					width: 100%;
					height: 80%;
					// border-radius: 15upx;
					z-index: 5;
					// opacity: 0.7;
					// top: 7%;
					// transform: translateY(-50%);
					// box-shadow:0px 4upx 15upx 0px rgba(153,153,153,0.24);
				}

				.active {
					opacity: 1;
					z-index: 10;
					height: 90%;
					top: -3%;
					transition: all .1s ease-in 0s;
					transform: translateY(0);
				}
			}

			.swiper-dot {
				display: flex;
				justify-content: center;
				align-items: center;

				// padding-top: 10upx;
				.dot {
					margin: 0 10upx;
					width: 15upx;
					height: 15upx;
					border-radius: 50%;
					background: #bbb;

					&.on {
						background: #F4BD48;
					}
				}
			}
		}

		.save {
			position: absolute;
			width: 100%;
			display: flex;
			justify-content: space-around;
			z-index: 100;

			.text {
				width: 120px;
				height: 38px;
				border-radius: 19px;
				background-color: #fff;
				color: #ffc387;
				text-align: center;
				line-height: 38px;
			}
		}

		.saveName {
			position: absolute;
			display: flex;
			justify-content: center;
			font-weight: bold;
			color: #ffc387;
			font-size: 16px;
		}
	</style>