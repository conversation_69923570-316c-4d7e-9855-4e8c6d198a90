<template>
	<view class="page">
		<view class="my-top">
			<!-- #ifndef MP-WEIXIN -->
			<view style="height: 100rpx;">

			</view>
			<!-- #endif -->
			 			<!-- #ifdef MP-WEIXIN -->
			<view style="height: 20rpx;">

			</view>
			<!-- #endif -->
			<view class="user-info" v-if="userInfo">
				<view class="portrait">
					<image :src="$fun.imgUrl(userInfo.avatar)"></image>
				</view>
				<view class="info">
					<view class="nickname">
						<text>{{userInfo.nickname}}</text>

					</view>
					<view class="rank">
						<text>账号：{{userInfo.mobile}}</text>
					</view>
				</view>
				<block v-if="!userInfo.is_user">
					<view class="gz_btn" v-if="!userInfo.follow" @click="gz(1)">
						关注
						<text>+</text>
					</view>
					<view class="gz_btn" @click="gz(0)" v-else style="color: #333333;background: #EFEFEF;">
						已关注
					</view>
				</block>

			</view>
			<view class="focus-area" v-if="userInfo">
				<view class="list">
					<view class="num">
						<text> {{userInfo.like}}</text>

					</view>
					<view class="title">
						<text> 点赞</text>
					</view>
				</view>
				<view class="list">
					<view class="num">
						<text> {{userInfo.follow}}</text>

					</view>
					<view class="title">
						<text> 关注</text>
					</view>
				</view>
				<view class="list">
					<view class="num">
						<text> {{userInfo.tofollow}}</text>
					</view>
					<view class="title">
						<text> 粉丝</text>
					</view>
				</view>
			</view>
		</view>
		<view style="height: 207px;">

		</view>
		<view class="live_list">
			<view class="live_title">
				视频
			</view>

			<block v-if="userInfo.data.length">
				<view class="live_item" v-for="(item,index) in userInfo.data" :key="index" @click="lookVideo(item)">
					<!-- {{$fun.imgUrl(item.coverimage)}} -->
					<image class="live_item_img" :src="$fun.imgUrl(item.coverimage)" mode=""></image>
					<view style="display: flex;justify-content: space-between;align-items: center;">
						<view class="title">
							{{item.title}}
						</view>
						<view class="time" style="display: flex;justify-content: space-between;align-items: center;">
							<u-icon name="thumb-up-fill" color="#310FFF"></u-icon>
							<text style="margin-left: 10rpx;">{{item.dianzan}}</text>
						</view>
					</view>
				</view>
			</block>

		</view>
		<view v-if="!userInfo.data.length" class="noneDate">
			暂时没有发布视频
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				userInfo: {
					share: []
				},
				user_id: null
			};
		},
		onShow() {
			this.init()
		},
		onLoad(option) {
			this.user_id = option.user_id;
		},
		methods: {
			async init() {
				await this.getUserInfo()
			},
			lookVideo(item) {

				uni.setStorageSync('isbottom', false);
				uni.setStorageSync('lookUser', this.user_id);
				this.$fun.jump(`./videCcircle?user_id=${this.user_id}&id=${item.id}`)
			},
			/**
			 * 获取用户信息
			 */
			async getUserInfo() {
				await this.$fun.ajax.post('video/mykbList', {
					user_id: this.user_id
				}).then(res => {
					if (res.status == 1) {
						this.userInfo = res.data
					}
				})
			},
			async gz(status) {
				await this.$fun.ajax.post('video/follow', {
					id: this.user_id
				}).then(res => {
					if (res.status == 1) {
						this.getUserInfo();
					}
				})
			},

		}
	}
</script>

<style lang="scss">
	page {
		background: #FFFFFF;
	}

	.my-top {
		position: absolute;
		top: 0;
		width: 100%;
		background: #310FFF;
		padding-bottom: 114rpx;

		/* 用户信息 */
		.user-info {
			display: flex;
			align-items: center;
			padding: 0 5%;
			height: 120rpx;
			// margin-top: 100rpx;
			.portrait {
				width: 120rpx;
				height: 120rpx;
				margin-right: 20rpx;
				box-sizing: border-box;

				image {
					width: 100%;
					height: 100%;
					border-radius: 100%;
					box-sizing: border-box;
				}
			}

			.endTime {
				display: flex;
				justify-content: center;
				flex-direction: column;
				align-items: center;
				height: 80px;
				color: #FFFFFF;
			}

			.info {
				display: flex;
				flex-direction: column;
				justify-content: center;
				width: 55%;
				height: 100%;

				.nickname {
					width: 100%;
					display: flex;
					margin-right: 10rpx;

					text {

						font-family: Source;
						font-weight: 500;
						font-size: 40rpx;
						color: #FFFFFF;
					}


				}

				.rank {
					display: flex;
					align-items: center;
					width: fit-content;
					height: 35rpx;
					padding-right: 10rpx;
					border-radius: 30rpx;
					font-family: Source;
					font-weight: 400;
					font-size: 24rpx;
					color: #FFFFFF;

					image {
						width: 24rpx;
						height: 24rpx;
					}

					text {
						font-size: 24rpx;
						color: #FFFFFF;
					}
				}
			}
		}

		.gz_btn {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 120rpx;
			height: 50rpx;
			background: #FFFFFF;
			border-radius: 25rpx 25rpx 25rpx 25rpx;
			font-family: Source;
			font-weight: 400;
			font-size: 24rpx;
			color: #F2671D;

			text {
				margin-left: 10rpx;
				font-size: 40rpx;
			}
		}

		/* 关注区 */
		.focus-area {
			margin-top: 40rpx;
			padding: 0 104rpx;
			display: flex;
			align-items: center;
			width: 100%;
			height: 120rpx;

			.list {
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				width: 33%;
				height: 100%;

				.num {
					display: flex;
					align-items: center;

					text {
						font-family: Source;
						font-weight: 500;
						font-size: 36rpx;
						color: #FFFFFF;
					}
				}

				.title {
					display: flex;
					align-items: center;
					margin-top: 5rpx;

					text {
						font-family: Source;
						font-weight: 400;
						font-size: 28rpx;
						color: #FFFFFF;
					}
				}
			}
		}

		/* vip */
		.vip-info {
			position: absolute;
			left: 50%;
			bottom: 0;
			display: flex;
			justify-content: space-between;
			padding: 0 4%;
			width: 90%;
			height: 80rpx;
			background-color: #464C5B;
			transform: translate(-50%, 0);
			border-radius: 10rpx 10rpx 0 0;

			.vip {
				position: relative;
				display: flex;
				align-items: center;
				width: 50%;
				height: 60rpx;

				text {
					color: #ffe678;
					font-size: 26rpx;
				}

				.line {
					position: absolute;
					right: 0;
					top: 40%;
					width: 2rpx;
					height: 20rpx;
					background-color: #ffe678;
				}
			}

			.vip-explain {
				display: flex;
				align-items: center;
				height: 60rpx;
				margin: 0 10rpx;

				text {
					color: #ffe678;
					font-size: 24rpx;
				}
			}

			.vip-btn {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 140rpx;
				height: 40rpx;
				background-color: #ffe678;
				border-radius: 30rpx;
				margin-top: 10rpx;

				text {
					font-size: 24rpx;
					color: #464C5B;

				}
			}
		}

		.my-service {
			position: absolute;
			bottom: -110rpx;
			left: 0;
			right: 0;
			margin: auto;
		}
	}

	.live_list {
		position: relative;
		z-index: 2;
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		padding: 32rpx 24rpx;
		background: #FFFFFF;
		border-radius: 60rpx 60rpx 0rpx 0rpx;

		.live_title {
			width: 100%;
			font-family: Source;
			font-weight: bold;
			font-size: 32rpx;
			color: #333333;
			margin-bottom: 24rpx;
		}

		.live_item {
			margin-bottom: 20rpx;
			padding-bottom: 30rpx;
			width: 342rpx;
			background: #FFFFFF;
			box-shadow: 0rpx 8rpx 8rpx 0rpx rgba(0, 0, 0, 0.1);
			border-radius: 16rpx 16rpx 16rpx 16rpx;

			.live_item_img {
				width: 342rpx;
				height: 342rpx;
				border-radius: 16rpx 16rpx 0rpx 0rpx;


			}

			.title {
				width: 200px;
				/* 设定容器宽度 */
				white-space: nowrap;
				/* 确保文本不会换行 */
				overflow: hidden;
				/* 隐藏超出容器的内容 */
				text-overflow: ellipsis;
				/* 添加省略号表示内容被截断 */
				margin-top: 16rpx;
				font-family: Source;
				font-weight: 500;
				font-size: 28rpx;
				color: #19352D;
				padding: 0 12rpx;
			}

			.time {
				padding: 0 12rpx;
				margin-top: 10rpx;
				font-family: Source;
				font-weight: 500;
				font-size: 24rpx;
				color: #AEB2B1;
			}
		}
	}

	.noneDate {
		position: absolute;
		bottom: 400rpx;
		left: 0;
		right: 0;
		margin: auto;
		text-align: center;
		font-family: Source;
		font-weight: 400;
		font-size: 28rpx;
		color: #AAAAAA;
	}

	// 绑定微信公众号
	.notice-box {
		width: 650rpx;
		margin: 0 auto;
		height: 70rpx;
		// background: rgba(253, 239, 216, 1);
		background: #FFFFFF;
		padding: 0 35rpx;

		.notice-detail {
			font-size: 24rpx;

			font-weight: 400;
			color: #000000;
		}

		.bindPhone {
			width: 135rpx;
			line-height: 52rpx;
			background: linear-gradient(90deg, rgba(233, 180, 97, 1), rgba(238, 204, 137, 1));
			border-radius: 26rpx;
			padding: 0;
			font-size: 26rpx;

			font-weight: 500;
			// color: rgba(255, 255, 255, 1);
			color: #000000;
		}
	}

	::v-deep .u-column {
		width: 50%;

		.list {
			width: 94% !important;
			margin-left: 3%;
			height: auto !important;
		}
	}
</style>