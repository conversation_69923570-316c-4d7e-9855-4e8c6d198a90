<template>
	<view class="conbox">
		<!-- <view class="lottery_top">
			<image class="top" src="/static/temp/zi-2.png" mode=""></image>
			<view class="qd" @click="jump('./qd')">
				签到记录
			</view>
		</view>
		<view class="mrqd">
			<image src="/static/temp/zi-1.png" mode=""></image>
		</view>
		<view class="qd_">
			<image src="/static/temp/qiandao.png" @click="jump('./qd')" mode=""></image>
		</view> -->
		<view class="container">
			<view class="main_circle">
				<view class="canvas-container">
					<view class="lottery_zz">

					</view>
					<view :animation="animationData" class="canvas-content" id="zhuanpano" style="">
						<view class="canvas-list">
							<view class="canvas-item"
								:style="{transform: 'rotate('+(index * width+15)+'deg)', zIndex:index, }"
								v-for="(iteml,index) in list" :key="index">
								<view class="canvas-item-text" :style="'transform:rotate('+(index )+')'">
									<text class="b" style="font-size: 32upx;">{{iteml.name}}</text>
									<image class="icon-awrad iconfont " :src="$imgUrl(iteml.image)"></image>
								</view>
							</view>
						</view>
					</view>

					<view @tap="playReward" class="canvas-btn" v-bind:class="btnDisabled"> </view>
				</view>
			</view>
		</view>

	</view>
</template>

<script>
	export default {
		name: "TurntableReward",
		data() {
			return {
				runDeg: 0, //旋转角度
				list: this.listArr, //内容
				width: this.widthNum, //宽度
				animationData: {}, //动画
				durationTime: 6000, //动画持续时间
				btnDisabled: '', //按钮不可点击
				animationRun: null, //动画
				timeOutId: null, //定时器
				stopRewardFlag: false, //是否停止抽奖
				n: 0, //相遇次数
				old:1
			}
		},
		props: {
			// 数据
			widthNum: {
				type: Number,
				default: 0
			},
			resultIndex: {
				type: Number,
				default: 0
			},
			rewardInstruction: {
				type: String,
				default: ""
			},
			listArr: {
				type: Array,
				default: new Array()
			}
		},
		watch: {
			listArr: {
				deep: true,
				handler(v) {
					this.list = v
				}
			},
			// resultIndex(v) {
			// 	this.resultIndex = v
			// },
			widthNum(v) {
				this.width = v
			},
			rewardInstruction(v) {
				this.rewardInstruction = v
			}
		},
		methods: {
			jump(url){
				uni.navigateTo({
					url
				})
			},
			animation() {
				let index = this.resultIndex;    
				//中奖index
				var list = this.list;
				// var awardIndex = 1 || Math.round(Math.random() * (awardsNum.length - 1)); //随机数
				var runNum = 6; //旋转6周
				let that = this;
				// 旋转角度
				if(this.old>=index){
					this.n+=1
				}
				// this.runDeg =  360 * runNum;
				this.runDeg =  (this.n*-360);
				console.log(this.runDeg)
				// console.log(this.old)
				console.log(index)
				//创建动画
				let animationRun = uni.createAnimation({
					duration: that.durationTime,
					timingFunction: 'ease'
				})
				  animationRun.rotate(this.runDeg).step()
				this.animationData = animationRun.export()
				this.btnDisabled = 'disabled';
				this.old = index
			},
			//发起抽奖
			playReward() {
				// this.animation();
				this.$emit('goPlayReward');
			},
			openReward(rewardIndex) {
				//停止转后开奖
				this.animationRun.rotate(this.runDeg + (360 - rewardIndex * (360 / this.list.length))).step();
				this.animationData = this.animationRun.export();
				this.timeOutId = setTimeout(() => {
					if (this.stopRewardFlag) {
						return false;
					}
					uni.showModal({
						title: "抽奖结果",
						content: this.list[rewardIndex].is_no_prize ? this.list[rewardIndex].reward_name :
							'恭喜，中奖' + this.list[rewardIndex].reward_name
					})
					clearTimeout(this.timeOutId);
				}, this.durationTime + 1000)
			},
			stopReward() {
				this.stopRewardFlag = true;
				clearTimeout(this.timeOutId);
			}
		}
	}
</script>
<style lang="scss" scoped>
	.main_circle {
		padding-top: 230upx;
	}

	.icon-awrad {
		font-size: 50upx !important;
		width: 90rpx;
		height: 100rpx;
		margin-top: 50rpx;
	}

	.conbox {
		width: 750upx;
		height: 100vh;
		overflow-x: hidden;
		overflow-y: scroll;
	}

	.lottery_top {
		text-align: center;
		margin-top: 45rpx;
		position: relative;

		.top {
			width: 480rpx;
			height: 25rpx;
			margin: 0 auto;
		}

		.qd {
			width: 120rpx;
			height: 40rpx;
			line-height: 40rpx;
			background: rgba(0, 145, 106, 0.5);
			border-radius: 20rpx;
			position: absolute;
			right: 15rpx;
			top: -8rpx;
			color: #FFFFFF;
			font-size: 20rpx !important;
		}
	}

	.mrqd {
		margin-top: 44rpx;
		display: flex;
		justify-content: center;

		image {
			width: 417rpx;
			height: 79rpx;
		}
	}

	.qd_ {
		margin-top: 30rpx;
		display: flex;
		justify-content: center;

		image {
			width: 186rpx;
			height: 43rpx;
		}
	}


	.container.cont {
		width: 750upx;
		min-height: 100vh;
		height: auto;
		position: relative;
	}

	.cont {
		width: 100%;
		height: 100%;
		position: absolute;
		z-index: 0;
	}

	.caidai {
		position: absolute;
		top: var(--status-bar-height)+68rpx;
		left: 0;
		width: 750upx;
		height: 1024upx;
	}

	/* 转盘 */
	.canvas-container {
		margin: 0 auto;
		position: relative;
		width: 718rpx;
		height: 723rpx;
		border-radius: 50%;
	}


	.canvas-content {
		position: absolute;
		left: 0;
		top: 0;
		z-index: 1;
		display: block;
		width: 718rpx;
		height: 723rpx;
		border-radius: inherit;
		/* background-clip: padding-box; */
		/* background-color: #ffcb3f; */
	}

	.canvas-list {
		position: absolute;
		left: 0;
		top: 0;
		width: inherit;
		height: inherit;
		z-index: 0;
		background: url("../../../temp/pan.png") no-repeat;
		background-size: cover;
	}

	.canvas-item {
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		color: #e4370e;
		/* text-shadow: 0 1upx 1upx rgba(255, 255, 255, 0.6); */
	}

	.canvas-item-text {
		position: relative;
		display: block;
		padding-top: 48upx;
		margin: 0 auto;
		text-align: center;
		-webkit-transform-origin: 50% 300upx;
		transform-origin: 50% 300upx;
		display: flex;
		flex-direction: column;
		align-items: center;
		color: #FB778B;
		font-size: 30upx;
	}

	/* 分隔线 */
	.canvas-line {
		position: absolute;
		left: 0;
		top: 0;
		width: inherit;
		height: inherit;
		z-index: 99;
	}

	.canvas-litem {
		position: absolute;
		left: 300upx;
		top: 0;
		width: 3upx;
		height: 300upx;
		background-color: rgba(228, 55, 14, 0.4);
		overflow: hidden;
		-webkit-transform-origin: 50% 300upx;
		transform-origin: 50% 300upx;
	}

	/**
* 抽奖按钮
*/
	.canvas-btn {
		background: url('../../../temp/anniu.png') no-repeat;
		border-radius: 50%;
		background-size: 100% 100%;
		position: absolute;
		top: calc(50% - 108rpx);
		left: calc(50% - 107rpx);
		width: 214rpx;
		height: 216rpx;
		z-index: 1000;
	}

	// .canvas-btn::after {
	// 	position: absolute;
	// 	display: block;
	// 	content: ' ';
	// 	left: 12upx;
	// 	top: -44upx;
	// 	width: 0;
	// 	height: 0;
	// 	overflow: hidden;
	// 	border-width: 30upx;
	// 	border-style: solid;
	// 	border-color: transparent;
	// 	border-bottom-color: #e44025;
	// }

	// .canvas-btn.disabled {
	// 	pointer-events: none;
	// 	background: #b07a7b;
	// 	color: #ccc;
	// }

	// .canvas-btn.disabled::after {
	// 	border-bottom-color: #b07a7b;
	// }

	.lottery_zz {
		width: 83rpx;
		height: 112rpx;
		background: url('../../../temp/jiantou.png') no-repeat;
		background-size: 83rpx 112rpx;
		position: absolute;
		top: 0;
		left: calc(50% - 20rpx);
		top: -36rpx;
		z-index: 1000;
	}

	.guize {
		width: 502upx;
		min-height: 300upx;
		display: flex;
		flex-direction: column;
		position: relative;
		z-index: 0;
		background-image: linear-gradient(-180deg, #F48549 0%, #F2642E 100%);
		border: 18upx solid #E4431A;
		border-radius: 16px;
		margin: 0 auto;
		margin-top: -104upx;
		padding: 48upx;
		/* box-sizing: border-box; */
		color: #fff;
	}

	.guize .title {
		text-align: center;
		font-size: 30upx;
		font-weight: bold;
		margin-bottom: 28upx;
	}

	.guize .g_item {
		font-family: PingFang-SC-Medium;
		font-size: 24upx;
		color: #FFFFFF;
		letter-spacing: 0.5px;
		text-align: justify;
	}

	.myrewards .title {
		font-family: PingFang-SC-Bold;
		font-size: 16px;
		color: #E4431A;
		letter-spacing: 0.57px;
		display: flex;
		padding-top: 36upx;
		justify-content: center;
	}
</style>
