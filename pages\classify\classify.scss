page{
	background-color: #FFFFFF;
}
.page{
	position: absolute;
	width: 100%;
	height: 100%;
	overflow-x: hidden;
	overflow-y: auto;
	padding-bottom: 100rpx;
	background-color: #FFFFFF;
}
	.head-search {
		display: flex;
		align-items: center;
		justify-content: space-between;
		background: #310FFF;
		padding-bottom: 20rpx;
		.search {
			display: flex;
			align-items: center;
			justify-content: flex-start;
			width: 100%;
			background: #FFFFFF;
			margin: 0 32rpx;
			padding: 0 20rpx;
			height: 76rpx;
			border-radius: 10rpx;
			border-radius: 38rpx 38rpx 38rpx 38rpx;
	
			.icon {
				display: flex;
				align-items: center;
				margin-right: 20rpx;
	
				image {
					width: 40rpx;
					height: 40rpx;
				}
			}
	
			.hint {
				display: flex;
				align-items: center;
	
				.max {
					font-size: 30rpx;
					font-weight: bold;
					color: #FFFFFF;
				}
	
				.min {
					font-size: 24rpx;
					color: #AAAAAA;
					margin-left: 10rpx;
				}
			}
		}
	}
/* 搜索 */
.search-index{
	position: fixed;
	left: 0;
	top: 0;
	/* #ifdef APP-PLUS */
	top: 80rpx;
	/* #endif */
	z-index: 10;
	display: flex;
	justify-content: space-between;
	align-items: center;
	width: 100%;
	height: 100rpx;
	padding: 0 4%;
	background-color: #FFFFFF;
	.icon{
		display: flex;
		align-items: center;
		justify-content: space-around;
		width: 10%;
		height: 100%;
		text{
			color: #333333;
			font-size: 52rpx;
		}
	}
	.search{
		display: flex;
		align-items: center;
		padding: 0 3%;
		width: 75%;
		height: 60rpx;
		background-color: #f6f6f6;
		border-radius: 60rpx;
		.iconfont{
			font-size: 28rpx;
			color: #C0C0C0;
		}
		input{
			width: 90%;
			height: 100%;
			color: #212121;
			font-size: 24rpx;
			margin-left: 10rpx;
		}
	}
}
/* 分类数据 */
.classify-data{
	display: flex;
	width: 100%;
	height: 90%;
	background-color: #FFFFFF;
	// margin-top: 100rpx;
	/* #ifdef APP-PLUS */
	// margin-top: 170rpx;
	/* #endif */
	.classify-one{
		width: 25%;
		height: 100%;
		background-color: #f6f6f6;
		.classify-list{
			width: 100%;
			height: 100%;
			.list{
				display: flex;
				align-items: center;
				justify-content: center;
				display: flex;
				flex-direction: column;
				width: 100%;
				height: 80rpx;
				text{
					display: flex;
					align-items: center;
					justify-content: center;
					width: 100%;
					font-family:YaHei;
					// color: #C0C0C0;
						font-weight: 400;
						font-size: 28rpx;
						color: #333333;
						line-height: 33rpx;
						text-align: center;
						font-style: normal;
						text-transform: none;
					box-sizing: border-box;
				}
			}
			.action{
				background-color: #FFFFFF;
				display: flex;
				flex-direction: column;
				text{
					font-size: 28rpx;
					color: #212121;
					// border-left: 6rpx solid $base;
					box-sizing: border-box;
				}
				image{
				}
			}
		}
	}
	.classify-two-three{
		width:calc(75% - 20rpx);
		height: 100%;
		padding-left: 20rpx;
		.banner{
			margin-top: 20rpx;
			 height: 186rpx;
			 border-radius: 10rpx;
			 overflow: hidden;
				 .screen-swiper{
					 height: 186rpx;
					 min-height: 100% !important;
					 image{
					 height: 186rpx;
					 border-radius: 10rpx;
								}
							}
						}
						
		.scroll{
			width: 100%;
			height: 100%;
			.store_list{
				margin-top: 24rpx;
				display: flex;
				flex-wrap: wrap;
				.store_item{
					margin-top: 22rpx;
					width: 25%;
					image{
						width: 100rpx;
						height: 100rpx;
						box-shadow: 0rpx 2rpx 8rpx 0rpx rgba(0,0,0,0.25);
						border-radius: 50%;
					}
					.store_name{
						font-weight: 400;
						font-size: 24rpx;
						color: #000000;
					}
				}
			}
			.classify-two{
				margin-top: 20rpx;
				width: 100%;
				.two-name{
					display: flex;
					justify-content: space-between;
					align-items: center;
					width: 100%;
					height: 80rpx;
					.name{
						font-family: Microsoft YaHei, Microsoft YaHei;
						font-weight: bold;
						font-size: 32rpx;
						color: #333333;
					}
					.two_gd{
						display: flex;
						align-items: center;
						image{
							width: 24rpx;
							height: 24rpx;
						}
						text{
							font-family: Source Han Sans CN, Source Han Sans CN;
							font-weight: 400;
							font-size: 24rpx;
							color: #AAAAAA;
							line-height: 28rpx;
							text-align: center;
							font-style: normal;
							text-transform: none;
						}
					}
				}
			}
			.classify-three{
				display: flex;
				flex-wrap: wrap;
				
				width: 100%;
				.goods-list{
					display: flex;
					flex-wrap: wrap;
					justify-content: space-between;
					width: 100%;
					.goods-list-left{	
						width: 49%;
					}
					.goods-list-right{
						width: 49%;
					}
				}
				.list{
					display: flex;

					flex-direction: column;
					justify-content: center;
					align-items: center;
					width: 32%;
					// height: 140rpx;
					margin-right: 2%;
					image{
						width: 100rpx;
						height: 100rpx;
					}
					text{
						color: #212121;
						font-size: 24rpx;
						margin-top: 10rpx;
						    display: -webkit-box;
						    -webkit-box-orient: vertical;
						    -webkit-line-clamp: 2;
						    overflow: hidden;
					}
				}
				.list:nth-child(3n){
					margin-right: 0;
				}
			}
		}
	}
}
