<template>
	<view class="page">
		<!-- 文章数据 -->
		<mescroll-body ref="mescrollRef" @init="mescrollInit" @down="downCallback" @up="upCallback" :down="downOption"
			:up="upOption" :top="20">
			<view class="goods-list1">
				<view :class="'list-li'" v-for="(item,index) in goodsList"
					@click="$fun.jump(`/pages/discover/discoverDetails?id=${item.id}`)" :key="index">
					<view class="thumb">
						<image :src="$fun.imgUrl(item.logoimage)"></image>
					</view>
					<view class="item">
						<view class="title">
							<text class="two-omit2">{{item.name}}</text>
						</view>
						<view class="title">
							<text class="two-omit1">联系方式:</text>
							<text class="two-omit2">{{item.mobile}}</text>
						</view>
						<view class="title">
							<text class="two-omit1">地区:</text>
							<text class="two-omit2">{{item.address}}</text>
						</view>
					</view>
				</view>
			</view>
		</mescroll-body>
		<!-- tabbar -->
	</view>
</template>

<script>
	import TabBar from '@/components/TabBar.vue';
	// 引入mescroll-mixins.js
	import MescrollMixin from "@/components/mescroll-uni/mescroll-mixins.js";
	export default {
		mixins: [MescrollMixin], // 使用mixin
		components: {
			TabBar,
		},
		data() {
			return {
				mescroll: null, // mescroll实例对象 (此行可删,mixins已默认)
				// 下拉刷新的配置(可选, 绝大部分情况无需配置)
				downOption: {},
				// 上拉加载的配置(可选, 绝大部分情况无需配置)
				upOption: {},
				swiperList: [],
				categoryList: [],
				goodsList: [],
				id: ''
			};
		},
		onLoad(option) {
			this.id = option.id
			uni.setNavigationBarTitle({
				title: option.name
			})
		},
		onReady() {
			uni.hideTabBar();
		},
		methods: {
			/**
			 * 初始化
			 */
			async init() {
				await this.getSwiper()
				await this.getCategory()
			},
			/**
			 * 获取轮播图
			 */
			getSwiper() {
				this.$fun.ajax.post('news/lists', {
					type: 'nearby'
				}).then(res => {
					console.log(res)
					if (res.status == 1) {
						this.swiperList = res.data
					}
				})
			},
			/**
			 * 获取分类
			 */
			getCategory() {
				this.$fun.ajax.post('category/list', {
					type: 'nearby'
				}).then(res => {
					console.log(res)
					if (res.status == 1) {
						this.categoryList = res.data
					}
				})
			},
			/*下拉刷新的回调, 有三种处理方式:*/
			downCallback() {
				this.mescroll.endSuccess();
			},
			/*上拉加载的回调*/
			async upCallback(e) {
				this.mescroll.removeEmpty();
				const data = {
					page: e.num,
					pagenum: e.size,
					cid: this.id
				};
				// await this.getSwiper()
				// await this.getCategory()
				this.$fun.ajax.post('nearby/index', data).then(res => {
					if (res.status == 1) {
						const curList = res.data.data;
						if (e.num === 1) {
							this.goodsList = [];
							//第一页清空数据重载
							if (curList.length > 0) {
								uni.pageScrollTo({
									scrollTop: 0,
									duration: 200
								});
							}
						}
						this.goodsList = this.goodsList.concat(curList); //追加新数据
						this.mescroll.endSuccess(curList.length); //结束加载状态
					}
				})
			},
			/**
			 * 文章点击
			 */
			onArticle() {
				uni.navigateTo({
					url: '/pages/ArticleDetails/ArticleDetails',
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	@import 'discoverList.scss';
</style>