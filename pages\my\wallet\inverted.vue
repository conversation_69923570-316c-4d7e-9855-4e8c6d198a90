<template>
	<view class="address-manage rf-row-wrapper">
		<view class="top">
			<view class="left">
				<view class="title">{{titleName}}</view>
				<view class="num">{{balance}}</view>
			</view>
		</view>
		<view class="content_box">
			<u-form labelPosition="top" :model="model"  ref="form1">
				
					<u-form-item label="划转类型"  prop="mobile" borderBottom ref="item1" @click="showType()" >
						<picker :range="list1"  @change="typeChange">
							<view>{{model.name?model.name:'请选择划转类型'}}</view>
						</picker>
					</u-form-item>
				<u-form-item label="划转金额"  prop="money" borderBottom ref="item1">
					<u-input placeholder="请输入划转金额" :border="true" v-model="model.money"></u-input>
				</u-form-item>
			</u-form>
		</view>
		<view style="height: 100rpx;">
			
		</view>
				<passkeyborad ref="passkeyborad" @configPay="configPay" :show="keyboardShow" @close="onSubmit" :payTitle="titleName" :payMoney="model.money" ></passkeyborad>
		<button class="add-btn"  @tap="confirm">
			提交
		</button>
	</view>
</template>
<script>
	/**
	 * @des 用户账户中心
	 *
	 * @<NAME_EMAIL>
	 * @date 2020-01-10 15:17
	 * @copyright 2019
	 */
	// import { memberInfo, memberUpdate, uploadImage } from '@/api/userInfo';
	import uniCopy from '@/components/xb-copy/uni-copy.js'
	export default {
		data() {
			return {
				balance: '==',
				themeColor: {
					color: '#310FFF'
				},
				model: {
					name:"",
					tid: '',
					money: "", //列表金额
				},
				exchange_arr:[],
				keyboardShow: false,
				list1:[],
				showTypeA:false
			};
		},
		onLoad(options) {
			this.id = options.id
			this.titleName = options.name
			this.initData();
		},
		methods: {
			typeChange(e){
				this.model.name = this.exchange_arr[e.detail.value].name
				this.model.tid = this.exchange_arr[e.detail.value].id
			},
			// 关闭支付框
			onSubmit(){

				this.keyboardShow = !this.keyboardShow
				this.$refs.passkeyborad.clear()
			},
			configPay(pay){
				let {
					tid,
					money,
				} = this.model
				let prams = {
					tid,
					money,
					id:this.id,
					pay
				}
				if (!prams.tid) {
					this.$fun.msg('请选择划转类型');
					return
				}
				if (!prams.money) {
					this.$fun.msg('请输入划转金额');
					return
				}
				this.$fun.ajax.post('wallet/exchange', {...prams
				}).then(res => {
					this.onSubmit()
					if (res.status == 1) {
						this.$fun.msg(res.msg);
						setTimeout(()=>{
							uni.navigateBack({
								
							})
						},1200)
					}
				})
			},
			lookImg(item){
				uni.previewImage({
				            urls: [this.$fun.imgUrl(item)]//预览图片的地址，必须要数组形式，如果不是数组形式就转换成数组形式就可以
				          })
			},
			copy(text) {
				uniCopy({
					content: text,
					success: (res) => {
						uni.showToast({
							title: res,
							icon: 'none'
						})
					},
					error: (e) => {
						uni.showToast({
							title: e,
							icon: 'none',
							duration: 3000,
						})
					}
				})
			},
			// 初始化数据
			initData() {
				this.getRecharge(this.id);
			},
			checkePrice(item, index) {
				this.money = item;
				this.currentIndex = index
				// //console.log(this.moneyList)
			},
			// 通用跳转
			navTo(route) {
				this.$mRouter.push({
					route
				});
			},
			confirm() {
				let {
					tid,
					money,
				} = this.model
				let prams = {
					tid,
					money
				}
				if (!prams.tid) {
					this.$fun.msg('请选择划转类型');
					return
				}
				if (!prams.money) {
					this.$fun.msg('请输入划转金额');
					return
				}
				this.keyboardShow = true
			},
			// 获取用户信息
			async getRecharge(id) {
				await this.$fun.ajax.post('/wallet/config', {
					id,
					type: 'exchange',
				}).then(res => {
					if (res.status == 1) {
						this.balance = res.data.money
						this.exchange_arr = res.data.exchange_arr
						this.list1=[]
						for(var i=0;i<this.exchange_arr.length;i++){
							this.list1.push(this.exchange_arr[i].name)
						}
					}
				})
			},
			// 上传头像
			uploadImage() {
				// 从相册选择图片
				const _this = this;
				uni.chooseImage({
					count: 1,
					sizeType: ['original', 'compressed'],
					sourceType: ['album'],
					success: function(res) {
						_this.handleUploadFile(res.tempFilePaths);
					}
				});
			},
			// 上传头像
			handleUploadFile(data) {
				const _this = this;
				const filePath = data.path || data[0];
				this.$http.uploadPic(
						filePath
				).then(res => {
					this.$fun.msg(res.msg)
					if (res.status == 1) {
								_this.image = res.data.url;
					}
				})
			},
			navTo(route) {
				this.$mRouter.push({
					route
				});
			}
		},
		onNavigationBarButtonTap(e) {
			// #ifdef APP-PLUS
			// eslint-disable-next-line
			const pages = getCurrentPages();
			const page = pages[pages.length - 1];
			const currentWebview = page.$getAppWebview();
			currentWebview.hideTitleNViewButtonRedDot({
				index
			});
			// #endif
			this.navTo(`/pages/user/account/listAll?id=${this.id}&name=充值记录`);
		},
	};
</script>
<style lang="scss">
	page {
		background: #FFFFFF;
	}
	.content_box{
		padding:30rpx
	}
	.my-account {
		background-color: #FFFFFF;
		/*  #ifndef H5  */
		height: 100vh;
		/*  #endif  */
		padding: 32upx 20upx;
		width: 100%;

		.header {
			padding: 30upx;
			height: 200upx;
			display: flex;
			align-items: center;
			opacity: 0.9;
			border-radius: 20upx;
			color: rgba(255, 255, 255, 0.6);
			font-size: 24rpx;
			position: relative;

			.account {
				width: calc(100% - 60upx);
				display: flex;
				position: absolute;
				z-index: 2;
				justify-content: space-between;

				.assets {
					.money {
						color: #fff;
						font-size: 30upx;
						margin: 0;
					}
				}

				.recharge {
					font-size: 28rpx;
					width: 150upx;
					height: 54upx;
					line-height: 54upx;
					border-radius: 28rpx;
					background-color: #fff9f8;
					text-align: center;
					margin-top: 10upx;
				}
			}

			.cumulative {
				width: calc(100% - 240upx);
				position: absolute;
				bottom: 20upx;
				display: flex;
				justify-content: space-between;

				.money {
					color: #fff;
					font-size: 36rpx;
					margin: 0;
				}
			}

			.header-bg {
				position: absolute;
				width: 100%;
				height: 320upx;
				z-index: 1;
				top: 0;

				image {
					width: 100%;
					height: 320upx;
				}
			}
		}

		.nav {
			border-bottom: 1px solid #f5f5f5;
			display: flex;

			.item {
				flex: 1;
				margin: 20upx;
				font-size: 26rpx;
				display: inline-block;
				text-align: center;
				color: #999;

				.iconfont {
					display: block;
					margin: 0 auto;
					font-size: 40rpx;
				}
			}
		}

		.advert {
			display: flex;

			.item {
				flex: 1;
				border-radius: 24upx;
				padding: 10upx 0;
				margin: 20upx 10upx;
				display: flex;
				justify-content: space-between;

				.iconfont {
					font-size: 40upx;
					margin-right: 20upx;
				}

				.text {
					margin-left: 20upx;

					.name {
						font-size: 28rpx;
						font-weight: bold;
						height: 40upx;
					}

					.desc {
						font-size: 24rpx;
					}
				}
			}

			.on {
				background-color: #fff3f3;
			}
		}
	}

	/* 内容区 */
	.conter {
		.title {
			color: #666;
			margin-top: 50rpx;
			font-size: 30rpx;
		}

		.cent {
			margin-top: 25rpx;
			overflow: hidden;

			.list {
				width: 200rpx;
				height: 120rpx;
				border: 1rpx solid #e8e8e8;
				float: left;
				margin: 0 18rpx;
				margin-bottom: 45rpx;
				position: relative;
				text-align: center;
				line-height: 120rpx;
				border-radius: 15rpx;

				// &:nth-child(3n) {
				// 	margin-right: 0;
				// }

				input {
					height: 50rpx;
					margin-left: 10rpx;
					margin-top: 35rpx;
				}

				.van-cell {
					padding: 0;
					width: 180rpx;
				}

				image {
					width: 40rpx;
					height: 37rpx;
					position: absolute;
					bottom: 0;
					right: 0;
					display: none;
				}

				&.active {
					color: #dd1021;
					border: 1rpx solid #dd1021;

					image {
						display: block;
					}
				}
			}
		}
	}

	.top {
		display: flex;
		justify-content: space-between;
		margin-left: 20rpx;

		.left {
			margin-top: 40rpx;

			.title {
				font-size: 24rpx;
				color: #666;
			}

			.num {
				font-size: 48rpx;
				color: #000;
				margin-top: 30rpx;
				font-weight: bold;
			}

			.jyjl {
				font-size: 24rpx;
				color: #dd1021;
				margin-top: 50rpx;
			}
		}

		.rightimg {
			width: 233rpx;
			height: 240rpx;
			margin-right: 50rpx;
		}
	}
</style>
