<template>
	<view class="page">
		<!-- 文章数据 -->
		<mescroll-body ref="mescrollRef" @init="mescrollInit"  @down="downCallback" @up="upCallback" :down="downOption" :up="upOption"
			:top="0">
			<view class="menu-nav" v-if="categoryList.length>0">
				<scroll-view scroll-x @scroll="ScrollMenu" class="nav-list">
					<view class="nav" ref="nav" :style="categoryList.length<=10?'flex-direction:row':''">
						<view class="list" v-for="(item,index) in categoryList" @click="$fun.jump(`/pages/home/<USER>/unionList?id=${item.id}&name=${item.name}`)" :key="item.id">
							<image :src="$fun.imgUrl(item.image)" mode=""></image>
							<text>{{item.name}}</text>
						</view>
					</view>
				</scroll-view>
				<view class="indicator" v-if="categoryList.length>10">
					<view class="plan">
						<view class="bar" :style="'left:'+slideNum+'%'"></view>
					</view>
				</view>
			</view>
			<view class="goods-list">
				<view :class="'list-li'" v-for="(item,index) in goodsList" @click="$fun.jump(`/pages/home/<USER>/unionDetails?id=${item.id}`)"
					:key="index">
					<view class="thumb" style="padding-left: 15rpx;">
						<image :src="$fun.imgUrl(item.logoimage)"></image>
					</view>
					<view class="item">
						<view class="title">
						 {{item.name}} 
						</view>
						<view class="title info">
							经营范围：{{item.title}}
						</view>
						<view class="title website">
							 公司网址：{{item.url}}
						</view>
					</view>
				</view>
			</view>
		</mescroll-body>
	</view>
</template>

<script>
	import TabBar from '@/components/TabBar/TabBar.vue';
	// 引入mescroll-mixins.js
	import MescrollMixin from "@/components/mescroll-uni/mescroll-mixins.js";
	export default {
		mixins: [MescrollMixin], // 使用mixin
		components: {
			TabBar,
		},
		data() {
			return {
				mescroll: null, // mescroll实例对象 (此行可删,mixins已默认)
				// 下拉刷新的配置(可选, 绝大部分情况无需配置)
				downOption: {},
				// 上拉加载的配置(可选, 绝大部分情况无需配置)
				upOption: {},
				swiperList: [],
				categoryList:[],
				goodsList:[]
			};
		},
		onLoad(option) {
			uni.setNavigationBarTitle({
				title:option.name
			})
		},
		onReady() {
			uni.hideTabBar();
		},
		methods: {
			/**
			 * 初始化
			 */
			async init(){
				await this.getCategory()
			},
			/**
			 * 获取分类
			 */
			getCategory(){
				this.$fun.ajax.post('category/list', {type:'union'}).then(res => {
					console.log(res)
					if(res.status==1){
						this.categoryList = res.data
					}
				})
			},
			/*下拉刷新的回调, 有三种处理方式:*/
			downCallback() {
				this.mescroll.endSuccess();
			},
			/*上拉加载的回调*/
			async upCallback(e) {
				this.mescroll.removeEmpty();
				const data = {
					page: e.num,
					pagenum: e.size,
					cid:'all'
				};
				await this.getCategory()
				this.$fun.ajax.post('Union/lists', data).then(res => {
					if (res.status == 1) {
						const curList = res.data.data;
						if (e.num === 1) {
							this.goodsList = [];
							//第一页清空数据重载
							if (curList.length > 0) {
								uni.pageScrollTo({
									scrollTop: 0,
									duration: 200
								});
							}
						}
						this.goodsList = this.goodsList.concat(curList); //追加新数据
						this.mescroll.endSuccess(curList.length); //结束加载状态
					}
				})
			},
			/**
			 * 文章点击
			 */
			onArticle() {
				uni.navigateTo({
					url: '/pages/ArticleDetails/ArticleDetails',
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	@import 'union.scss';
</style>
