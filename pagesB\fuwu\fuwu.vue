<template>
	<view>
		<u-tabs :list="tabList" count="count" active-color="#310FFF" :current="current" @change="tabChange"></u-tabs>
		<!-- <view class="add_customer">
			<text>添加客户</text>
		</view> -->
		<view style="height: 20rpx;">

		</view>
		<view class="wallet-list u-flex" v-for="item in customerList" :key="item.id"
			@click="$fun.jump(`./fuwu_user?id=${item.id}`)">
			<image class="head-img u-m-r-20" :src="$fun.imgUrl(item.avatar)" mode=""></image>
			<view class="list-content">
				<view class="title-box u-flex u-row-between">
					<text class="title u-ellipsis-1">{{ item.username }}</text>
					<view class="money">
						<text class="add font-OPPOSANS"
							style="color: #310FFF;display: flex;justify-content: flex-end;">{{item.status | getStatus}}</text>
					</view>
				</view>
				<view style="display: flex;justify-content: space-between;margin-top: 20rpx;">
					<text class="time" style="font-size: 25rpx;">{{item.mobile}}</text>
					<text class="time"></text>
				</view>
				<view style="display: flex;justify-content: space-between;margin-top: 10rpx;">
					<text class="time"></text>
					<text class="time">{{ $u.timeFormat(item.createtime, 'yyyy-mm-dd hh:MM') }}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				customerList: [],
				page: 1,
				current: 0,
				tabList: [{
						name: '待创建',
						status: 0,
						count: 0
					},
					{
						name: '审核中',
						status: 1,
						count: 0
					},
					{
						name: '待打款',
						status: 2,
						count: 0
					},
					{
						name: '审核失败',
						status: 3,
						count: 0
					},
					{
						name: '已打款',
						status: 4,
						count: 0
					},
					{
						name: '康复中',
						status: 5,
						count: 0
					},
					{
						name: '康复结束',
						status: 6,
						count: 0
					},

				]
			}
		},
		filters: {
			getStatus(val) {
				return val == 0 ? '待创建' : val == 1 ? '审核中' : val == 2 ? '待打款' : val == 3 ? '审核失败' : val == 4 ? '已打款' :
					val == 5 ? '康复中' : val == 6 ? '康复结束' : ''
				console.log(val)
			}
		},
		onShow() {
			this.customerList = [];
			this.page = 1;
			this.getCustomerList();
		},
		onReachBottom() {
			if (this.page == 1) {
				this.customerList = []
				return
			}
			this.getCustomerList()
		},
		methods: {
			tabChange(e) {
				this.current = e;
				this.customerList = [];
				this.page = 1;
				this.getCustomerList();
			},
			getCustomerList() {

				this.$fun.ajax.post(`/manage/icon`, {}).then(res => {
					if (res.status == 1) {
						for (var key in res.data) {
							for (var key1 in this.tabList) {
								if (this.tabList[key1]['status'] == key) {
									this.tabList[key1]['count'] = res.data[key]
								}
							}
						}
					}
				})
				this.$fun.ajax.post(`/manage/index`, {
					page: this.page,
					status: this.current
				}).then(res => {
					if (res.status == 1) {
						this.customerList = this.customerList.concat(res.data)
					}
				})
			}
		}
	}
</script>

<style lang="scss">
	.add_customer {
		display: flex;
		justify-content: flex-end;
		padding: 30rpx;

		text {
			background: #310FFF;
			color: #FFFFFF;
			padding: 5rpx 20rpx;
			border-radius: 20rpx;
		}
	}









	.u-flex {
		display: flex;
	}

	.nav-box {
		justify-content: space-around;
		// background: #ffffff
	}

	// 钱包记录
	.wallet-list {
		padding: 20rpx 30rpx;
		background-color: #ffff;
		margin: 0 30rpx;
		border-radius: 8rpx 8rpx 8rpx 8rpx;
		flex: 1;
		border-bottom: 1rpx solid #eeeeee;
		box-sizing: border-box;
		overflow: hidden;

		.head-img {
			width: 70rpx;
			height: 70rpx;
			border-radius: 50%;
			background: #ccc;
		}

		.list-content {
			justify-content: space-between;
			align-items: flex-start;
			background: #FFFFFF;

			width: calc(100% - 100rpx);

			.title {
				font-size: 28rpx;
				color: #333;
				width: 500rpx;
			}

			.time {
				color: #c0c0c0;
				font-size: 22rpx;
			}
		}

		.money {
			width: calc(100% - 380rpx);
			font-size: 28rpx;
			font-weight: bold;
			box-sizing: border-box;
			// overflow: hidden;

			.add {
				display: inherit;
				width: 100%;
				color: #008467;
				overflow: hidden;
				text-overflow: ellipsis;
				display: -webkit-box;
				-webkit-line-clamp: 2;

			}

			.minus {
				display: inherit;
				width: 100%;
				color: #008467;
				overflow: hidden;
				text-overflow: ellipsis;
				display: -webkit-box;
				-webkit-line-clamp: 2;

			}
		}
	}

	// 钱包
	.head_box {
		width: 750rpx;
		// background-color: #fff;
		padding: 30rpx 0;

		.card-box {
			width: 690rpx;
			max-height: 291rpx;
			padding: 40rpx;
			// background: url('/static/wallet-bg.jpeg') no-repeat;
			// background-size: 100% 100%;
			background-color: #310FFF;
			box-shadow: 1rpx 5rpx 12rpx 0 #949494;
			border-radius: 30rpx;
			overflow: hidden;
			position: relative;
			display: flex;

			.l {
				width: 50%;

				.card-head {
					color: #fff;
					font-size: 30rpx;
				}

				.money-num {
					font-size: 70rpx;
					line-height: 70rpx;
					font-weight: 500;
					color: #ffffff;
				}

				.reduce-num {
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 500;
					font-size: 48rpx;
					color: #FFFFFF;
				}
			}


			.withdraw-btn {
				width: 120rpx;
				height: 60rpx;
				line-height: 60rpx;
				background: #ffffff;
				border-radius: 30px;
				font-size: 24rpx;
				font-weight: 500;
				color: #310FFF;

				// position: absolute;
				// right: 30rpx;
				// top: 40rpx;
			}
		}
	}

	// 筛选

	.filter-box {
		padding: 30rpx;
		line-height: 54rpx;
		background-color: #f6f6f6;

		.date-btn {
			background-color: #fff;
			line-height: 54rpx;
			border-radius: 27rpx;
			padding: 0 20rpx;
			font-size: 24rpx;
			font-weight: 500;
			color: #666666;
			margin-left: 0
		}

		.total-box {
			font-size: 24rpx;
			font-weight: 500;
			color: #999999;
		}
	}

	// 分类
	.state-item {
		height: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		background-color: #fff;
		border-bottom: 1rpx solid transparent;

		.state-title {
			color: #666;
			font-weight: 500;
			font-size: 28rpx;
			line-height: 90rpx;
		}

		.title-active {
			color: #333;
		}

		.underline {
			display: block;
			width: 68rpx;
			height: 4rpx;
			background: #fff;
			border-radius: 2rpx;
		}

		.underline-active {
			background: #310FFF;
			display: block;
			width: 68rpx;
			height: 4rpx;
			border-radius: 2rpx;
		}
	}
</style>