<template>
	<view class="page">
		<view v-html="vHtml">

		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				isWeixin: 1,
				oid: null,
				gid: null,
				isJump: null,
				vHtml: ''
			};
		},
		onLoad(option) {
			// this.CountDownData();
			this.oid = option.oid
			this.gid = option.gid
			this.isJump = option.isJump
			this.isWeixin = option.isWeixin
		},
		onShow() {
			// #ifdef H5
			let prams = {
				oid: this.oid,
				type: 'alipay'
			}
			this.$fun.ajax.post('order/pay', prams).then(res => {
				if (res.status == 1) {
					this.vHtml = res.data
				}
			})
			// #endif
		},
		methods: {

		}
	}
</script>

<style scoped lang="scss">
	.page {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: #f6f6f6;
	}
</style>