<template>
	<view>
		<view class="content_box">
			<u-form labelPosition="top">
				<u-form-item label="手机号" prop="name" borderBottom ref="item1">
					<u-input placeholder="请输入手机号" :border="true" v-model="params.mobile"></u-input>
				</u-form-item>
				<!-- <u-form-item label="验证码" prop="bankcode" borderBottom ref="item1">
			<u-input placeholder="请输入验证码" :border="true" v-model="params.captcha"></u-input>
			<view class="sendCode btn-color" v-if="showTime">{{time}}s后重新获取</view>
				<view class="sendCode btn-color" @click="getCode()" hover-class="text-hover" v-else>获取验证码</view>
			
		</u-form-item> -->
			</u-form>
			<view style="height: 60rpx;">

			</view>
			<u-button type="error" mode="plain" shape="circle" class="u-m-t-40" @click="confirmClick()">确 定
			</u-button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				params: {
					mobile: '',
					captcha: ''
				},
				timer: null,
				isClick: true, //避免用户重复点击
				time: 60, //60s后重置
				showTime: false, //时间与获取验证码切换
			}
		},
		methods: {
			confirmClick(code) {
				if (!this.params.mobile) {
					this.$fun.msg('请填写要绑定的手机号码!');
					return false;
				}
				// if (!this.params.captcha) {
				// 	this.$fun.msg('请输入验证码!');
				// 	return false;
				// }
				let prams = {
					mobile: this.params.mobile,
					// captcha: this.params.captcha,
				}
				this.$fun.ajax.post('user/changemobile', {
					...prams
				}).then(res => {
					this.$fun.msg(res.msg);
					if (res.status == 1) {
						setTimeout(() => {
							uni.navigateBack({

							})
						}, 1500)
					}
				})
			},
			// 获取验证码
			getCode() {
				if (this.isClick) {
					if (this.params.mobile.length != 11) {
						return this.$fun.msg('手机格式不正确')
					}
					// this.$loading()
					this.$fun.ajax.post('sms/send', {
						mobile: this.params.mobile,
						event: 'changemobile'
					}).then(res => {
						if (res.status == 1) {
							this.showTime = true;
							this.$fun.msg(res.msg)
							this.timer = setInterval(() => {
								this.isClick = false;
								this.time = this.time - 1;
								if (this.time <= 0) {
									this.isClick = true;
									this.time = 60;
									this.showTime = false;
									clearInterval(this.timer);
									this.timer = null;
								}
							}, 1000)
						}

					})
				}
			},
		},
		// 页面卸载
		onUnload() {
			this.isClick = true;
			this.time = 60;
			// 清空定时器
			clearInterval(this.timer);
			this.timer = null;
		},
	}
</script>

<style>
	page {
		background: #FFFFFF;
	}

	.content_box {
		padding: 30rpx;
		background: #FFFFFF;
	}
</style>