<template>
	<view class="caht_d">
		<view class="group_box">
			<block v-for="(item,index) in chatInfo.users" :key="index">
				<view class="avatar_box" v-if="index<19"
					@click="$fun.jump(`./add_friend/add_confirm?account=${item.id}&type=1`)">
					<view class="avatar">
						<u-avatar :src="$fun.imgUrl(item.avatar)"></u-avatar>
						<view class="username">
							{{item.username}}
						</view>
					</view>
				</view>

			</block>
			<view class="avatar_box" @click="$fun.jump(`./group/creat_group?id=${currentChatGroupId}`)" v-if="20>19">
				<view class="avatar b">
					<u-icon name="plus" color="#C4C4C4" size="50"></u-icon>
				</view>
			</view>
		</view>
		<view style="width: 100%; height: 30rpx;background: #f6f6f6;">

		</view>
		<u-cell-group v-if="type==1">
			<u-cell-item title="群聊名称" :value="chatInfo.username"
				@click="$fun.jump(`/pagesB/chat/add_friend/add_confirm1?type=4&id=${chatInfo.id}&frommemo=${chatInfo.username}`)"></u-cell-item>
			<!-- <u-cell-item title="消息免打扰" :arrow="false">
				<u-switch active-color="#310FFF" slot="right-icon" v-model="checked"></u-switch>
			</u-cell-item>
			<u-cell-item title="置顶聊天" :arrow="false">
				<u-switch active-color="#310FFF" slot="right-icon" v-model="checked"></u-switch>
			</u-cell-item>
			<u-cell-item title="消息免打扰" :arrow="false">
				<u-switch active-color="#310FFF" slot="right-icon" v-model="checked"></u-switch>
			</u-cell-item> -->
		</u-cell-group>
		<u-cell-group v-if="type==0">
			<u-cell-item title="备注"
				@click="$fun.jump(`/pagesB/chat/add_friend/add_confirm1?type=3&id=${chatInfo.id}&frommemo=${chatInfo.username}`)"
				:value="chatInfo.username"></u-cell-item>
		</u-cell-group>
		<view style="width: 100%; height: 30rpx;background: #f6f6f6;">

		</view>
		<!-- 		<u-cell-group>
			<u-cell-item title="查找聊天内容"></u-cell-item>
		</u-cell-group>
		<view style="width: 100%; height: 30rpx;background: #f6f6f6;">

		</view>
		<u-cell-group :border-bottom="false">
			<u-cell-item title="查找聊天内容"></u-cell-item>
		</u-cell-group> -->

	</view>
</template>

<script>
	export default {
		data() {
			return {
				keyword: '',
				checked: false,
				list: [],
				chatInfo: {},
				groupList: [],
				type: 0,
				currentChatGroupId: null
			};
		},
		onLoad(option) {
			this.type = option.type;
			this.currentChatGroupId = option.id;
		},
		onShow() {
			this.getList(this.currentChatGroupId);
		},
		methods: {
			getList(id) {
				this.$fun.ajax.post(`chat/getChatInfo`, {
					id
				}).then(res => {
					if (res.status == 1) {
						this.chatInfo = res.data
					}
				})
			},
			checkboxGroupChange(e) {
				console.log(e)
				this.groupList = [];
				for (let i = 0; i < e.length; i++) {
					this.groupList.push({
						title: '样式' + i,
						msg: '样式2样式2样式2样式2样式2样式2样式2样式2样式2样式2样式2样式2样式2样式2',
						isTop: true,
						icon: 0,
						isOff: false,
						time: '11:31',
						checked: false,
						disabled: false
					});
				}
				document.querySelector('.uni-page-head-ft .uni-page-head-btn .uni-btn-icon').innerHTML = `完成(${e.length})`;

			}
		}
	}
</script>

<style lang="scss">
	page {
		background: #FFFFFF;
		padding: 32rpx 0;

		.caht_d {
			width: 100vw;
			display: flex;
			flex-wrap: wrap;
			align-items: center;
			box-sizing: border-box;

			.group_box {
				padding: 0 32rpx;
				width: 100%;
				display: flex;
				flex-wrap: wrap;

				.avatar_box {
					margin-bottom: 20rpx;
					width: 20%;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;

					.avatar {
						width: 90rpx;
						font-style: normal;
						text-transform: none;

						.username {
							width: 90rpx;
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
						}
					}

					.b {
						display: flex;
						justify-content: center;
						align-items: center;
						width: 100rpx;
						height: 100rpx;
						border: 1px dashed #C4C4C4;
					}

					.username {
						margin-top: 5rpx;
						font-family: Source Han Sans CN, Source Han Sans CN;
						font-weight: 400;
						font-size: 25rpx;
						color: #666666;
						line-height: 33rpx;
						text-align: center;
						font-style: normal;
						text-transform: none;
					}
				}

			}
		}

		.group_list {
			padding: 0 32rpx;
			box-sizing: border-box;
		}
	}
</style>