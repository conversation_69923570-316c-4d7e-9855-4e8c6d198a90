<template>
	<view class="page">
		<!-- #ifdef H5 -->
		<view class="head-back">
			<view class="back" @click="$fun.jump('',6)">
				<text></text>
			</view>
			<view class="title">
				<text>我的订单</text>
			</view>
			<view class="more-icon">
				<view class="icon-list">
					<!-- <text class="iconfont icon-fadajing"></text> -->
				</view>
			</view>
		</view>
		<!-- #endif -->
		<!-- 订单tab -->
		<view class="order-tab" style="display: flex;justify-content: space-between;">
			<!-- 	<view class="tab" :class="{'action':OrderType=='all'}" @click="onOrderTab('all')">
				<text>全部</text>
				<text class="line"></text>
			</view> -->
			<view class="tab" :class="{'action':OrderType==1}" @click="onOrderTab(1)">
				<text>待发货</text>
				<text class="line"></text>
			</view>
			<view class="tab" :class="{'action':OrderType==2}" @click="onOrderTab(2)">
				<text>待收货</text>
				<text class="line"></text>
			</view>
			<view class="tab" :class="{'action':OrderType==3}" @click="onOrderTab(3)">
				<text>已完成</text>
				<text class="line"></text>
			</view>
		</view>
		<mescroll-body ref="mescrollRef" @init="mescrollInit" @down="downCallback" @up="upCallback" :down="downOption"
			:up="upOption" :top="0">
			<!-- 订单列表 -->
			<view class="order-list">
				<view class="list" v-for="(item,index) in cartList" :key="index">
					<view class="title-status">
						<view class="title">
							<text>下单时间：{{$u.timeFormat(item.createtime, 'yyyy-mm-dd hh:MM')}}</text>
						</view>
						<view class="status">
							<text>{{getstatusTip(item)}}</text>
							<!-- <text class="iconfont icon-laji del"></text> -->
						</view>
					</view>
					<view class="goods-list" v-for="(item1,index1) in item.goods" :key="index1">
						<view class="goods">
							<view class="thumb">
								<image :src="$fun.imgUrl(item1.image)" mode=""></image>
							</view>
							<view class="item">
								<view class="goods-name">
									<text class="two-omit">{{item1.name}}</text>
									<text class="two-omit" style="color: gray;font-size: 26rpx;">{{item1.sign}}</text>
									<view class="goods-price"
										style="display: flex;align-items: center;width: auto;justify-content: flex-start;">
										<view class="">
											<text class="min">易货额</text>
											<text class="max">{{item.money*1}}</text>
										</view>
										<view class="max" v-if="item.dikou!=0" style="margin-left: 10rpx;">
											+
											{{item.dikou}}
										</view>
									</view>
								</view>

							</view>
						</view>
					</view>
					<view class="status-btn" v-if="item.status ==0&&item.pin==0">
						<view class="btn action" @click.stop="$fun.jump(`./orderDetails?order_num=${item.id}`)">
							<text>详情</text>
						</view>
					</view>
					<view class="status-btn" v-else-if="item.status ==2">
						<view class="btn action" v-if="item.status !=0&&item.pin==1" @click.stop="onEvaluate">
							<text>拼团</text>
						</view>
						<view class="btn action" v-else @click.stop="$fun.jump(`./orderDetails?order_num=${item.id}`)">
							<text>详情</text>
						</view>
					</view>
					<view class="status-btn" v-else>
						<view class="btn action" @click.stop="$fun.jump(`./orderDetails?order_num=${item.id}`)">
							<text>详情</text>
						</view>
					</view>
				</view>
			</view>

		</mescroll-body>
		<!-- 提示框 -->
		<DialogBox ref="DialogBox"></DialogBox>
	</view>
</template>

<script>
	// 引入mescroll-mixins.js
	import MescrollMixin from "@/components/mescroll-uni/mescroll-mixins.js";
	export default {
		mixins: [MescrollMixin], // 使用mixin
		data() {
			return {
				mescroll: null, // mescroll实例对象 (此行可删,mixins已默认)
				// 下拉刷新的配置(可选, 绝大部分情况无需配置)
				downOption: {},
				// 上拉加载的配置(可选, 绝大部分情况无需配置)
				upOption: {},
				cartList: [],
				OrderType: 'all',
			}
		},
		onLoad(option) {
			this.OrderType = option.type
		},
		methods: {
			onOrderTab(type) {
				this.OrderType = type
				this.downCallback()
			},
			getstatusTip(item) {
				let status = item.status
				let tip = ""
				if (item.pin == 1) {
					status = item.pinstatus
					tip = status == 1 ? '拼团成功' : status == 2 ? '拼团失败' : '拼团中'
				} else {
					tip = status == 0 ? '待支付' : status == 1 ? '待发货' : status == 2 ? '待收货' : status == 3 ? '已完成' : status ==
						4 ?
						'已取消' : ''
				}

				return tip
			},
			/*下拉刷新的回调, 有三种处理方式:*/
			downCallback() {
				this.cartList = []
				this.mescroll.resetUpScroll(false);
			},
			/*上拉加载的回调*/
			upCallback(e) {
				this.mescroll.removeEmpty();
				const data = {
					page: e.num,
					limit: e.size,
					type: this.OrderType
				};
				this.$fun.ajax.post('business/order', data).then(res => {
					if (res.status == 1) {
						const curList = res.data.data;
						if (e.num === 1) {
							this.cartList = [];
							//第一页清空数据重载
							if (curList.length > 0) {
								uni.pageScrollTo({
									scrollTop: 0,
									duration: 200
								});
							}
						}
						this.cartList = this.cartList.concat(curList); //追加新数据
						this.mescroll.endSuccess(curList.length); //结束加载状态
						console.log(curList.length)
					}
				})
			},
			/**
			 * 确认收货
			 */
			getqrshouhuo(oid) {
				uni.showModal({
					content: '确认收到货了吗?',
					success: e => {
						if (e.confirm) {
							this.$fun.ajax.post('order/received', {
								oid
							}).then(res => {
								if (res.status == 1) {
									this.$fun.msg(res.msg);
									this.list = [];
									this.mescroll.resetUpScroll();
								}
							})
						}
					}
				});
			},
			/**
			 * 取消订单
			 */
			cancellation(oid) {
				console.log(oid)
				// uni.showModal({

				// })
				uni.showModal({
					content: '确认取消订单吗?',
					success: e => {
						if (e.confirm) {
							this.$fun.ajax.post('order/delOrder', {
								oid
							}).then(res => {
								if (res.status == 1) {
									this.$fun.msg(res.msg);
									this.cartList = [];
									this.mescroll.resetUpScroll();
								}
							})
						}
					}
				})
			},
		}
	}
</script>

<style scoped lang="scss">
	.page {
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		background-color: #f6f6f6;
	}

	/* 顶部返回 */
	.head-back {
		position: fixed;
		left: 0;
		top: 0;
		z-index: 10;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 100rpx;
		background-color: #FFFFFF;
		/* #ifdef APP-PLUS */
		height: calc(50rpx + var(--status-bar-height));
		padding-top: var(--status-bar-height);
		/* #endif */
		/* #ifdef MP */
		height: 150rpx;
		padding-top: 20rpx;

		/* #endif */
		.back {
			position: absolute;
			left: 0;
			top: 0;
			/* #ifdef APP-PLUS */
			padding-top: var(--status-bar-height);
			/* #endif */
			/* #ifdef MP */
			padding-top: 20rpx;
			/* #endif */
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100rpx;
			height: 100%;

			text {
				width: 20rpx;
				height: 20rpx;
				border-left: 2rpx solid #555555;
				border-bottom: 2rpx solid #555555;
				transform: rotate(45deg);
			}
		}

		.title {
			display: flex;
			align-items: center;

			text {
				font-size: 28rpx;
				color: #222222;
			}
		}

		.more-icon {
			position: absolute;
			right: 0;
			top: 0;
			/* #ifdef APP-PLUS */
			right: 0rpx;
			padding-top: var(--status-bar-height);
			/* #endif */
			/* #ifdef MP */
			right: 220rpx;
			padding-top: 20rpx;
			/* #endif */
			display: flex;
			align-items: center;
			height: 100%;

			.icon-list {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 80rpx;
				height: 100%;

				text {
					font-size: 34rpx;
					color: #222222;
				}
			}
		}
	}

	/* 订单tab */
	.order-tab {
		position: fixed;
		left: 0;
		top: 0rpx;
		/* #ifdef APP-PLUS */
		top: 0;
		/* #endif */
		z-index: 10;
		display: flex;
		align-items: center;
		width: 100%;
		height: 100rpx;
		background-color: #FFFFFF;

		.tab {
			position: relative;
			display: flex;
			align-items: center;
			justify-content: center;
			width: 20%;
			height: 80%;

			text {
				font-size: 26rpx;
				color: #959595;
			}
		}

		.action {
			text {
				color: #222222;
			}

			.line {
				position: absolute;
				left: 50%;
				bottom: 0;
				width: 60rpx;
				height: 6rpx;
				background: linear-gradient(to right, $base, #f6f6f6);
				transform: translate(-50%, 0);
			}
		}
	}

	/* 订单列表 */
	.order-list {
		width: 100%;
		margin-top: 70rpx;
		/* #ifdef APP-PLUS */
		margin-top: calc(70rpx + var(--status-bar-height));

		/* #endif */
		.list {
			padding: 0 4%;
			min-height: 400rpx;
			background-color: #FFFFFF;
			border-radius: 20rpx;
			margin-bottom: 20rpx;

			.title-status {
				display: flex;
				align-items: center;
				justify-content: space-between;
				width: 100%;
				height: 100rpx;

				.title {
					display: flex;
					align-items: center;

					text {
						font-size: 26rpx;
						font-weight: bold;
						color: #222222;
					}
				}

				.status {
					display: flex;
					align-items: center;

					text {
						font-size: 26rpx;
						color: $base;
					}

					.del {
						padding: 10rpx;
						font-size: 34rpx;
						color: #222222;
						background-color: #f6f6f6;
						border-radius: 100%;
						margin-left: 20rpx;
					}
				}
			}

			.goods-list {
				width: 100%;

				.goods {
					display: flex;
					align-items: center;
					width: 100%;
					height: 200rpx;

					.thumb {
						display: flex;
						align-items: center;
						width: 30%;
						height: 100%;

						image {
							width: 160rpx;
							height: 160rpx;
							border-radius: 10rpx;
						}
					}

					.item {
						display: flex;
						align-items: center;
						width: 70%;
						height: 100%;

						.goods-name {
							width: 70%;

							text {
								font-size: 26rpx;
								color: #555555;
							}
						}

						.goods-price {
							display: flex;
							align-items: center;
							justify-content: flex-end;
							width: 30%;

							text {
								color: #222222;
							}

							.min {
								font-size: 26rpx;
							}

							.max {
								font-size: 34rpx;
							}
						}
					}
				}
			}

			.status-btn {
				display: flex;
				align-items: center;
				justify-content: flex-end;
				width: 100%;
				height: 100rpx;

				.btn {
					padding: 10rpx 30rpx;
					border: 2rpx solid #EEEEEE;
					border-radius: 100rpx;
					margin-left: 20rpx;

					text {
						font-size: 26rpx;
						color: #555555;
					}
				}

				.action {
					border: 2rpx solid $base;

					text {
						color: $base;
					}
				}
			}
		}
	}
</style>