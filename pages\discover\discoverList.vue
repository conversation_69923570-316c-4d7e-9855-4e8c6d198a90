<template>
	<view class="page">
		<!-- 文章数据 -->
		<mescroll-body ref="mescrollRef" @init="mescrollInit" @down="downCallback" @up="upCallback" :down="downOption"
			:up="upOption" :top="0">
			<view style="padding: 30rpx;">
				<u-search placeholder="请输入你要搜索的商家" bg-color="#FFFFFF" v-model="search" @change="searchStore" :show-action="false"></u-search>
			</view>
			<view class="goods-list">
				<view :class="'list-li'" v-for="(item,index) in goodsList"
					@click="item.types==1?$fun.jump(`/pages/discover/discoverDetails?id=${item.id}`):$fun.jump(`/pages/classify/classify_store?id=${item.id}&name=${item.buname}`)"
					:key="index">
					<view class="thumb" style="padding-left: 15rpx;">
						<image :src="$fun.imgUrl(item.logo)"></image>
					</view>
					<view class="item">
						<view class="title">
							<text class="two-omit">{{item.buname}}</text>
							<text class="info">{{item.content}}</text>
						</view>
						<view class="price" style="color: gray;font-size: 26rpx;">
							<view class="vip-price">
								<text class="min">{{item.types==0?'线上店铺':'实体门店'}}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</mescroll-body>
	</view>
</template>

<script>
	import TabBar from '@/components/TabBar/TabBar.vue';
	// 引入mescroll-mixins.js
	import MescrollMixin from "@/components/mescroll-uni/mescroll-mixins.js";
	export default {
		mixins: [MescrollMixin], // 使用mixin
		components: {
			TabBar,
		},
		data() {
			return {
				mescroll: null, // mescroll实例对象 (此行可删,mixins已默认)
				// 下拉刷新的配置(可选, 绝大部分情况无需配置)
				downOption: {},
				// 上拉加载的配置(可选, 绝大部分情况无需配置)
				upOption: {},
				swiperList: [],
				categoryList: [],
				goodsList: [],
				slideNum: 0,
				ListLength: 0,
				bid: null,
				lat: '',
				lng: '',
				search:''
			};
		},
		onLoad(option) {
			if (option.bid) {
				this.bid = option.bid
			}
			if (option.lat) {
				this.lat = option.lat
			}
			if (option.lng) {
				this.lng = option.lng
			}
			uni.setNavigationBarTitle({
				title: option.name ? option.name : '商家列表'
			})
		},
		methods: {
			searchStore(e){
				this.downCallback();
			},
			/*上拉加载的回调*/
			async upCallback(e) {
				this.mescroll.removeEmpty();
				const data = {
					page: e.num,
					pagenum: e.size,
					bid: this.bid,
					lat:this.lat,
					lng:this.lng,
					search:this.search
				};
				this.$fun.ajax.post('business/index', data).then(res => {
					if (res.status == 1) {
						const curList = res.data.data;
						if (e.num === 1) {
							this.goodsList = [];
							//第一页清空数据重载
							if (curList.length > 0) {
								uni.pageScrollTo({
									scrollTop: 0,
									duration: 200
								});
							}
						}
						this.goodsList = this.goodsList.concat(curList); //追加新数据
						this.mescroll.endSuccess(curList.length); //结束加载状态
					}
				})
			},
			/**
			 * 菜单导航滚动
			 */
			ScrollMenu(e) {
				let scrollLeft = e.target.scrollLeft;
				const query = uni.createSelectorQuery().in(this);
				query.select('.nav').boundingClientRect(data => {
					let wid = e.target.scrollWidth - data.width - (data.left * 2 + 5);
					this.slideNum = (scrollLeft / wid * 300) / 2;
				}).exec();
			},
			/**
			 * 分类跳转
			 */
			navListJump(item) {
				console.log(item);
				let keywords = `/pages/tabbar/discover/discoverList?id=${item.id}`
				this.$fun.jump(keywords, 0, 0)
			},
		}
	}
</script>

<style scoped lang="scss">
	@import 'discoverList.scss';
</style>