.page{
	//position: absolute;
	//width: 100%;
	//height: 100%;
	//overflow-x: hidden;
	//overflow-y: auto;
	padding-bottom: 100rpx;
	background-color: #f6f6f6;
}
/* banner */
	.banner{
		margin-top: 20rpx;
		padding: 0 25rpx;
		height: 260rpx;
		margin-bottom: 30rpx;
		// margin: -200rpx auto 20rpx;
		border-radius: 10rpx;
		overflow: hidden;
		.screen-swiper{
			height: 100%;
			min-height: 100% !important;
			image{
				height: 260rpx;
				border-radius: 10rpx;
			}
		}
	}
	/* 菜单导航 */
	.menu-nav{
		position: relative;
		// height: 300rpx;
		margin:30rpx 24rpx;
		background: #ffffff;
		border-radius: 12rpx 12rpx 12rpx 12rpx;
		.nav-list{
			white-space: nowrap; 
			padding: 20rpx 0;
			width: calc(100vw - 48rpx);
			.nav{
				display: inline-block;
				display: flex;
				flex-direction: inherit !important;
				flex-wrap: wrap;
				justify-content: flex-start;
				width: calc(100vw - 48rpx);
				// height: 280rpx;
			}
			.list{
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				width: calc((100vw - 48rpx) / 5);
				height: 130rpx;
				margin-bottom: 20rpx;
				image{
					width: 75rpx;
					height: 75rpx;
					// border-radius: 100%;
				}
				text{
					font-size: 26rpx;
					color: #363636;
					margin-top: 10rpx;
				}
			}
		}
		.indicator{
			position: absolute;
			left: 0;
			bottom: 0;
			display: flex;
			justify-content: center;
			align-items: center;
			width: 100%;
			height: 30rpx;
			.plan{
				position: relative;
				width: 100rpx;
				height: 8rpx;
				border-radius: 8rpx;
				background-color: #e1e1e1;
				.bar{
					position: absolute;
					width: 50%;
					height: 100%;
					border-radius: 6rpx;
					background-color: $base;
				}
			}
		}
	}
.goods-list {
		padding: 0 25rpx;
		border-radius: 20rpx;
		overflow: hidden;
		// 列表
		.list-li {
			display: flex;
			align-items: center;
			width: 100%;
			padding: 30rpx 10rpx;
			background-color: #ffffff;
			margin-bottom: 20rpx;
			border-radius: 12rpx 12rpx 12rpx 12rpx;
			.thumb {
				display: flex;
				align-items: center;
				justify-content: center;
				height: 100%;
				image {
					width: 200rpx;
					height: 200rpx;
					border-radius: 8rpx 8rpx 8rpx 8rpx;
				}
			}
			.item {
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				width: calc(100% - 220rpx);
				height: 200rpx;
				.title {
					padding: 0 20rpx;
					text.two-omit {
						width: 100%;
					}
					text.info {
						margin-top: 8rpx;
						font-weight: 400;
						font-size: 24rpx;
						color: #666666;
						display: -webkit-box;
						-webkit-line-clamp: 2;
						overflow: hidden;
						text-overflow: ellipsis;
						-webkit-box-orient: vertical;
						
					}
				}
				.price {
					padding: 0 20rpx;
					.retail-price {
						display: flex;
						align-items: flex-end;
						width: 100%;
						height: 40rpx;
						.min {
							display: inline-block;
							font-size: 24rpx;
							color: $base;
							font-weight: bold;
							transform: scale(0.7);
						}
						.max {
							font-size: 28rpx;
							color: $base;
							font-weight: bold;
						}
						.tag {
							position: relative;
							background-color: $base;
							border-radius: 4rpx;
							margin-left: 10rpx;
							text {
								display: inline-block;
								color: #ffffff;
								font-size: 24rpx;
								transform: scale(0.7);
							}
						}
						.tag:before {
							position: absolute;
							left: -6rpx;
							top: 0;
							content: '';
							width: 0;
							height: 0;
							border-top: 0rpx solid transparent;
							border-right: 10rpx solid $base;
							border-bottom: 6rpx solid transparent;
						}
					}
					.vip-price {
						width: 124rpx;
						height: 40rpx;
						background: #FFF4E7;
						border-radius: 6rpx 6rpx 6rpx 6rpx;
						display: flex;
						justify-content: center;
						align-items: center;
						font-weight: 400;
						font-size: 20rpx;
						color: #C07D2F;
						// .min {
						// 	display: inline-block;
						// 	font-size: 24rpx;
						// 	color: #212121;
						// }
						// .max {
						// 	font-size: 24rpx;
						// 	color: #212121;
						// }
					}
				}
			}
		}
	}