<template>
	<view class="page">
				<view class="tabList">
			<view :class="type==item.type?'tabItem active':'tabItem'" @click="clickItem(item)"
				v-for="(item,index) in typeList" :key="index">
				{{item.name}}
			</view>
		</view>
		<mescroll-body ref="mescrollRef" @down="downCallback" @up="upCallback" :down="downOption" :up="upOption"
			:top="0">
			<view class="textList" v-if="type=='test'">
				<view class="textItem" v-for="(item,index) in goodsList" :key="index">
					<view class="text" :style="{ webkitLineClamp:item.num}">
						{{item.text}}
					</view>
					<view>
						<image :src="$fun.imgUrl(item.image)" @click="$fun.lookImg(item.image)"
							style="width: 100%;border-radius: 10px;" mode="widthFix"></image>
						<view class="btn_box">
							<view class="copy" @click="$fun.copy(item.text)">
								<image :src="$fun.imgUrl('/static/icon/c.png')" mode=""></image>
								复制文案
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="textList imageB" v-if="type=='image'">
				<view class="textItem">
					<image v-for="(item,index) in goodsList" :key="index" :src="$fun.imgUrl(item.image)"
						@click="$fun.lookImg(item.image)" mode="widthFix"></image>
				</view>
			</view>
			<view class="textList" v-if="type=='video'">
				<view class="textItem" v-for="(item,index) in goodsList" :key="index">
					<view class="text" :style="{ webkitLineClamp:item.num}" @click="$fun.jump(`./materialVideo?id=${item.id}`)">
						{{item.text}}
					</view>
					<view>
						<image @click="$fun.jump(`./materialVideo?id=${item.id}`)" :src="$fun.imgUrl(item.image)" style="width: 100%;" mode="widthFix"></image>
						<view class="btn_box">
							<view class="copy" @click="$fun.jump(`./materialVideo?id=${item.id}`)">
								查看详情
							</view>
						</view>
					</view>
				</view>
			</view>
		</mescroll-body>
	</view>
</template>

<script>
	import MescrollMixin from "@/components/mescroll-uni/mescroll-mixins.js";
	export default {
		mixins: [MescrollMixin], // 使用mixin
		data() {
			return {
				type: '',
				mescroll: null, // mescroll实例对象 (此行可删,mixins已默认)
				// 下拉刷新的配置(可选, 绝大部分情况无需配置)
				downOption: {},
				// 上拉加载的配置(可选, 绝大部分情况无需配置)
				upOption: {},
				goodsList: [],
				typeList: [{
						name: '文本素材',
						type: 'test'
					},
					{
						name: '图片素材',
						type: 'image'
					},
					{
						name: '视频素材',
						type: 'video'
					},
				],
				name: '',
				isPlay: false,
				perIndex: null,
				iscontrols: false,
				preId:null
				
			};
		},
		onLoad(option) {
			this.type = option.type
			this.name = option.name
			uni.setNavigationBarTitle({
				title: option.name
			})
		},
		methods: {
			videoEnded(e, item, index) {
				this.isPlay = false
				this.iscontrols = true
				this.$fun.ajax.post('/video/setvideo', {
					id: item.id
				}).then(res => {
					this.$fun.msg(res.msg)
				})
			},
			videoPlay(e, item, index) {
				for (var i = 0; i < this.goodsList.length; i++) {
					if (i != index && this.isPlay &&  this.goodsList[i].id!=this.perIndex) {
						this.preId = this.goodsList[i].id
						let videoContext1 = uni.createVideoContext(`videoId` + this.perIndex, this)
						videoContext1.pause()
						videoContext1.seek(0);
					}
				}
				// let videoContext = uni.createVideoContext(`videoId` + index, this)
				// videoContext.requestFullScreen()
				this.isPlay = true
				this.perIndex = index
				this.iscontrols = false
			},
			fullscreenchange(e, item, index) {
				// #ifndef MP-WEIXIN
				// if (!e.detail.fullScreen) {
				// 	let videoContext = uni.createVideoContext(`videoId` + index, this)
				// 	videoContext.pause()
				// 	console.log(e.detail.fullScreen)
				// }
				// #endif

			},
			clickItem(item) {
				this.type = item.type
				this.downCallback()
			},
			/*下拉刷新的回调, 有三种处理方式:*/
			downCallback() {
				this.goodsList = []
				this.mescroll.resetUpScroll(false);
			},
			/*上拉加载的回调*/
			async upCallback(e) {
				this.mescroll.removeEmpty();
				const data = {
					page: e.num,
					pagenum: e.size,
					type: this.type
				};
				this.getMessageList(data)

			},
			getMessageList(e) {
				this.$fun.ajax.post('Source/index', e).then(res => {
					if (res.status == 1) {
						const curList = res.data;
						if (e.num === 1) {
							this.goodsList = [];
							//第一页清空数据重载
							if (curList.length > 0) {
								uni.pageScrollTo({
									scrollTop: 0,
									duration: 200
								});
							}
						}
						this.goodsList = this.goodsList.concat(curList); //追加新数据
						this.mescroll.endSuccess(curList.length); //结束加载状态
					}
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	.page {
		background-color: #FFFFFF;
	}

	page {
		background: #FFFFFF;
	}

	.activity-list {
		padding: 20rpx 4%;

		.list {
			width: 100%;
			margin-bottom: 10px;

			.date {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 100%;
				height: 100rpx;

				text {
					padding: 10rpx 30rpx;
					background-color: rgba(0, 0, 0, 0.2);
					color: #FFFFFF;
					font-size: 26rpx;
					border-radius: 10rpx;
				}
			}

			.item {
				padding: 0 4%;
				// height: 500rpx;
				background-color: #FFFFFF;
				border-radius: 10rpx;

				.title {
					display: flex;
					align-items: center;
					width: 100%;
					height: 100rpx;

					text {
						font-size: 32rpx;
						color: #222222;
					}
				}

				.pictrue {
					position: relative;
					width: 100%;
					height: 300rpx;

					image {
						width: 100%;
						height: 100%;
					}

					.hint {
						position: absolute;
						left: 0;
						top: 0;
						display: flex;
						align-items: center;
						justify-content: center;
						width: 100%;
						height: 100%;
						background-color: rgba(0, 0, 0, 0.3);

						text {
							font-size: 32rpx;
							color: #FFFFFF;
						}
					}
				}

				.describe {
					display: flex;
					align-items: center;
					width: 100%;
					height: 100rpx;

					text {
						font-size: 28rpx;
						color: #959595;
					}
				}
			}
		}
	}

	.tabList {
		background: #FFFFFF;
		border-radius: 0px 0px 0px 0px;
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		justify-content: space-around;
		padding: 32rpx;

		.tabItem {
			margin-right: 15rpx;
			margin-bottom: 20rpx;
			width: 208rpx;
			height: 80rpx;
			background: #F2F2F2;
			border-radius: 44rpx 44rpx 44rpx 44rpx;
			color: #999999;
			text-align: center;
			line-height: 80rpx;
		}

		.active {
			background: rgba(245, 64, 54, .2);
			color: #F54035;
		}
	}

	.textList {
		padding: 25rpx 32rpx;

		.textItem {
			.text {
				font-size: 28rpx;
				font-family: PingFang SC-Medium, PingFang SC;
				font-weight: 500;
				color: #000000;
				margin-bottom: 17rpx;

				overflow: hidden;
				text-overflow: ellipsis;
				display: -webkit-box;
				/* autoprefixer: off */
				-webkit-box-orient: vertical;
				/* autoprefixer: on */

			}

			image {
				border-radius: 20rpx;
			}

			.btn_box {
				margin: 20rpx 0;
				display: flex;
				justify-content: flex-end;
				align-items: center;

				.copy {

					width: 164rpx;
					height: 54rpx;
					border-radius: 27rpx 27rpx 27rpx 27rpx;
					border: 2rpx solid #999999;
					font-size: 22rpx;
					font-family: PingFang SC-Regular, PingFang SC;
					font-weight: 400;
					color: #999999;
					display: flex;
					align-items: center;
					justify-content: center;

					image {
						margin-right: 10rpx;
						width: 25rpx;
						height: 25rpx;
					}
				}
			}
		}
	}

	.textList.video {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;

		.textItem {
			width: 49%;
			margin-bottom: 10rpx;

			video {
				width: 100%;

			}
		}

		.copy {

			height: 54rpx;
			font-size: 22rpx;
			font-family: PingFang SC-Regular, PingFang SC;
			font-weight: 400;
			color: #999999;
			display: flex;
			align-items: center;
			justify-content: center;

			image {
				margin-right: 10rpx;
				width: 25rpx;
				height: 25rpx;
			}
		}

	}

	.imageB {
		.textItem {
			display: flex;
			flex-wrap: wrap;
			justify-content: space-between;

			image {
				margin-bottom: 20rpx;
				width: 335rpx;
				height: 596rpx;
				background: #FFFFFF;
				border-radius: 8px 8px 8px 8px;
				opacity: 1;
			}
		}
	}
</style>