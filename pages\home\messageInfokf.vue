<template>
	<view class="">
		<video style="width: 100%;" v-if="vInfo.videofile" :src="$fun.imgUrl(vInfo.videofile)"></video>
		<view style="padding: 30rpx;" v-html="vHtml">

		</view>
	</view>

</template>

<script>
	export default {
		data() {
			return {
				type: '',
				vHtml: '',
				vInfo: ''
			}
		},
		onLoad(option) {
			this.type = option.type
			uni.setNavigationBarTitle({
				title: option.name
			})
			this.init()
		},
		methods: {
			init() {
			
				this.$fun.ajax.post('config/getassist', {
					name: this.type
				}).then(res => {
					this.vHtml = res.data
					this.vInfo = res.data
				})
			}
		}
	}
</script>

<style lang="scss">
	page {
		background-color: #FFFFFF;
	}
</style>