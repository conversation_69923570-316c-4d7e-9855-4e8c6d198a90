<template>
	<view>
		<view class="video">
			<video  :src="$fun.imgUrl(contentInfo.videofile)"  ></video>
		</view>
		<view class="text">
			<view class="textinfo">{{contentInfo.text}}</view>
		</view>
		<view class="btn_box">
			<view class="copy" @click="download">
				<image :src="$fun.imgUrl('/static/icon/d.png')" mode=""></image>
				下载视频
			</view>
			<view class="copy" @click="$fun.copy(contentInfo.text)">
				<image :src="$fun.imgUrl('/static/icon/c.png')" mode=""></image>
				复制文案
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				id:null,
				contentInfo:{
					videofile:''
				}
			};
		},
		onLoad(option) {
			this.id = option.id;
			this.getContent()
		},
		methods:{
			download(){
				let url = this.$fun.imgUrl(this.contentInfo.videofile);
				this.$fun.uploadVideo(url)
			},
			getContent(){
				this.$fun.ajax.post('Source/content',{id:this.id}).then(res=>{
					this.contentInfo = res.data
				})
			}
		}
	}
</script>

<style lang="scss">
	page{
		background: #FFFFFF;
	}
.video{
	text-align: center;
	video{
		margin: 32rpx auto;
		width: 670rpx;
		height: 324rpx;
		background: #000000;
		border-radius: 16px 16px 16px 16px;
	}
}
.text{
	display: flex;
	justify-content: center;
	.textinfo{
	width: 670rpx;
	font-size: 28rpx;
	font-family: PingFang SC-Medium, PingFang SC;
	font-weight: 500;
	color: #000000;	
	text-align: left;
	}
}
.btn_box {
	width: 670rpx;
				margin: 20rpx 0;
				display: flex;
				justify-content: flex-end;
				align-items: center;

				.copy {
					margin-left: 20rpx;
					width: 164rpx;
					height: 54rpx;
					border-radius: 27rpx 27rpx 27rpx 27rpx;
					border: 2rpx solid #999999;
					font-size: 22rpx;
					font-family: PingFang SC-Regular, PingFang SC;
					font-weight: 400;
					color: #999999;
					display: flex;
					align-items: center;
					justify-content: center;
					image {
						margin-right: 10rpx;
						width: 25rpx;
						height: 25rpx;
					}
				}
			}
</style>
