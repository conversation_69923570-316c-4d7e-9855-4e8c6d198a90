/**
* @Description:  太和永康 



* 
*/
<template>
	<view>
		<view class="inputWrap">
			<block v-if="sms_status==1">
				<u-input class="input" v-model="phone" placeholder="请填写联系电话" disabled maxlength="11" :border="true"
					border-color="#eee" />
				<view class="u-flex u-row-left u-col-center">
					<u-input class="input" v-model="code" placeholder="请输入验证码" maxlength="6" :border="true"
						border-color="#eee" />
					<text class="sendcoder" @click="setPayPwdSms">{{clickText}}</text>
				</view>
			</block>
			<block v-else>
				<u-input class="input" v-model="oldpwd" type="number" placeholder="请输入旧支付密码" maxlength="6" :border="true"
					border-color="#eee" />
			</block>
			<u-input class="input u-m-t-38" type="number" v-model="paypwd" placeholder="请输入支付密码" maxlength="6" :border="true"
				border-color="#eee" />
			<u-input class="input u-flex u-m-t-38" type="number" maxlength="6" v-model="paypwd2" placeholder="请确认支付密码"
				 :border="true" border-color="#eee" />

			<u-button style="background: #310FFF;" type="error" mode="plain" shape="circle" class="u-m-t-40" @click="confirmClick">确 定</u-button>

		</view>
	</view>
</template>

<script>
	let courr = 60;
	let setId = null;
	export default {

		data() {
			return {
				phone: "",
				oldpwd:"",
				paypwd: "",
				paypwd2: "",
				code: "",
				clickText: "",
				sms_status: false
			};
		},
		onLoad(option) {
			this.sms_status = option.sms_status
			this.getUserPhone();
			this.clickText = '获取验证码'
		},
		copmputed: {
			returnPwd() {
				return this.paypwd === this.paypwd2
			}
		},

		methods: {
			confirmClick() {
				if (this.sms_status == 1) {
					if (this.code == "") {
						this.$fun.msg('请输入验证码');
						return false;
					}
				} else {
					if (this.oldpwd == "") {
						this.$fun.msg('请输入旧支付密码');
						return false;
					}
				}
				if (this.paypwd == "") {
					this.$fun.msg('请输入密码');
					return false;
				}
				if (this.paypwd !== this.paypwd2) {
					return this.$fun.msg('两次输入的密码不一致');

				}
				if (this.paypwd.length!=6) {
					return this.$fun.msg('支付密码必须为6位数字');

				}
				let prams = {
					pay: this.paypwd,
					repay: this.paypwd,
				}
				if (this.sms_status == 1) {
					prams.code = this.code
				} else {
					prams.oldpay = this.oldpwd
				}
				console.log(prams)
				this.$fun.ajax.post('user/editPay', {
					...prams
				}).then(res => {
					if (res.status == 1) {
						this.$fun.msg(res.msg);
						setTimeout(() => {
							this.$u.route({
								url: "/pagesB/setting/setting",
								type: "back"
							});
						}, 1500);
					}
				})
			},
			setPayPwdSms() {
				if (this.phone.length != 11) {
					this.$fun.msg('请输入正确的手机号');
					return false;
				}
				if (setId) {
					// 禁止重复发送
					return false;
				}
				this.$fun.ajax.post('sms/send',{mobile:this.phone,event:'resetpay'}).then(res=>{
					if (res.status == 1) {
							this.$fun.msg(res.msg);
							setId = setInterval(() => {
								courr--;
								if (courr == 0) {
									courr = 60;
									clearInterval(setId);
									this.clickText = '重新获取';
									return false;
								}
								this.clickText = courr + 's' + '重新获取';
							}, 1000);
						}
				})
			},
			getUserPhone() {
				// this.$http.getUserPhone().then(res => {
				// if (res.status == 200) {
				this.phone = uni.getStorageSync('userinfo').mobile;
				// 	}
				// });
			}
		}
	};
</script>
<style lang="scss" scoped>
	@import '~uview-ui/index.scss';
	page{
		background-color: #fff;
	}
	.inputWrap {
		background-color: #fff;
		padding: 30upx;
	}

	.input {
		height: 80upx;
		margin: 20upx 0;
	}

	.sendcoder {
		font-size: 24upx;
		color: #310FFF;
		margin-left: 20upx;
	}

	::v-deep uni-view {
		line-height: 2.4;
	}
	::v-deep .u-error-hover {
		background: #310FFF !important;
	}
</style>
