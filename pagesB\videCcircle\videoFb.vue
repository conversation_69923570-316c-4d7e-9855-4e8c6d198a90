<template>
	<view class="page">
		<view class="input_box">
			<view class="input_title">
				<view class="title">
					口碑视频
				</view>
			</view>
			<view class="input_box_img">
				<view class="upload_img" v-if="!videofile" @click="upload()">
					<view class="up_img">
						+
					</view>
				</view>
				<view class="upload_img" style="position: relative;" v-else>
					<image @click="videofile=''"
						style="position: absolute; top: -50rpx;right: -50rpx;width: 50rpx;height: 50rpx;"
						src="/static/close.png" mode=""></image>
					<video style="width: 200rpx;height:200rpx;" :src="$fun.imgUrl(videofile)"></video>
				</view>
			</view>
		</view>
		<!-- <view class="input_box">
			<view class="input_title">
				<view class="title">
					视频封面
				</view>
			</view>
			<view class="input_box_img">
				<view class="upload_img" v-if="!coverimage" @click="upload(2)">
					<view class="up_img">
						+
					</view>
				</view>
				<view class="upload_img" style="position: relative;" v-else>
					<image @click="coverimage=''"
						style="position: absolute; top: -50rpx;right: -50rpx;width: 50rpx;height: 50rpx;"
						src="/static/close.png" mode=""></image>
					<image class="up_img" :src="$fun.imgUrl(coverimage)" mode=""></image>
				</view>
			</view>
		</view> -->
		<view class="input_box">
			<view class="input_title">
				<image src="/static/live/store_2.png" mode=""></image>
				<view class="title">
					口碑信息
				</view>
			</view>
			<u-form ref="uForm">
				<u-form-item :label-width="300" :label-style="albelS" label="标题"><u-input v-model="title"
						placeholder="请输入您的口碑标题" /></u-form-item>
				<u-form-item :label-width="300" :label-style="albelS" label="格式">
					<u-radio-group v-model="object_fit">
						<u-radio :name="'contain'" active-color="#310FFF">
							横屏
						</u-radio>
						<u-radio :name="'cover'" active-color="#310FFF">
							竖屏
						</u-radio>
					</u-radio-group>
				</u-form-item>
			</u-form>
		</view>
		<view class="config_btn" @click="confirm()">
			发布
		</view>
		<view style="height: 102rpx;">

		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				swiperList: [],
				videofile: null,
				coverimage: null,
				object_fit: null,
				title: '',
				albelS: {
					fontFamily: 'Source',
					fontWeight: '400',
					fontSize: '28rpx',
					color: '#333333'
				}
			};
		},
		onLoad() {
			this.init();
		},
		methods: {
			confirm() {
				if (!this.videofile) {
					this.$fun.msg('请上传口碑视频');
					return
				}
				if (!this.title) {
					this.$fun.msg('请上传口碑标题');
					return
				}
				if (!this.object_fit) {
					this.$fun.msg('请选择视频显示格式');
					return
				}
				console.log(this.object_fit)
				this.$fun.ajax.post('video/addkb', {
					videofile: this.videofile,
					title: this.title,
					// coverimage: this.coverimage,
					object_fit: this.object_fit,
				}).then(res => {
					if (res.status == 1) {
						this.$fun.msg(res.msg);
						this.$fun.jump('', 4, 1200);
					}
				})
			},
			/**
			 * 初始化
			 */
			async init() {
				await this.getSwiper()
			},
			upload(type = 1) {
				if (type == 1) {
					this.uploadImage('chooseVideo', '发送视频', 4, ['original']);
				} else {
					this.uploadImage('chooseImage', '发送图片', 3, ['original']);
				}
			},
			// 上传头像

			uploadImage(uploadType, msg, type, sizeType) {
				if (type != 3) {
					// 从相册选择图片
					const _this = this;
					uni[uploadType]({
						count: 1,
						sizeType, //可以指定是原图还是压缩图，默认二者都有
						success: function(res) {
							// 视频
							_this.handleUploadFile(res.tempFilePath, msg, type);

						}
					});
					return;
				}
				const _this = this;
				// #ifdef H5
				uni[uploadType]({
					count: 1,
					extension: ['.txt', '.doc', '.ppt', '.pdf'],
					success: function(res) {
						_this.handleUploadFile(res.tempFilePaths.path || res.tempFilePaths[0], msg,
							type);
					}
				});
				return;
				// #endif

				// #ifdef APP-PLUS
				switch (uni.getSystemInfoSync().platform) {
					case 'android':
						let permisionID = 'android.permission.READ_EXTERNAL_STORAGE'
						permision.requestAndroidPermission(permisionID)
							.then(res => {
								if (res == 1) {
									this.chooseFileAndroid()
								} else {
									uni.showModal({
										title: '无法访问存储',
										content: '您已经关闭存储权限,您已经关闭存储权限,请在"设置-隐私-存储"中允许访问存储',
										success: (res) => {
											if (res.confirm) {
												// plus.runtime.openURL("package:");
											} else if (res.cancel) {
												plus.runtime.openURL("package:");
												console.log('用户点击取消');
											}
										}
									});
								}
							})
						break;
					case 'ios':
						this.filePathIos()
						break;
				}
				// #endif
			},
			// 上传文件
			handleUploadFile(filePath, msg, type, time = 0) {
				console.log(filePath)
				const _this = this;
				this.$fun.uploadPic(
					filePath
				).then(res => {
					if (res.status == 1) {
						if (type == 4) {
							this.videofile = res.data.url;
						} else {
							this.coverimage = res.data.url;
						}
					}
				})
			},
			/**
			 * 获取轮播图
			 */
			getSwiper() {
				this.$fun.ajax.post('News/lists', {
					type: 'index'
				}).then(res => {
					console.log(res)
					if (res.status == 1) {
						this.swiperList = res.data
					}
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	page {
		.page {
			padding: 0 16rpx;

			.banner {
				margin-top: 32rpx;
				// margin-top: calc(110rpx + var(--status-bar-height));
				margin-top: 30rpx;

				padding: 0 25rpx;
				height: 324rpx;
				border-radius: 10rpx;
				overflow: hidden;

				.screen-swiper {
					height: 324rpx;
					min-height: 100% !important;

					image {
						height: 324rpx;
						border-radius: 10rpx;
					}
				}
			}

			.input_box {
				margin-top: 24rpx;
				padding: 24rpx;
				background: #FFFFFF;
				border-radius: 20rpx 20rpx 20rpx 20rpx;

				.input_title {
					display: flex;
					align-items: center;
					justify-content: center;

					image {
						width: 50rpx;
						height: 50rpx;
					}

					.title {
						font-family: Source;
						font-weight: 500;
						font-size: 36rpx;
						color: #310FFF;
					}
				}

				.input_box_img {
					padding-top: 24rpx;
					display: flex;
					flex-wrap: wrap;
					justify-content: center;

					.name {
						font-family: Source;
						font-weight: 400;
						font-size: 28rpx;
						color: #333333;
					}

					.upload_img {
						display: flex;
						align-items: center;
						flex-direction: column;
						justify-content: center;

						.up_img {
							display: flex;
							justify-content: center;
							align-items: center;
							width: 200rpx;
							height: 200rpx;
							border: 2rpx dashed #AAAAAA;
							font-size: 80rpx;
							color: #AAAAAA;
						}

						.t {
							font-family: Source;
							font-weight: 400;
							font-size: 24rpx;
							color: #AAAAAA;
						}
					}

					.upload_img_b {
						margin-top: 24rpx;
						width: 100%;
						display: flex;
						justify-content: center;

						image {
							margin: 0 22rpx;
						}
					}
				}

				.textarea_b {
					margin-top: 20rpx;
					width: 100%;
					height: 288rpx;
					background: #F8F8F8;
					border-radius: 16rpx 16rpx 16rpx 16rpx;

					textarea {
						padding: 24rpx;
					}

					.textarea_p {
						font-family: Source;
						font-weight: 400;
						font-size: 24rpx;
						color: #AAAAAA;
					}
				}
			}

			.config_btn {
				width: 352rpx;
				height: 92rpx;
				background: #310FFF;
				border-radius: 12rpx 12rpx 12rpx 12rpx;
				font-family: Source;
				font-weight: 500;
				font-size: 32rpx;
				color: #FFFFFF;
				display: flex;
				justify-content: center;
				align-items: center;
				margin: 102rpx auto;
			}
		}
	}
</style>