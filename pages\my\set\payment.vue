<template>
	<view class="index_class">

		<view class="bus-top">
			<view class="order-filter">
				<view class="items" v-for="(item,index) in  PayConfig.pay_type" :key="index"
					:class="{'order_active':showingIndex == index}" @click="tabChange(index)">{{item.name}}</view>
			</view>
		</view>
		<block v-for="(item,index) in  PayConfig.pay_type" :key="index">
			<view class="content_box" v-if="item.code=='wechat'&&item.code==PayConfig.pay_type[showingIndex].code">
				<u-form labelPosition="top" :model="wechat" ref="form1">
					<u-form-item label="姓名" prop="name" borderBottom ref="item1">
						<u-input placeholder="请输入姓名" :border="true" v-model="wechat.name"></u-input>
					</u-form-item>
					<u-form-item label="手机号" prop="mobile" borderBottom ref="item1">
						<u-input placeholder="请输入手机号" type="number" maxlength="11" :border="true"
							v-model="wechat.mobile"></u-input>
					</u-form-item>
				</u-form>
				<view class="top">
					<view class="left">
						<view class="title">收款码(微信)</view>
					</view>
				</view>
				<view class="top">
					<view class="left">
						<image :src="wechat.image?$fun.imgUrl(wechat.image):'/static/ico-101.png'"
							@click="uploadImage('wechat')" style="width: 150rpx;height:150rpx;" mode=""></image>
					</view>
				</view>
				<view style="height: 60rpx;">

				</view>
				<u-button type="error" mode="plain" shape="circle" class="u-m-t-40" @click="confirmClick(item.code)">确 定
				</u-button>
			</view>
			<view class="content_box" v-if="item.code=='alipay'&&item.code==PayConfig.pay_type[showingIndex].code">
				<u-form labelPosition="top" :model="alipay" ref="form1">
					<u-form-item label="姓名" prop="name" borderBottom ref="item1">
						<u-input placeholder="请输入姓名" :border="true" v-model="alipay.name"></u-input>
					</u-form-item>
					<u-form-item label="手机号" prop="mobile" borderBottom ref="item1">
						<u-input placeholder="请输入手机号" type="number" maxlength="11" :border="true"
							v-model="alipay.mobile"></u-input>
					</u-form-item>
				</u-form>
				<view class="top">
					<view class="left">
						<view class="title">收款码(支付宝)</view>
					</view>
				</view>
				<view class="top">
					<view class="left">
						<image :src="alipay.image?$fun.imgUrl(alipay.image):'/static/ico-101.png'"
							@click="uploadImage('alipay')" style="width: 150rpx;height:150rpx;" mode=""></image>
					</view>
				</view>
				<view style="height: 60rpx;">

				</view>
				<u-button type="error" mode="plain" shape="circle" class="u-m-t-40" @click="confirmClick(item.code)">确 定
				</u-button>
			</view>
			<view class="content_box" v-if="item.code=='bank'&&item.code==PayConfig.pay_type[showingIndex].code">
				<!-- <u-alert-tips v-if="bank.memo&&bank.status==2" type="error" :desc-style="{color:'red'}"
					:title-style="{color:'red',fontWeight:'bold'}" :title="'失败原因'" :description="bank.memo"></u-alert-tips>
				<u-alert-tips v-if="bank.status==0" type="success" :desc-style="{color:'#71d5a1'}"
					:title-style="{color:'#71d5a1',fontWeight:'bold'}" :title="'提示'"
					:description="'审核中'"></u-alert-tips>
				<u-alert-tips v-if="bank.status==1" type="success" :desc-style="{color:'#71d5a1'}"
					:title-style="{color:'#71d5a1',fontWeight:'bold'}" :title="'提示'"
					:description="'审核成功'"></u-alert-tips> -->
				<u-form labelPosition="top" :model="bank" ref="form1">
					<u-form-item label="真实姓名" prop="name" borderBottom ref="item1">
						<u-input placeholder="请输入真实姓名" :border="true" v-model="bank.name"></u-input>
					</u-form-item>
					<u-form-item label="手机号" prop="mobile" borderBottom ref="item1">
						<u-input placeholder="请输入手机号" type="number" maxlength="11" :border="true" v-model="bank.mobile">
						</u-input>
					</u-form-item>
					<u-form-item label="开户行" prop="bank" borderBottom ref="item1">
						<u-input placeholder="请输入开户行" :border="true" v-model="bank.bank"></u-input>
					</u-form-item>
					<u-form-item label="开户地址" prop="bankname" borderBottom ref="item1">
						<u-input placeholder="请输入开户地址" :border="true" v-model="bank.bankname"></u-input>
					</u-form-item>
					<u-form-item label="开户卡号" prop="bankcode" borderBottom ref="item1">
						<u-input placeholder="请输入开户卡号" :border="true" v-model="bank.bankcode"></u-input>
					</u-form-item>

					<!-- 	<u-form-item label="电子发票" prop="bankcode" borderBottom ref="item1">
						<u-input placeholder="请输入电子发票" :border="true" v-model="bank.invoice"></u-input>
					</u-form-item> -->
				</u-form>
				<!-- <view class="top">
					<view class="left">
						<view class="title">营业执照</view>
					</view>
				</view>
				<view class="top">
					<view class="left">
						<image :src="bank.licenseimage?$fun.imgUrl(bank.licenseimage):'/static/ico-101.png'"
							@click="uploadImage('bank','licenseimage')" style="width: 150rpx;height:150rpx;" mode="">
						</image>
					</view>

				</view> -->
				<!-- <view class="top">
					<view class="left">
						<view class="title">电子发票</view>
					</view>
				</view>
				<view class="top">
					<view class="left">
						<image :src="bank.invoicefile ?$fun.imgUrl(bank.invoicefile ):'/static/ico-101.png'"
							@click="uploadImage('bank','invoicefile')" style="width: 150rpx;height:150rpx;" mode="">
						</image>
					</view>
				</view> -->
				<view style="height: 60rpx;">

				</view>
				<view class="btn">

				</view>
				<u-button type="error" style="background: #310FFF;" mode="plain" shape="circle" class="u-m-t-40 btn_q"
					@click="confirmClick(item.code)">确 定
				</u-button>
			</view>
		</block>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// tab
				PayConfig: {},
				// tab active
				showingIndex: 0,
				// tab list
				PayConfigList: [],
				wechat: {
					name: "",
					mobile: "",
					image: "",
					type: "wechat"
				},
				alipay: {
					name: "",
					mobile: "",
					image: "",
					type: "alipay"
				},
				bank: {
					name: "",
					mobile: "",
					bank: "",
					bankname: "",
					bankcode: "",
					// licenseimage: "",
					memo: '',
					status: 100,
					type: "bank"
				},

			};
		},
		async onLoad(option) {
			await this.getPayConfig()
		},
		onShow() {

		},
		methods: {
			tabChange(e) {
				this.showingIndex = e
				this.getPaylist(this.PayConfig.pay_type[e].code)
			},
			getPayConfig() {
				this.$fun.ajax.post('config/index', {}).then(res => {
					if (res.status == 1) {
						this.PayConfig = res.data
						this.getPaylist(this.PayConfig.pay_type[0].code)
					}
				})
			},
			getPaylist(type) {
				this.$fun.ajax.post('bank/lists', {
					type
				}).then(res => {
					if (res.status == 1 && res.data.length > 0) {
						this[res.data[0].type] = res.data[0]
					}
				})
			},
			confirmClick(code) {
				let prams = {
					...this[code]
				}
				let isok = true
				Object.getOwnPropertyNames(prams).forEach((key, item) => {
					if (prams[key] == "" && key!='memo') {
						isok = false
					}
				})
				if (!isok) {
					this.$fun.msg('请确认所有信息已填写!');
					return false;
				}
				let list_item = this.PayConfig.pay_type[this.showingIndex];
				this.$fun.ajax.post('bank/add', {
					...prams
				}).then(res => {
					this.$fun.msg(res.msg);
					if (res.status == 1) {
						setTimeout(() => {
							uni.navigateBack({

							})
						}, 1500)
					}
				})
			},
			// 上传头像
			uploadImage(str, str1) {
				// 从相册选择图片
				const _this = this;
				uni.chooseImage({
					count: 1,
					sizeType: ['original', 'compressed'],
					sourceType: ['album'],
					success: function(res) {
						_this.handleUploadFile(res.tempFilePaths, str, str1);
					}
				});
			},
			// 上传头像
			handleUploadFile(data, str, str1) {
				const _this = this;
				const filePath = data.path || data[0];
				this.$fun.uploadPic(
					filePath
				).then(res => {
					// this.$fun.msg(res.msg)
					if (res.status == 1) {
						if (str == 'bank') {
							console.log(str1)
							_this[str][str1] = res.data.url;
						} else {
							_this[str].image = res.data.url;
						}
					}
				})
			},
		}
	};
</script>

<style lang="scss" scoped>
	page {
		background: #FFFFFF;
	}

	.content_box {
		padding: 30rpx;
		background: #FFFFFF;
	}

	.shou_sure {
		span {
			display: block;
			width: 100upx;
			height: 40upx;
			line-height: 40upx;
			border-radius: 20upx;
			border: 1upx solid #999;
			color: #999;
			text-align: center;
			font-size: 24upx;
		}
	}

	.padding-btn {
		padding-bottom: 80upx;
	}

	.mescroll-uni.mescroll-uni-fixed {
		top: 96upx !important;
	}

	.to_comment {
		width: 100upx !important;
		margin-left: 0 !important;
	}

	.paybtn {
		width: 100%;
		position: fixed;
		bottom: 0;
		left: 0;
		background-color: #fff;
		padding: 20upx;
	}

	.all-order {
		padding-top: 88upx;

		.order_list {
			margin-bottom: 20upx;
			background-color: #fff;

			.order_top {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 20upx 30upx;
				border-bottom: 1upx solid #f4f4f4;
				color: #333;
			}
		}

		.order-all-text {
			text-align: right;
			font-size: 24upx;
			color: #333;
			margin-top: 15upx;
		}
	}

	.top {
		display: flex;
		justify-content: space-between;
		margin-left: 20rpx;

		.left {
			margin-top: 40rpx;

			.title {
				font-size: 24rpx;
				color: #666;
			}

			.num {
				font-size: 48rpx;
				color: #000;
				margin-top: 30rpx;
				font-weight: bold;
			}

			.jyjl {
				font-size: 24rpx;
				color: #dd1021;
				margin-top: 50rpx;
			}
		}

		.rightimg {
			width: 233rpx;
			height: 240rpx;
			margin-right: 50rpx;
		}
	}

	// 所有订单页面
	.order-filter {
		display: flex;
		justify-content: space-between;
		align-items: center;
		height: 88rpx;
		padding: 20rpx;
		background-color: #FFFFFF;

		.items {
			height: 86rpx;
			width: 20%;
			text-align: center;
			line-height: 86rpx;
			font-size: 32rpx;

			&.order_active {
				border-bottom: 2rpx solid #310FFF;
				color: #310FFF;
			}

		}
	}

	::v-deep .u-error-hover {
		background: #310FFF !important;
	}
</style>