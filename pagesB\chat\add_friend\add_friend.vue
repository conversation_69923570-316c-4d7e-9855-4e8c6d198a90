<template>
	<view class="add_firend">
		<view class="search">
			<u-search placeholder="搜索" v-model="keyword" input-align="center" @custom="searchUser" @search="searchUser"></u-search>
		</view>
		<view class="my_code">
			<text>我的账号:{{userInfo.mobile}}</text>
			<u-modal v-model="show" :content="content"></u-modal>
			<!-- <u-icon name="grid"   size="28"></u-icon> -->
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				keyword: '',
				userInfo: "",
				show: false,
				content: '用户不存在'
			}
		},
		onLoad() {
			this.userInfo = uni.getStorageSync('userinfo');
		},
		methods: {
			searchUser() {
				this.$fun.ajax.post('chat/getUser', {
					account: this.keyword
				}).then(res => {
					if (res.status == 1) {
						if (res.data) {
							this.$fun.jump(`./add_confirm?account=${this.keyword}&type=1`);
							this.keyword = ""
						} else {
							this.show = true
						}
					}
				})
			}
		}
	}
</script>

<style lang="scss">
	page {
		background: #F6F6F6;

		.add_firend {
			background: #FFFFFF;
			padding: 32rpx;

			.my_code {
				margin-top: 20rpx;
				justify-content: center;
				display: flex;
				font-family: Source Han Sans CN, Source Han Sans CN;
				font-weight: 400;
				font-size: 24rpx;
				color: #333333;
				line-height: 28rpx;
				text-align: center;
				font-style: normal;
				text-transform: none;

				text {
					margin-right: 10rpx;
				}
			}
		}
	}
</style>