<template>
	<view class="shop_list_box">
		<mescroll-body ref="mescrollRef" @init="mescrollInit" @down="downCallback" @up="upCallback" :down="downOption"
			:up="upOption" :top="0">
			<u-tabs :bg-color="'#310FFF'" :inactive-color="'#FFFFFF'" :active-color="'#FFFFFF'" :list="list"
				:is-scroll="false" :current="current" @change="change"></u-tabs>
			<view class="shop_list">
				<view class="shop_item" v-for="(item,index) in  shop_list" :key="index">
					<view class="item_img">
						<image :src="$fun.imgUrl(item.image)" mode="">
						</image>
					</view>
					<view class="item_info">
						<view class="title">
							<view class="name">
								{{item.name}}
							</view>
							<view class="tip">
								<u-tag
									:text="item.status==0?'下架':item.status==1?'上架':item.status==2?'待审核':item.status==3?'审核失败':''"
									mode="dark"
									:type="item.status==0?'error':item.status==1?'success':item.status==2?'primary':item.status==3?'warning':''" />
							</view>
						</view>
						<view class="p_box">
							<text class="price">{{item.money}}</text>
						</view>
						<view class="p_box">
							<view class="btn_box" v-if="item.status!=2">
								<view class="btn y" @click="operation(item,'business/setGoodsStatus',1,'确定是否要上架该产品?')"
									v-if="item.status==0&&item.status!=3">
									<text>上架</text>
								</view>
								<view class="btn b" @click="operation(item,'business/setGoodsStatus',0,'确定是否要下架该产品?')"
									v-else-if="item.status!=3">
									<text>下架</text>
								</view>
								<view class="btn r" v-if="item.status==3" @click="lookYy(item)">
									<text>查看原因</text>
								</view>
								<view class="btn r" @click="$fun.jump(`./push_shop/push_shop?id=${item.id}`)">
									<text>编辑</text>
								</view>
								<view class="btn r" @click="operation(item,'business/delGoods',1,'确定是否要删除该产品?')">
									<text>删除</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>

		</mescroll-body>
		<view class="fb_flex1" v-if="!is_push" @click="changePush(1)">
			<text>开启</text>
			<text>推送</text>
		</view>
		<view class="fb_flex1" v-else @click="changePush(0)">
			<text>关闭</text>
			<text>推送</text>
		</view>
		<view class="fb_flex" @click="$fun.jump(`./push_shop/push_shop`)">
			<image :src="$fun.imgUrl('/static/apply/f.png')" mode="widthFix"></image>
			<text>发布</text>
		</view>
		<u-modal v-model="show" :content="content" @confirm="show=false"></u-modal>
	</view>
</template>

<script>
	import MescrollMixin from "@/components/mescroll-uni/mescroll-mixins.js";
	export default {
		mixins: [MescrollMixin], // 使用mixin
		data() {
			return {
				list: [{
					name: '全部'
				}, {
					name: '下架'
				}, {
					name: '上架',
				}, {
					name: '待审核',
				}, {
					name: '审核失败',
				}],
				current: 0,
				shop_list: [],
				mescroll: null, // mescroll实例对象 (此行可删,mixins已默认)
				// 下拉刷新的配置(可选, 绝大部分情况无需配置)
				downOption: {},
				// 上拉加载的配置(可选, 绝大部分情况无需配置)
				upOption: {},
				show: false,
				content: '',
				is_push: false
			}
		},
		onLoad() {
			// this.getList()
		},
		methods: {
			lookYy(item) {
				this.show = true;
				this.content = item.memo;
			},
			changePush(type) {
				let _this = this;
				let openid = uni.getStorageSync('openid');
				let prams = {
					openid: openid ? openid : '',
					status: type
				}
				uni.showModal({
					title: '提示',
					content: `确定要${type==0?'关闭':'开启'}订单推送`,
					success: function(res) {
						if (res.confirm) {
							_this.$fun.ajax.post('business/setOrderPush', prams).then(res => {
								if (res.status == 1) {
									_this.$fun.msg('操作成功')
								}
							})
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});

			},
			change(e) {
				this.current = e
				this.getList()
			},
			/*下拉刷新的回调, 有三种处理方式:*/
			downCallback() {
				this.cartList = []
				this.mescroll.resetUpScroll(false);
			},
			/*上拉加载的回调*/
			upCallback(e) {
				this.mescroll.removeEmpty();
				const data = {
					page: e.num,
					pagenum: e.size,
					status: this.current == 0 ? 'all' : this.current - 1
				};
				this.$fun.ajax.post('business/goods', data).then(res => {
					if (res.status == 1) {
						const curList = res.data;
						// for (var i = 0; i < curList.length; i++) {
						// 	curList[i].icon = false
						// }
						if (e.num === 1) {
							this.shop_list = [];
							//第一页清空数据重载
							if (curList.length > 0) {
								uni.pageScrollTo({
									scrollTop: 0,
									duration: 200
								});
							}
						}
						this.shop_list = this.shop_list.concat(curList); //追加新数据
						this.mescroll.endSuccess(curList.length); //结束加载状态
					}
				})
				let openid = uni.getStorageSync('openid');
				this.$fun.ajax.post('business/getOrderPush', {
					openid: openid ? openid : ''
				}).then(res => {
					if (res.status == 1) {
						this.is_push = res.data
					}
				})
			},
			getList() {
				let prams = {
					status: this.current == 0 ? 'all' : this.current - 1
				};
				this.$fun.ajax.post('business/goods', {
					...prams
				}).then(res => {
					if (res.status == 1) {
						this.shop_list = res.data;
					}
				})
			},
			operation(item, url, status, tip) {
				uni.showModal({
					title: '提示',
					content: tip,
					success: res => {
						if (res.confirm) {
							this.$fun.ajax.post(url, {
								id: item.id,
								status
							}).then(res => {
								if (res.status == 1) {
									this.$fun.msg(res.msg)
									setTimeout(() => {
										this.getList()
									}, 800)
								}
							})
						}
					}
				})
			}
		}
	}
</script>

<style lang="scss">
	page {
		.shop_list {
			margin: 24rpx;

			.shop_item {
				display: flex;
				padding: 24rpx;

				background: #FFFFFF;
				border-radius: 16rpx 16rpx 16rpx 16rpx;
				margin-bottom: 24rpx;

				.item_img {
					margin-right: 16rpx;

					image {
						width: 160rpx;
						height: 160rpx;
						border-radius: 8rpx 8rpx 8rpx 8rpx;
					}
				}

				.item_info {
					display: flex;
					flex-direction: column;
					justify-content: center;

					.title {
						display: flex;
						justify-content: space-between;

						.name {
							width: calc(100% - 150rpx);
							display: -webkit-box;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 2;
							/* 限制显示两行 */
							overflow: hidden;
						}

						.tip {}
					}

					.p_box {
						width: calc(100vw - 273rpx);
						margin-top: 32rpx;
						display: flex;
						align-items: center;

						.price {
							font-weight: 500;
							font-size: 32rpx;
							color: #FF2F00;
						}

						.btn_box {

							display: flex;
							justify-content: flex-end;

							.btn {
								margin-left: 32rpx;
								padding: 10rpx 20rpx;
								border-radius: 8rpx 8rpx 8rpx 8rpx;

								text {
									font-size: 24rpx;
									color: #FFFFFF;
								}
							}

							.y {
								background: #FFBB3C;

								text {
									color: #333333;
								}
							}

							.b {
								background: #046BF6;
							}

							.r {
								background: #310FFF;
							}
						}
					}
				}
			}
		}

		.fb_flex {
			position: fixed;
			right: 32rpx;
			bottom: 232rpx;
			width: 110rpx;
			height: 110rpx;
			background: #310FFF;
			border-radius: 50%;
			display: flex;
			flex-direction: column;
			align-items: center;

			image {
				margin-top: 12rpx;
				width: 58rpx;
			}

			text {
				font-weight: 400;
				font-size: 24rpx;
				color: #FFFFFF;
			}
		}

		.fb_flex1 {
			position: fixed;
			right: 32rpx;
			bottom: 350rpx;
			width: 110rpx;
			height: 110rpx;
			background: #310FFF;
			border-radius: 50%;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;

			text {
				font-weight: 400;
				font-size: 24rpx;
				color: #FFFFFF;
			}
		}
	}
</style>