import Vue from 'vue';
import Vuex from 'vuex';
Vue.use(Vuex);
const store = new Vuex.Store({
	state: {
		tabList: [{
				index: 0,
				name: '首页',
				img: '/static/tab_01.png',
				acImg: '/static/tab_02.png',
				path: '/pages/home/<USER>'
			},
			{
				index: 1,
				name: '商城',
				img: '/static/tab_03.png',
				acImg: '/static/tab_04.png',
				path: '/pages/classify/classify'
			},
			{
				index: 2,
				name: '聊天',
				img: '/static/tab_11.png',
				acImg: '/static/tab_12.png',
				path: '/pagesB/chat/chat'
			},
			// {
			// 	index: 3,
			// 	name: '公告',
			// 	img: '/static/tab_07.png',
			// 	acImg: '/static/tab_08.png',
			// 	path: '/pages/home/<USER>'
			// },
			{
				index: 3,
				name: '购物车',
				img: '/static/tab_071.png',
				acImg: '/static/tab_081.png',
				path: '/pages/cart/cart'
			},
			{
				index: 4,
				name: '我的',
				img: '/static/tab_09.png',
				acImg: '/static/tab_10.png',
				path: '/pages/my/my'
			}
		]
	},
	getters: {},
	mutations: {},
	actions: {}
});
export default store;