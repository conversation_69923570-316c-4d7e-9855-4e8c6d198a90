<template>
	<view>
		<block v-if="store_status == '' || store_status == 1">
			<view class="top_step">
				<u-steps :list="numList" @clickStep="setStep" active-color="#310FFF" mode="number"
					:current="current"></u-steps>
			</view>

			<view class="apply_box" v-if="current == 0">
				<view class="apply_item">
					<view class="title">
						请选择营业类型
					</view>
				</view>
				<view class="apply_item" @click="change('one', 'type', 0)">
					<view class="title">
						<image :src="$fun.imgUrl('/static/apply/gt.png')" mode="widthFix"></image>

						个体工商户
					</view>
					<view class="radio">
						<image v-if="prams.one.type == 0" :src="$fun.imgUrl('/static/apply/c1.png')" mode="widthFix">
						</image>
						<image v-else :src="$fun.imgUrl('/static/apply/c.png')" mode="widthFix"></image>
					</view>
				</view>
				<view class="apply_item" @click="change('one', 'type', 1)">
					<view class="title">
						<image :src="$fun.imgUrl('/static/apply/gt.png')" mode="widthFix"></image>
						企业
					</view>
					<view class="radio">
						<image v-if="prams.one.type == 1" :src="$fun.imgUrl('/static/apply/c1.png')" mode="widthFix">
						</image>
						<image v-else :src="$fun.imgUrl('/static/apply/c.png')" mode="widthFix"></image>
					</view>
				</view>
				<view class="apply_item">
					<view class="title">
						请选店铺类型
					</view>
				</view>
				<view class="apply_item" @click="change('one', 'types', 0)">
					<view class="title">
						<image :src="$fun.imgUrl('/static/apply/gt.png')" mode="widthFix"></image>
						线上店铺
					</view>
					<view class="radio">
						<image v-if="prams.one.types == 0" :src="$fun.imgUrl('/static/apply/c1.png')" mode="widthFix">
						</image>
						<image v-else :src="$fun.imgUrl('/static/apply/c.png')" mode="widthFix"></image>
					</view>
				</view>
				<view class="apply_item" @click="change('one', 'types', 1)">
					<view class="title">
						<image :src="$fun.imgUrl('/static/apply/gt.png')" mode="widthFix"></image>
						实体店铺
					</view>
					<view class="radio">
						<image v-if="prams.one.types == 1" :src="$fun.imgUrl('/static/apply/c1.png')" mode="widthFix">
						</image>
						<image v-else :src="$fun.imgUrl('/static/apply/c.png')" mode="widthFix"></image>
					</view>
				</view>
				<block v-if="prams.one.types == 1">
					<view class="apply_box2" style="margin: 0;">
						<view class="info_title">
							<view class="t">
								店铺门头
							</view>
							<view class="image_box">
								<image v-if="store_st.mtimage" @click="uploadImage1('mtimage')"
									:src="$fun.imgUrl(store_st.mtimage)">
								</image>
								<image v-else @click="uploadImage1('mtimage')"
									:src="$fun.imgUrl('/static/apply/upload.png')"></image>
							</view>

						</view>
						<view class="info_title">
							<view class="t">
								店铺照片
							</view>
							<view class="image_box">
								<image v-for="(item, index) in store_st.images" :key="index"
									@click="uploadImage1('images', 1, index)" :src="$fun.imgUrl(item)">
								</image>
								<image v-if="store_st.images.length < 6" @click="uploadImage1('images', 1, '1008611')"
									:src="$fun.imgUrl('/static/apply/upload.png')"></image>
							</view>
						</view>
						<view class="box2_item "
							style="flex-direction: column;justify-content: flex-start;align-items: flex-start;border: none;">
							<view class="title"
								style="margin-bottom: 20rpx;display: flex;justify-content: space-between;width: 100%;">
								<text>店铺详细地址</text>
								<u-icon name="map-fill" @click="changeMap(1)" size="40" color="#310FFF"></u-icon>
							</view>
							<view class="radio">
								<textarea name="" @click="changeMap(0)" v-model="store_st.address_info"
									placeholder="请输入您的店铺介绍" id="" cols="30" rows="10"></textarea>
							</view>
							<view class="r">
							</view>
						</view>
					</view>
				</block>
			</view>
			<view class="apply_box2" v-if="current == 1">
				<view class="info_title">
					<view class="t">
						LOGO
					</view>
					<image v-if="prams.two.logo" @click="uploadImage('two', 'logo')" :src="$fun.imgUrl(prams.two.logo)">
					</image>
					<image v-else @click="uploadImage('two', 'logo')" :src="$fun.imgUrl('/static/apply/upload.png')">
					</image>
				</view>
				<view class="box2_item">
					<view class="title">
						所属行业
					</view>
					<view class="radio">
						<input type="text" v-model="category_title" placeholder="请选择您的所属行业" />

					</view>
					<view class="r">
						<image :src="$fun.imgUrl('/static/apply/d.png')" mode="widthFix"></image>
					</view>
					<view class="b" @click="selectClick('two', 'category_id')">

					</view>
				</view>
				<!-- <view class="box2_item">
					<view class="title">
						所在区域
					</view>
					<view class="radio">
						<input type="text" v-model="prams.two.address" placeholder="请选择您的所在区域" />
			
					</view>
					<view class="r">
						<image src="/static/apply/d.png" mode="widthFix"></image>
					</view>
					<view class="b" @click="">
			
					</view>
				</view> -->
				<view class="box2_item">
					<view class="title">
						经营地址
					</view>
					<view class="radio">
						<input type="text" v-model="prams.two.address" placeholder="请选择您的经营地址" />

					</view>
					<view class="r">
						<image :src="$fun.imgUrl('/static/apply/d.png')" mode="widthFix"></image>
					</view>
					<view class="b" @click="lotusAddressData.visible = true">

					</view>
				</view>
				<view class="box2_item">
					<view class="title">
						客服电话
					</view>
					<view class="radio">
						<input type="number" v-model="prams.two.mobile" placeholder="请输入您的客服电话" />

					</view>
					<view class="r">
						<view style="width: 13px;">

						</view>
					</view>
				</view>
				<view class="box2_item">
					<view class="title">
						微信号/手机号
					</view>
					<view class="radio">
						<input type="text" v-model="prams.two.wechat" placeholder="请输入您的微信号/手机号" />

					</view>
					<view class="r">
						<view style="width: 13px;">

						</view>
					</view>
				</view>
				<view class="box2_item "
					style="flex-direction: column;justify-content: flex-start;align-items: flex-start;border: none;">
					<view class="title" style="margin-bottom: 20rpx;">
						店铺介绍
					</view>
					<view class="radio">
						<textarea name="" v-model="prams.two.content" placeholder="请输入您的店铺介绍" id="" cols="30"
							rows="10"></textarea>
					</view>
					<view class="r">
					</view>
				</view>
			</view>
			<view class="apply_box2" v-if="current == 2">
				<view class="box2_item">
					<view class="title">
						公司名称
					</view>
					<view class="radio">
						<input type="text" v-model="prams.three.buname" placeholder="请输入您的公司名称" />

					</view>
					<view class="r">
						<view style="width: 13px;">

						</view>
					</view>
				</view>
				<view class="box2_item">
					<view class="title">
						统一社会信用代码
					</view>
					<view class="radio">
						<input type="text" v-model="prams.three.code" placeholder="请输入您的统一社会信用代码" />

					</view>
					<view class="r">
						<view style="width: 13px;">

						</view>
					</view>
				</view>
				<view class="box2_item">
					<view class="title">
						法定代表人
					</view>
					<view class="radio">
						<input type="text" v-model="prams.three.username" placeholder="请输入您的法定代表人" />

					</view>
					<view class="r">
						<view style="width: 13px;">

						</view>
					</view>
				</view>
				<view class="info_title" style="margin-top: 20rpx;">
					<view class="t">
						身份证信息
					</view>
					<view class="up_list">
						<view class="up_item">
							<image v-if="prams.three.frontimage" @click="uploadImage('three', 'frontimage')"
								:src="$fun.imgUrl(prams.three.frontimage)">
							</image>
							<image v-else @click="uploadImage('three', 'frontimage')"
								:src="$fun.imgUrl('/static/apply/upload.png')">
							</image>
							<view class="t">
								身份证正面照
							</view>
						</view>
						<view class="up_item">
							<image v-if="prams.three.oppositeimage" @click="uploadImage('three', 'oppositeimage')"
								:src="$fun.imgUrl(prams.three.oppositeimage)">
							</image>
							<image v-else @click="uploadImage('three', 'oppositeimage')"
								:src="$fun.imgUrl('/static/apply/upload.png')">
							</image>
							<view class="t">
								身份证反面照
							</view>
						</view>
					</view>
				</view>
				<view class="info_title" style="margin-top: 20rpx;">
					<view class="t">
						营业执照
					</view>
					<image v-if="prams.three.image" @click="uploadImage('three', 'image')"
						:src="$fun.imgUrl(prams.three.image)">
					</image>
					<image v-else @click="uploadImage('three', 'image')" :src="$fun.imgUrl('/static/apply/upload.png')">
					</image>
				</view>
			</view>
			<lotusAddress v-on:choseVal="choseValue" :lotusAddressData="lotusAddressData"></lotusAddress>
			<!-- <u-action-sheet :list="categorytitle" @click="clickCategory" v-model="show"></u-action-sheet> -->
			<view class="net_btn" @click="nextBtn()">
				{{ current == 2 ? '提交' : '下一步' }}
			</view>
			<view style="height: 200rpx;">

			</view>
		</block>
		<block v-else-if="store_status == 0">
			<view class="status_box">
				<view>
					<image :src="$fun.imgUrl('/static/apply/store_1.png')" mode="widthFix"></image>
				</view>
				<view class="status_tip">
					后台正在加急审核中,请耐心等待
				</view>
			</view>
			<view class="net_btn" @click="$fun.jump(``, 5, 0)">
				确定
			</view>
		</block>
		<block v-else-if="store_status == 2">
			<view class="status_box">
				<view>
					<image :src="$fun.imgUrl('/static/apply/store_2.png')" mode="widthFix"></image>
				</view>
				<view class="status_tip">
					失败原因:{{ store_memo }}
				</view>
			</view>
			<view class="net_btn" @click="store_status = ''">
				重新提交
			</view>
		</block>
		<Flame-chooseLocation @confirm="getAddress" v-model="txshow"
			mapkey="Y5PBZ-RUQ6Z-JVSXP-TEXVC-GTVOH-ENBGL"></Flame-chooseLocation>
		<u-modal v-model="show" :title="'选择行业'" :title-style="{ color: '#310FFF' }" :show-confirm-button="false"
			:show-cancel-button="true">
			<view class="content_fh">
				<scroll-view scroll-y="true" style="height: 500rpx;">
					<view class="fh_box_list">
						<view class="fh_item" @click="clickCategory(item)" v-for="(item, index) in category"
							:key="index">
							{{ item.name }}
						</view>
					</view>
				</scroll-view>
			</view>
		</u-modal>
		<view class="fb_flex1" @click="submit(1)" v-if="store_status == ''">
			<text>暂存</text>
			<text>数据</text>
		</view>
	</view>
</template>

<script>
// #ifdef H5
import wx from "@/util/jweixin.js"
// #endif
import lotusAddress from "@/components/Winglau14-lotusAddress/Winglau14-lotusAddress.vue";

export default {
	components: {
		lotusAddress
	},
	data() {
		return {
			store_status: '',
			store_memo: '',
			numList: [{
				name: '选择营业类型'
			}, {
				name: '填写基本信息'
			}, {
				name: '上传相关资料'
			}],
			current: 0,
			show: false,
			show1: false,
			txshow: false,
			category: [],
			categorytitle: [],
			category_title: '',
			lotusAddressData: {
				visible: false,
				provinceName: '',
				cityName: '',
				townName: '',
			},
			store_st: {
				mtimage: '',
				images: [],
				address1: '',
				address_info: '',
				lat: '',
				lng: ''
			},
			prams: {
				one: {
					type: 0,
					types: 0,
				},
				two: {
					logo: '',
					category_id: '',
					address: '',
					mobile: '',
					wechat: '',
					content: ''
				},
				three: {
					buname: '',
					code: '',
					username: '',
					frontimage: '',
					oppositeimage: '',
					image: ''
				}
			}
		}
	},
	onLoad(option) {
		uni.setNavigationBarTitle({
			title: option.name
		})
		this.getBusiness();
	},
	methods: {
		setStep(e) {
			console.log(e)
			if (e != 0) {
				if (e == 1) {
					if (this.getIsok(this.prams.one)) {
						if (this.prams.one.types == 1) {
							if (!this.getIsok(this.store_st)) {
								this.$fun.msg('请完成实体店铺相关信息');
								return;
							}
						}
					} else {
						this.$fun.msg('请选择营业类型');
						return
					}
				} else if (e == 2) {
					if (!this.getIsok(this.prams.two)) {
						this.$fun.msg('请填写完整基本信息');
						return;
					}
				}
			}
			this.current = e;
		},
		getShop(url = 'common/cache?key=store', type = 0) {
			let prams = {};
			this.$fun.ajax.post(url, {}).then(res => {
				if (res.status == 1) {
					if (res.data) {
						this.setM(res);
					}
				}
			})
		},
		changeMap(type) {
			if (type == 1) {
				this.txshow = true;
			} else {
				if (this.store_st.lat == '' && this.store_st.lng == '') {
					this.txshow = true;
				}
			}
		},
		selectClick(key, key1) {
			this.show = true;
		},
		//回传已选的省市区的值
		choseValue(res) {
			if (res.isChose) {
				this.lotusAddressData.visible = res.visible; //visible为显示与关
				this.prams.two.address = `${res.province}-${res.city}-${res.town}`; //region为已选的省市区的值
				this.$forceUpdate()
			} else {
				this.$fun.msg('请完整选择地址')
			}
		},
		clickCategory(item) {
			this.prams.two.category_id = item.id;
			this.category_title = item.name;
			this.show = !this.show
		},

		change(key, key1, value) {
			console.log(this.prams[key])
			this.prams[key][key1] = value
		},
		getIsok(prams) {
			let isok = true;
			Object.getOwnPropertyNames(prams).forEach((key, item) => {
				if (prams[key] === "") {
					console.log(prams[key])
					isok = false
				}
			})
			return isok
		},
		nextBtn() {
			if (this.current == 0) {
				if (this.getIsok(this.prams.one)) {
					if (this.prams.one.types == 1) {
						if (this.getIsok(this.store_st)) {
							this.current += 1
							return;
						} else {
							this.$fun.msg('请完成实体店铺相关信息');
							return;
						}
					} else {
						this.current += 1
						return;
					}
				} else {
					this.$fun.msg('请选择营业类型')
				}
			} else if (this.current == 1) {
				if (this.getIsok(this.prams.two)) {
					this.current += 1
				} else {
					this.$fun.msg('请填写完整信息')
				}
			} else if (this.current == 2) {
				if (this.getIsok(this.prams.three)) {
					this.submit();
				} else {
					this.$fun.msg('请上传完整资料')
				}
			}
		},
		submit(type = 0) {
			let prams = {
				...this.prams.one,
				...this.prams.two,
				...this.prams.three
			};
			if (this.prams.one.types == 1) {
				let store_st = JSON.parse(JSON.stringify(this.store_st));
				store_st.images = store_st.images.join();

				prams = {
					...store_st,
					...this.prams.one,
					...this.prams.two,
					...this.prams.three
				}
			}
			if (type == 0) {
				this.$fun.ajax.post('business/add', {
					...prams
				}).then(res => {
					if (res.status == 1) {
						this.$fun.jump('', 5, 1200)
					}
				})
			} else {
				this.$fun.ajax.post('common/cache?key=store', prams).then(res => {
					if (res.status == 1) {
						this.$fun.msg('暂存成功')
					}
				})
			}

		},
		// 上传头像
		uploadImage1(key, type, index) {
			// 从相册选择图片
			const _this = this;
			uni.chooseImage({
				count: 1,
				sizeType: ['original', 'compressed'],
				sourceType: ['album'],
				success: function (res) {
					_this.handleUploadFile1(res.tempFilePaths, key, type, index);
				}
			});
		},
		// 上传头像
		handleUploadFile1(data, key, type = 0, index = '1008611') {
			const _this = this;
			const filePath = data.path || data[0];
			this.$fun.uploadPic(
				filePath
			).then(res => {
				console.log(type)
				console.log(index)
				this.$fun.msg(res.msg)
				if (res.status == 1) {
					if (type == 0) {
						this.store_st[key] = res.data.url;
					} else {
						if (index == '1008611') {
							this.store_st[key].push(res.data.url);
							console.log(this.store_st[key])
						} else {
							this.store_st[key][index] = res.data.url;
							this.$forceUpdate()
						}
					}
				}
			})
		},
		// 上传头像
		uploadImage(key, key1) {
			// 从相册选择图片
			const _this = this;
			uni.chooseImage({
				count: 1,
				sizeType: ['original', 'compressed'],
				sourceType: ['album'],
				success: function (res) {
					_this.handleUploadFile(res.tempFilePaths, key, key1);
				}
			});
		},
		// 上传头像
		handleUploadFile(data, key, key1) {
			const _this = this;
			const filePath = data.path || data[0];
			this.$fun.uploadPic(
				filePath
			).then(res => {
				this.$fun.msg(res.msg)
				if (res.status == 1) {
					_this.prams[key][key1] = res.data.url;
					console.log(_this.prams[key][key1])
				}
			})
		},
		setM(res) {
			let {
				type,
				types,
				logo,
				category_id,
				address,
				mobile,
				wechat,
				content,
				buname,
				code,
				username,
				frontimage,
				oppositeimage,
				image
			} = res.data;
			this.prams.one = {
				type,
				types,
			}
			this.prams.two = {
				logo,
				category_id,
				address,
				mobile,
				wechat,
				content
			}
			this.prams.three = {
				buname,
				code,
				username,
				frontimage,
				oppositeimage,
				image
			}
			if (category_id) {
				// console.log(category_id)
				for (var i = 0; i < this.category.length; i++) {
					if (this.category[i].id == category_id) {
						this.category_title = this.category[i].name;
					}
				}
			}
			if (types == 1) {
				let {
					mtimage,
					images,
					address1,
					address_info,
					lat,
					lng
				} = res.data;
				this.store_st = {
					mtimage,
					address1,
					address_info,
					lat,
					lng
				}
				this.store_st.images = images.split(',');
			}
		},
		getApply_info() {
			this.$fun.ajax.post('business/getBusiness', {}).then(res => {
				if (res.status == 1) {
					this.setM(res);
				}
			})
		},
		async getBusiness() {
			await this.$fun.ajax.post('business/getStatus', {}).then(res => {
				if (res.status == 1) {
					this.store_status = res.data.status;
					this.store_memo = res.data.memo;
					if (res.data.status == 1) {
						this.getApply_info();
					}
				}
			})
			await this.$fun.ajax.post('category/list', {
				type: 'business'
			}).then(res => {
				if (res.status == 1) {
					console.log(this.category)
					this.category = res.data;
					this.categorytitle = this.$fun.objToArr(res.data, 'name', 'text');
				}
			});
			if (this.store_status == 1) {
				await this.getShop(`business/getBusiness`, 1);
			} else {
				await this.getShop();
			}
		},
		getAddress(e) {
			console.log(e)
			if (e.ad_info) {
				this.store_st.address1 = `${e.ad_info.province}-${e.ad_info.city}-${e.ad_info.district}`;
			} else {
				this.store_st.address1 = `${e.province}-${e.city}-${e.district}`;
			}
			this.store_st.lat = `${e.location.lat}`;
			this.store_st.lng = `${e.location.lng}`;
			this.store_st.address_info = `${e.address}`;
		}
	}
}
</script>

<style lang="scss">
page {
	position: relative;
	background: #FFFFFF;

	.top_step {
		margin-top: 32rpx;
	}

	.apply_box {
		margin: 32rpx 24rpx;

		.apply_item {
			margin-bottom: 24rpx;
			padding: 30rpx 20rpx;
			background: #FFFFFF;
			border-radius: 8rpx 8rpx 8rpx 8rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.title {
				font-weight: 400;
				font-size: 28rpx;
				color: #333333;
				display: flex;
				align-items: center;

				image {
					width: 30.41rpx;
					margin-right: 6rpx;
				}
			}

			.radio {
				image {
					width: 24rpx;
				}
			}
		}
	}

	.apply_box2 {
		margin: 0 24rpx;
		margin-top: 32rpx;
		background: #FFFFFF;
		border-radius: 8rpx 8rpx 8rpx 8rpx;
		padding: 18rpx 24rpx;

		.info_title {
			display: flex;
			flex-direction: column;
			padding-bottom: 34rpx;
			border-bottom: 1rpx solid #E4E4E4;

			.t {
				width: 100%;
				font-weight: bold;
				font-size: 28rpx;
				color: #333333;
			}

			.image_box {
				display: flex;
				flex-wrap: wrap;

				image {
					margin-right: 20rpx;
				}
			}

			.up_list {
				display: flex;

				.up_item {
					margin-right: 20rpx;
					display: flex;
					flex-direction: column;
					align-items: center;

					.t {

						font-weight: 400;
						font-size: 24rpx;
						color: #666666;
					}
				}
			}

			image {
				margin-top: 20rpx;
				width: 178rpx;
				height: 178rpx;
			}
		}

		.box2_item {
			display: flex;
			padding: 30rpx 0;
			align-items: center;
			justify-content: space-between;
			position: relative;
			border-bottom: 1rpx solid #E4E4E4;

			.title {
				width: 230rpx;
				font-weight: 500;
				font-size: 28rpx;
				color: #333333;
			}

			.radio {
				textarea {
					padding: 24rpx;
					width: 654rpx;
					height: 270rpx;
					border-radius: 12rpx 12rpx 12rpx 12rpx;
					border: 2rpx solid #E4E4E4;
				}
			}

			.r {
				image {
					width: 24rpx;
				}
			}

			.b {
				position: absolute;
				top: 0;
				left: 0;
				background: transparent;
				width: 100%;
				height: 100%;
			}
		}

		.fl {
			display: flex;
			flex-direction: column;
			align-items: flex-start;
		}
	}

	.net_btn {
		position: fixed;
		bottom: 80rpx;
		left: calc((100% - 560rpx)/2);
		width: 560rpx;
		height: 76rpx;
		background: #310FFF;
		display: flex;
		align-items: center;
		justify-content: center;
		font-weight: 500;
		font-size: 32rpx;
		color: #FFFFFF;
		border-radius: 38rpx 38rpx 38rpx 38rpx;
	}
}

.status_box {
	width: 100%;
	display: flex;
	align-items: center;
	flex-direction: column;

	image {
		width: 100vw;
	}

	.status_tip {
		display: flex;
		justify-content: center;
		color: #310FFF;
		font-size: 32rpx;
		font-weight: bold;
	}
}

.content_fh {
	height: 500rpx;

	.fh_box_list {
		display: flex;
		flex-direction: column;

		.fh_item {
			padding: 20rpx;
			text-align: center;
		}
	}
}

.fb_flex1 {
	position: fixed;
	right: 32rpx;
	bottom: 350rpx;
	width: 110rpx;
	height: 110rpx;
	background: #310FFF;
	border-radius: 50%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;

	text {
		font-weight: 400;
		font-size: 24rpx;
		color: #FFFFFF;
	}
}
</style>