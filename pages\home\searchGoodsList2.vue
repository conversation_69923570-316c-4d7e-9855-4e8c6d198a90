<template>
	<view class="page">
		<!-- 搜索 -->
		<!-- <view class="search-head">
			<view class="back" @click="onBack">
				<text></text>
			</view>
			<view class="search">
				<text class="iconfont icon-fadajing"></text>
				<input type="text" v-model="keyword" placeholder="搜索商品"  />
			</view>
			<view class="cut" @click="isList = !isList">
				<text class="iconfont" :class="isList?'icon-shitu01':'icon-shitu02'"></text>
			</view>
		</view> -->
		<!-- 筛选 -->
		<view class="screen-info">
			<view class="screen-list">
				<view class="list" @click="chnageTab('weigh')" :class="{'action':screenShow=='weigh'}">
					<text>综合</text>
				</view>
				<view class="list" @click="chnageTab('num')" :class="{'action':screenShow=='num'}">
					<text>浏览量</text>
					<text></text>
				</view>
				<view class="list" @click="chnageTab('new')" :class="{'action':screenShow=='new'}">
					<text>最新</text>
					<text></text>
				</view>
			</view>

			<view class="screen-popup" @click.stop="isScreen = false" v-show="isScreen">
				<view class="synthesize">
					<view class="list">
						<text class="check"></text>
						<text class="title">综合排序</text>
					</view>
					<view class="list">
						<text class="check"></text>
						<text class="title">评论数从高到低</text>
					</view>
				</view>
			</view>
		</view>			
		<view style="height: 80px;">
				
		</view>
		
		<!-- 商品列表 -->
		<!-- 订单tab -->
		<mescroll-body ref="mescrollRef" @init="mescrollInit" @down="downCallback" @up="upCallback" :down="downOption"
			:up="upOption" :top="0">
	<view class="goods-data">
		<view class="goods-list">
			<view :class="'list-li'" v-for="(item,index) in cartList" :key="index" @click="$fun.jump(`./projectD?id=${item.id}`)">
				<view class="thumb">
					<image :src="$fun.imgUrl(item.zimage)" mode="heightFix"></image>
				</view>
				<view class="item">
					<view class="title" style="display: flex;justify-content: space-between;">
						<text class="two-omit" style="font-size: 16px;font-weight: bold;">{{item.title}}</text>
						<!-- <text class="two-omit"
							style="text-align: right;color: #310FFF;">{{item.status==0?'审核中':item.status==1?'已通过':item.status==2?'已拒绝':''}}</text> -->
					</view>
					<view class="title" style="display: flex;justify-content: space-between;margin-top: 20rpx;flex-wrap: wrap;">
						<text class="two-omit" style="color:#959595;">手机号 : {{item.mobile}}</text>
						<text class="two-omit" style="color:#959595;">微信号 : {{item.wechart_code}}</text>
						<!-- <text class="two-omit" style="color:#959595;">浏览量 : {{item.browse}}</text> -->
					</view>
					<view class="title"
						style="display: flex;justify-content: flex-end;margin-top: 20rpx;text-align: right;">
						<text class="two-omit"
							style="color:#959595;margin-right:10px" >{{$u.timeFormat(item.updatetime*1000, 'yyyy-mm-dd hh:MM')}}</text>
					</view>
				</view>
			</view>
		</view>
		</view>
		</mescroll-body>
		<!-- 提示框 -->
		<!-- <DialogBox ref="DialogBox"></DialogBox> -->
	</view>
</template>

<script>
	// 引入mescroll-mixins.js
	import MescrollMixin from "@/components/mescroll-uni/mescroll-mixins.js";
	export default {
		mixins: [MescrollMixin], // 使用mixin
		data() {
			return {
				cartList: [],
				OrderType: 'all',
				id: '',
				mescroll: null, // mescroll实例对象 (此行可删,mixins已默认)
				// 下拉刷新的配置(可选, 绝大部分情况无需配置)
				downOption: {},
				// 上拉加载的配置(可选, 绝大部分情况无需配置)
				upOption: {},
				// 列表视图切换
				isList: true,
				// 筛选弹窗
				isScreen: false,
				// 筛选切换
				screenShow: 'weigh',
				// 抽屉
				isDrawer: false,
				keyword: '',
				goodsList: [],
				swiperList:[],
				navList:[],
				slideNum: 0,
				ListLength: 0,
			}
		},
		watch:{
			keyword(val){
				this.cartList = []
				this.mescroll.resetUpScroll(false);
			}
		},
		onLoad(params) {
			this.keyword = decodeURIComponent(params.keyword || '');
			this.id = params.cateid;
			console.log(params.name)
			// #ifdef APP-PLUS
			uni.setNavigationBarTitle({
				title:params.name
			}) 
			// #endif
		},
		methods: {
			getIndexNavList() {
				this.$fun.ajax.post('category/list', {
					type: 'project'
				}).then(res => {
					if (res.status == 1) {
						this.navList = []
						let data = res.data
						let l = res.data.length / 10 < 1 ? 1 : Math.ceil(res.data.length / 10)
						this.ListLength = l
						let index = 0
						for (var i = 0; i < l; i++) {
							let o = res.data.slice(i * 10, i * 10 + 10)
							console.log(o)
							this.navList.push(o)
						}
					}
				})
			},
			ScrollMenu(e) {
				let scrollLeft = e.target.scrollLeft;
				const query = uni.createSelectorQuery().in(this);
				query.select('.nav').boundingClientRect(data => {
					let wid = e.target.scrollWidth - data.width - (data.left * 2 + 5);
					this.slideNum = (scrollLeft / wid * 300) / 2;
				}).exec();
			},
			onBack() {
				uni.navigateBack();
			},
			/*下拉刷新的回调, 有三种处理方式:*/
			downCallback() {
				this.cartList = []
				this.mescroll.resetUpScroll(false);
			},
			/*上拉加载的回调*/
			upCallback(e) {
				this.mescroll.removeEmpty();
				const data = {
					page: e.num,
					pagenum: e.size,
					cateid: this.id,
					sort: this.screenShow,
					search: this.keyword
				};
				this.$fun.ajax.post('project/index', data).then(res => {
					if (res.status == 1) {
						const curList = res.data;
						if (e.num === 1) {
							this.cartList = [];
							//第一页清空数据重载
							if (curList.length > 0) {
								uni.pageScrollTo({
									scrollTop: 0,
									duration: 200
								});
							}
						}
						this.cartList = this.cartList.concat(curList); //追加新数据
						this.mescroll.endSuccess(curList.length); //结束加载状态
						console.log(curList.length)
					}
				})
			},
			chnageTab(type, t = 1) {
				if (this.screenShow == type) {
					return
				}
				if (t == 1) {
					this.screenShow = type;
				} else {
					if (this.screenShow == 'desc') {
						this.screenShow = 'asc'
					} else {
						this.screenShow = 'desc'
					}
				}
				this.goodsList = []
				this.mescroll.resetUpScroll(false);
				// this.isScreen = !this.isScreen;
			},
		}
	}
</script>

<style scoped lang="scss">
	.page {
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		background-color: #f6f6f6;
	}
	/* 搜索 */
	.search-head {
		position: fixed;
		left: 0;
		top: 0;
		display: flex;
		align-items: center;
		width: 100%;
		height: 100rpx;
		z-index: 10;
		/* #ifdef APP-PLUS */
		height: calc(150rpx + var(--status-bar-height));
		/* #endif */
		/* #ifdef MP */
		height: calc(300rpx + var(--status-bar-height));
		padding-top: var(--status-bar-height);
		/* #endif */
		background-color: #ffffff;

		.back {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 10%;
			height: 100%;

			text {
				width: 20rpx;
				height: 20rpx;
				border-left: 2rpx solid #555555;
				border-bottom: 2rpx solid #555555;
				transform: rotate(45deg);
			}
		}

		.search {
			display: flex;
			align-items: center;
			width: 76%;
			height: 60rpx;
			background-color: #f6f6f6;
			border-radius: 60rpx;
			padding: 0 4%;

			text {
				font-size: 34rpx;
				color: #c0c0c0;
			}

			input {
				width: 90%;
				height: 100%;
				font-size: 26rpx;
				color: #212121;
				margin-left: 10rpx;
			}
		}

		.cut {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 10%;
			height: 100%;

			text {
				font-size: 38rpx;
				color: #555555;
			}
		}
	}

	/* 筛选 */
	.screen-info {
		position: fixed;
		left: 0;
		top: 0rpx;
		z-index: 11;
		/* #ifdef APP-PLUS */
		top: 0;
		/* #endif */
		/* #ifdef MP */
		top: calc(200rpx + var(--status-bar-height));
		/* #endif */
		width: 100%;
		height: 100rpx;
		background-color: #ffffff;

		.screen-list {
			display: flex;
			align-items: center;
			width: 100%;
			height: 100%;
			justify-content: space-between;

			.list {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 25%;
				height: 100%;

				text {
					font-size: 26rpx;
					color: #555555;
				}

				.icon_z {
					font-size: 24rpx;
					transform: rotate(90deg) scale(0.7);
				}

				.icon_j {
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-items: center;
					width: 40rpx;
					height: 100%;

					.up {
						transform: rotate(-90deg) scale(0.7);
						margin-bottom: -15rpx;
						//margin-left: 8rpx;
					}

					.down {
						transform: rotate(90deg) scale(0.7);
					}
				}

				.icon_s {
					font-size: 24rpx;
					margin-left: 10rpx;
					// transform: scale(0.7);
				}
			}

			.action {
				text {
					color: $base;
				}
			}
		}

		// 弹出层
		.screen-popup {
			position: fixed;
			left: 0;
			top: 200rpx;
			/* #ifdef APP-PLUS */
			top: 250rpx;
			/* #endif */
			/* #ifdef MP */
			top: 300rpx;
			/* #endif */
			width: 100%;
			height: 100%;
			background-color: rgba(0, 0, 0, 0.3);

			// 综合
			.synthesize {
				padding: 0 20rpx;
				height: 200rpx;
				background-color: #f6f6f6;
				border-radius: 0 0 20rpx 20rpx;

				.list {
					display: flex;
					align-items: center;
					width: 100%;
					height: 80rpx;

					.check {
						display: inline-block;
						width: 20rpx;
						height: 10rpx;
						border-left: 4rpx solid $base;
						border-bottom: 4rpx solid $base;
						border-radius: 4rpx;
						transform: rotate(-45deg);
					}

					.title {
						font-size: 26rpx;
						color: #555555;
						margin-left: 20rpx;
					}
				}
			}
		}
	}
	.goods-data {
		width: 100%;
	
		.goods-list {
			padding: 0 25rpx;
			border-radius: 20rpx;
			overflow: hidden;
			.list-view {
				float: left;
				width: 49%;
				height: 560rpx;
				background-color: #ffffff;
				border-radius: 10rpx;
				margin-right: 2%;
				margin-bottom: 20rpx;
				overflow: hidden;
				.thumb {
					width: 100%;
					//height: 300rpx;
					overflow: hidden;
					image {
	                    height: 350rpx;
					}
				}
				.item {
					width: 100%;
					.title {
						padding: 20rpx;
						text {
							width: 100%;
							color: #212121;
							font-size: 26rpx;
						}
					}
					.price {
						padding: 0 20rpx;
						.retail-price {
							display: flex;
							align-items: flex-end;
							width: 100%;
							height: 40rpx;
							.min {
								display: inline-block;
								font-size: 24rpx;
								color: $base;
								font-weight: bold;
								transform: scale(0.7);
							}
							.max {
								font-size: 28rpx;
								color: $base;
								font-weight: bold;
							}
							.tag {
								position: relative;
								background-color: $base;
								border-radius: 4rpx;
								margin-left: 10rpx;
								text {
									display: inline-block;
									color: #ffffff;
									font-size: 24rpx;
									transform: scale(0.7);
								}
							}
							.tag:before {
								position: absolute;
								left: -6rpx;
								top: 0;
								content: '';
								width: 0;
								height: 0;
								border-top: 0rpx solid transparent;
								border-right: 10rpx solid $base;
								border-bottom: 6rpx solid transparent;
							}
						}
						.vip-price {
							display: flex;
							align-items: flex-end;
							width: 100%;
							height: 40rpx;
							.min {
								display: inline-block;
								font-size: 24rpx;
								color: #212121;
							}
							.max {
								font-size: 24rpx;
								color: #212121;
							}
						}
					}
				}
			}
			.list-view:nth-child(2n) {
				margin-right: 0;
			}
			// 列表
			.list-li {
				display: flex;
				align-items: center;
				width: 100%;
				height: 300rpx;
				background-color: #ffffff;
				.thumb {
					display: flex;
					align-items: center;
					justify-content: center;
					width: 30%;
					height: 100%;
					image {
						width: 200rpx;
						height: 200rpx;
						border-radius: 10rpx;
					}
				}
				.item {
					display: flex;
					flex-direction: column;
					justify-content: center;
					width: 70%;
					height: 100%;
					border-bottom: 2rpx solid #f6f6f6;
					.title {
						padding: 20rpx;
						text {
							width: 100%;
							color: #212121;
							font-size: 26rpx;
						}
					}
					.price {
						padding: 0 20rpx;
						.retail-price {
							display: flex;
							align-items: flex-end;
							width: 100%;
							height: 40rpx;
							.min {
								display: inline-block;
								font-size: 24rpx;
								color: $base;
								font-weight: bold;
								transform: scale(0.7);
							}
							.max {
								font-size: 28rpx;
								color: $base;
								font-weight: bold;
							}
							.tag {
								position: relative;
								background-color: $base;
								border-radius: 4rpx;
								margin-left: 10rpx;
								text {
									display: inline-block;
									color: #ffffff;
									font-size: 24rpx;
									transform: scale(0.7);
								}
							}
							.tag:before {
								position: absolute;
								left: -6rpx;
								top: 0;
								content: '';
								width: 0;
								height: 0;
								border-top: 0rpx solid transparent;
								border-right: 10rpx solid $base;
								border-bottom: 6rpx solid transparent;
							}
						}
						.vip-price {
							display: flex;
							align-items: flex-end;
							width: 100%;
							height: 40rpx;
							.min {
								display: inline-block;
								font-size: 24rpx;
								color: #212121;
							}
							.max {
								font-size: 24rpx;
								color: #212121;
							}
						}
					}
				}
			}
		}
	}
	.basis-lg {
		padding-top: 0;
		border-radius: 20rpx 0 0 20rpx;
		flex-basis: 80% !important;
		.serve {
			/* #ifdef APP-PLUS */
			padding-top: 50rpx;
			/* #endif */
			/* #ifdef MP */
			padding-top: 140rpx;
			/* #endif */
			padding-left: 20rpx;
			padding-right: 20rpx;
			background-color: #ffffff;
			.title {
				display: flex;
				align-items: center;
				width: 100%;
				height: 80rpx;
				text {
					color: #212121;
					font-size: 28rpx;
				}
			}
			.serve-list {
				display: flex;
				flex-wrap: wrap;
				padding: 20rpx 0;
				.list {
					display: flex;
					align-items: center;
					justify-content: center;
					width: 30%;
					height: 60rpx;
					border-radius: 60rpx;
					margin-right: 4%;
					background-color: #f6f6f6;
					text {
						color: #555555;
						font-size: 24rpx;
					}
				}
				.list:nth-child(3n) {
					margin-right: 0;
				}
				.action {
					background-color: $rgba-03;
					border: 2rpx solid $base;
					text {
						color: $base;
					}
				}
			}
		}
		.price-screen {
			padding: 0 4%;
			background-color: #ffffff;
			margin-top: 20rpx;
			.title {
				display: flex;
				align-items: center;
				width: 100%;
				height: 80rpx;
				text {
					color: #212121;
					font-size: 28rpx;
				}
			}
			.price-section {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 100%;
				height: 80rpx;
				input {
					width: 180rpx;
					height: 50rpx;
					border-radius: 50rpx;
					font-size: 24rpx;
					color: #555555;
					background-color: #f6f6f6;
				}
				text {
					display: flex;
					width: 60rpx;
					height: 2rpx;
					background-color: #f6f6f6;
					margin: 0 20rpx;
				}
			}
		}
		.operation-btn {
			position: absolute;
			left: 0;
			bottom: 0;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 4%;
			width: 100%;
			height: 100rpx;
			background-color: #ffffff;
			.btn {
				display: flex;
				justify-content: center;
				align-items: center;
				width: 46%;
				height: 70rpx;
				background-color: #ffffff;
				border: 2rpx solid #f6f6f6;
				border-radius: 70rpx;
				// margin-left: 5%;
				text {
					color: #212121;
					font-size: 26rpx;
				}
			}
			.action {
				background-color: $base;
				text {
					color: #ffffff;
				}
			}
		}
	}
	/* banner */
		.banner{
			margin-top: 20rpx;
			margin-bottom: 20rpx;
			padding: 0 25rpx;
			height: 143px;
			// margin-bottom: 30rpx;
			// margin: -200rpx auto 20rpx;
			border-radius: 10rpx;
			overflow: hidden;
			.screen-swiper{
				height: 143px;
				min-height: 100% !important;
				image{
					height: 143px;
					border-radius: 10rpx;
				}
			}
		}
		/* 菜单导航 */
		.menu-nav{
			position: relative;
			width: 100%;
			// height: 300rpx;
			margin:30rpx auto;
			background: #ffffff;
			.nav-list{
				white-space: nowrap; 
				// height: 270rpx;
				width: 100%;
				.nav{
					display: inline-block;
					display: flex;
					flex-direction: inherit !important;
					flex-wrap: wrap;
					justify-content: flex-start;
					width: 100vw;
					// height: 280rpx;
				}
				.list{
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-items: center;
					width: calc(100vw / 5);
					height: 130rpx;
					margin-bottom: 20rpx;
					image{
						width: 75rpx;
						height: 75rpx;
						border-radius: 100%;
					}
					text{
						font-size: 26rpx;
						color: #363636;
						margin-top: 10rpx;
					}
				}
			}
			.indicator{
				position: absolute;
				left: 0;
				bottom: 0;
				display: flex;
				justify-content: center;
				align-items: center;
				width: 100%;
				height: 30rpx;
				.plan{
					position: relative;
					width: 100rpx;
					height: 8rpx;
					border-radius: 8rpx;
					background-color: #e1e1e1;
					.bar{
						position: absolute;
						width: 50%;
						height: 100%;
						border-radius: 6rpx;
						background-color: $base;
					}
				}
			}
		}
		
</style>
