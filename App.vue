<script>
	import Vue from 'vue';
	import websocketUtils from '@/util/websocketUtils.js'
	export default {
		globalData: {
			isconnent: false
		},
		onLaunch: function() {
			// console.log('App Launch');
			// #ifdef APP-PLUS
			// var w = plus.webview.open(
			// 	'hybrid/html/advertise/advertise.html',
			// 	'本地地址',
			// 	{ top: 0, bottom: 0, zindex: 999 },
			// 	'fade-in',
			// 	500
			// );
			// //设置定时器，4s后关闭启动广告页
			// setTimeout(function() {
			// 	plus.webview.close(w);
			// }, 5000);
			// #endif
		},
		onShow: function() {
			// if (!this.globalData.isconnent && uni.getStorageSync('token')) {
			// 	new websocketUtils("*******", 10);
			// }
		},
		onHide: function() {
			console.log('App Hide');
		}
	};
</script>

<style lang="scss">
	/*每个页面公共css */
	@import "uview-ui/index.scss";
	@import 'colorui/main.css';
	@import 'colorui/icon.css';
	@import 'style/FontStyle.css';

	/* #ifndef H5 || MP-WEIXIN */
	@font-face {
		font-family: 'YaHei';
		src: url('/static/ttf/Microsoft-YaHei.ttf');
	}

	@font-face {
		font-family: 'Source';
		src: url('/static/ttf/SourceHanSansCN-Regular.ttf');
	}

	/* #endif */

	body {
		-webkit-text-size-adjust: none;
	}

	/* 超出一行省略号 */
	.one-omit {
		white-space: nowrap;
		/*规定段落中的文本不进行换行*/
		overflow: hidden;
		/*内容会被修剪，并且其余内容是不可见的。*/
		text-overflow: ellipsis;
		/*显示省略号来代表被修剪的文本*/
	}

	.clearfix::after {
		content: "";
		display: table;
		clear: both;
	}

	.two-omit {
		display: -webkit-box;
		-webkit-line-clamp: 2;
		overflow: hidden;
		text-overflow: ellipsis;
		-webkit-box-orient: vertical;
	}

	img {
		width: 100%;
	}

	.add-btn {
		background: #3C6D49;
		color: #fff;
		width: 90%;
	}
</style>