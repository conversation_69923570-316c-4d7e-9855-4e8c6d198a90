<template>
	<view class="content">
		<view class="heradr_top">
			<view :style="{height: system_info.statusBarHeight+'px'}"></view>
			<view class="header_name">
				项目XXX
			</view>
			<view class="head-search">
				<view class="search" @click="$fun.jump(`/pages/home/<USER>">

					<view class="hint">
						<text class="min">热门内容</text>
					</view>
					<view class="icon">
						<image :src="$fun.imgUrl('/static/fdj_ico.png')" mode=""></image>
					</view>
				</view>
			</view>
			<view class="heradr_radius">

			</view>
		</view>
		<view class="menu-nav">
			<scroll-view scroll-x @scroll="ScrollMenu" class="nav-list">
				<view :style="{display: 'flex',width:`calc(100vw*${ListLength})`}">
					<view class="nav" ref="nav" :style="'flex-direction:column'" v-for="(item,index) in navList"
						:key="index">
						<view class="list" v-for="(item2,index2) in item" @click="navListJump(item2)" :key="item2">
							<image :src="$fun.imgUrl(item2.image)" mode=""></image>
							<text>{{item2.name}}</text>
						</view>
					</view>
				</view>
			</scroll-view>
			<view class="indicator" v-if="navList.length>1">
				<view class="plan">
					<view class="bar" :style="'left:'+slideNum+'%'"></view>
				</view>
			</view>
		</view>
		<view class="content_box">
			<view class="banner1">
				<swiper class="screen-swiper square-dot" autoplay indicator-dots="true" circular="true" autoplay="true"
					interval="5000" duration="500">
					<swiper-item v-for="(item,index) in swiperList" :key="index">
						<image :src="$fun.imgUrl(item.image)">
						</image>
					</swiper-item>
				</swiper>
			</view>
			<view class="inform" v-if="newsList.length>0">
				<view class="inform-info">
					<view class="picture" @click="$fun.jump(`./messageList1?type=news&name=公告`)">
						<image :src="$fun.imgUrl(`/static/gg_ico.png`)" mode="widthFix"></image>
					</view>
					<view class="info">
						<swiper class="swiper" :circular="true" :vertical="true" :indicator-dots="false"
							:autoplay="true" :interval="3000" :duration="1000">
							<swiper-item v-for="(item,index) in newsList" :key="index">
								<view class="swiper-item" @click="$fun.jump(`./messageInfo?id=${item.id}&name=公告详情`)">
									<text class="one-omit">{{item.title}}</text>
									<text class="icon iconfont icon-more"></text>
								</view>
							</swiper-item>
						</swiper>
					</view>
				</view>
			</view>
			<view class="class_box">
				<image v-for="(item,index) in 4" :key="index" :src="$fun.imgUrl(`/static/indexb.png`)" mode=""></image>
			</view>
			<view class="class_box1">
				<image class="l" :src="$fun.imgUrl(`/static/indexb.png`)" mode=""></image>
				<view class="r">
					<image v-for="(item,index) in 2" :key="index" :src="$fun.imgUrl(`/static/indexb.png`)" mode="">
					</image>
				</view>
			</view>
			<view class="selected_box">
				<image :src="$fun.imgUrl(`/static/selected_img.png`)" mode="widthFix"></image>
				<view class="selected_shop">
					<view class="selected_item" v-for="(item,index) in 3" :key="index">
						<image :src="$fun.imgUrl(`/static/u-bg.png`)" mode=""></image>
						<view class="shop_info">
							<view class="title">
								海尔（Haier）335升星门海...
							</view>
							<view class="price">
								¥ 1620
							</view>
							<view class="ys">
								已售100件
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="selected_box1">
				<view class="selected_img">
					<image :src="$fun.imgUrl(`/static/selected_img1.png`)" mode="widthFix"></image>
					<view class="shop_top">

					</view>
				</view>
				<view class="selected_shop" v-for="(item,index) in 3" :key="index">
					<view class="shop_store">
						<view class="l">
							<image :src="$fun.imgUrl(`/static/d.png`)" mode="widthFix"></image>
							<view class="store_info">
								<view class="name">
									我是名称
								</view>
								<u-rate active-color="#FFA623" :current="3.7" :disabled="true"></u-rate>
							</view>
						</view>
						<view class="r">
							进店
						</view>
					</view>
					<view class="shop_box">
						<view class="selected_item" v-for="(item,index) in 3" :key="index">
							<image :src="$fun.imgUrl(`/static/u-bg.png`)" mode=""></image>
							<view class="shop_info">
								<view class="title">
									海尔（Haier）335升星门海...
								</view>
								<view class="price">
									¥ 1620
								</view>
								<view class="ys">
									已售100件
								</view>
							</view>
						</view>

					</view>
				</view>
			</view>
			<view class="banner2">
				<swiper class="screen-swiper square-dot" autoplay indicator-dots="true" circular="true" autoplay="true"
					interval="5000" duration="500">
					<swiper-item v-for="(item,index) in swiperList" :key="index">
						<image :src="$fun.imgUrl(item.image)">
						</image>
					</swiper-item>
				</swiper>
			</view>
			<view class="recommend-info" v-if="goodsList.length>0">
				<view class="goods-list">
					<view class="list" v-for="(item,index) in goodsList"
						@click="$fun.jump(`/pages/home/<USER>" :key="index">
						<view class="pictrue">
							<image :src="$fun.imgUrl(item.image)"></image>
						</view>
						<view class="shop">

							<view class="">
								<view class="title-tag">
									<view class="tag">
										{{item.name}}
									</view>
								</view>

							</view>
							<view class="price-info">
								<view class="user-price">
									<text class="min">￥</text>
									<text class="max">{{item.money}}</text>
								</view>
								<view class="tag_text">
									销量{{item.num}}
								</view>
							</view>
						</view>
					</view>
				</view>

			</view>
		</view>
		<view style="height: 90rpx;"></view>
		<TabBar :tabBarShow="0"></TabBar>
	</view>
</template>

<script>
	import TabBar from '@/components/TabBar/TabBar.vue';
	// 引入mescroll-mixins.js
	import MescrollMixin from "@/components/mescroll-uni/mescroll-mixins.js";
	import {
		checkUpdate
	} from "@/components/yzhua006-update/js/app-update-check.js";
	export default {
		mixins: [MescrollMixin], // 使用mixin
		components: {
			TabBar,
		},
		data() {
			return {
				system_info: {},
				swiperList: [],
				navList: [],
				goodsList: [],
				newsList: [],
				ListLength: 1,
				slideNum: 0
			}
		},
		onLoad(option) {
			this.system_info = uni.getSystemInfoSync();
			this.init()
		},
		onShow() {

		},
		mounted() {

		},

		methods: {
			/**
			 * 初始化
			 */
			async init(page) {
				await this.getIndexSwipter();
				await this.getIndexNavList();
				await this.getIndexNews();
				await this.getIndexGoodsList();
			},
			/**
			 * 首页轮播图
			 */
			getIndexSwipter() {
				this.$fun.ajax.post('News/lists', {
					type: 'index'
				}).then(res => {
					if (res.status == 1) {
						this.swiperList = res.data
					}
				})
			},
			/**
			 * 获取分类
			 */
			getIndexNavList() {
				this.$fun.ajax.post('category/list', {
					type: 'index'
				}).then(res => {
					if (res.status == 1) {
						this.navList = []
						res.data = [...res.data, ...res.data, ...res.data, ...res.data, ...res.data, ...res.data,
							...res.data, ...res.data,
						]
						let l = res.data.length / 10 < 1 ? 1 : Math.ceil(res.data.length / 10)
						this.ListLength = l
						let index = 0
						for (var i = 0; i < l; i++) {
							let o = res.data.slice(i * 10, i * 10 + 10)
							console.log(o)
							this.navList.push(o)
						}
					}
				})
			},
			/**
			 * 获取公告
			 */
			getIndexNews() {
				this.$fun.ajax.post('News/lists', {
					type: 'news'
				}).then(res => {
					if (res.status == 1) {
						this.newsList = res.data
					}
				})
			},
			/**
			 * 获取商品列表
			 */
			getIndexGoodsList() {
				this.$fun.ajax.post('goods/list', {
					cid: 'hot',
				}).then(res => {

					if (res.status == 1) {
						if (this.page == 1) {
							this.goodsList = []
						}
						const curList = res.data;
						this.goodsList = this.goodsList.concat(curList); //追加新数据
						if (curList.length > 0) {
							this.page++
						}
					}
				})
			},
			ScrollMenu(e) {
				let scrollLeft = e.target.scrollLeft;
				const query = uni.createSelectorQuery().in(this);
				query.select('.nav').boundingClientRect(data => {
					let wid = e.target.scrollWidth - data.width - (data.left * 2 + 5);
					this.slideNum = (scrollLeft / wid * 300) / 2;
				}).exec();
			},
			
		}
	};
</script>

<style lang="scss">
	page {
		background: #FFFFFF;

		.content {


			.heradr_top {
				position: relative;
				padding-bottom: 32rpx;
				width: 100%;
				background: #310FFF;

				.header_name {
					padding: 20rpx 32rpx;
					font-weight: 400;
					font-size: 32rpx;
					color: #333333;
					// margin-bottom: 40rpx;
				}

				.head-search {
					padding: 20rpx 32rpx;
					z-index: 10;
					display: flex;
					align-items: center;
					justify-content: space-between;

					.search {
						display: flex;
						align-items: center;
						justify-content: space-between;
						width: 100%;
						padding: 0 20rpx;
						height: 70rpx;
						box-sizing: border-box;
						background: #FFFFFF;
						border-radius: 10rpx;
						border-radius: 38rpx 38rpx 38rpx 38rpx;

						.icon {
							display: flex;
							align-items: center;
							margin-right: 20rpx;

							image {
								width: 40rpx;
								height: 40rpx;
							}
						}

						.hint {
							display: flex;
							align-items: center;

							.max {
								font-size: 30rpx;
								font-weight: bold;
								color: #FFFFFF;
							}

							.min {
								font-size: 24rpx;
								color: #AAAAAA;
								margin-left: 10rpx;
							}
						}
					}
				}

				.heradr_radius {
					position: absolute;
					bottom: -1px;
					border-radius: 40rpx 40rpx 0rpx 0rpx;
					height: 32rpx;
					background: #FFFFFF;
					width: 100%;
				}
			}

			/* 菜单导航 */
			.menu-nav {
				position: relative;
				width: 100%;

				.nav-list {
					white-space: nowrap;
					width: 100%;

					.nav {
						display: inline-block;
						display: flex;
						flex-direction: inherit !important;
						flex-wrap: wrap;
						justify-content: flex-start;
						width: 100vw;
					}

					.list {
						display: flex;
						flex-direction: column;
						justify-content: center;
						align-items: center;
						width: calc(100vw / 5);
						height: 130rpx;
						margin-bottom: 20rpx;

						image {
							width: 76rpx;
							height: 76rpx;
						}

						text {
							font-size: 26rpx;
							color: #333333;
							margin-top: 5rpx;
						}
					}
				}

				.indicator {
					position: absolute;
					left: 0;
					bottom: 0;
					display: flex;
					justify-content: center;
					align-items: center;
					width: 100%;
					height: 30rpx;

					.plan {
						position: relative;
						width: 100rpx;
						height: 8rpx;
						border-radius: 8rpx;
						background-color: #e1e1e1;

						.bar {
							position: absolute;
							width: 50%;
							height: 100%;
							border-radius: 6rpx;
							background-color: #310FFF;
						}
					}
				}
			}

			.content_box {
				padding: 20rpx 24rpx;

				.banner1 {
					height: 258rpx;
					border-radius: 10rpx;
					overflow: hidden;

					.screen-swiper {
						height: 258rpx;
						min-height: 100% !important;

						image {
							width: 100%;
							height: 258rpx;
							border-radius: 10rpx;
						}
					}
				}

				/* 通知 */
				.inform {
					margin: 20rpx 0;

					.inform-info {
						display: flex;
						padding: 0 20rpx;
						height: 72rpx;
						border-radius: 37rpx 37rpx 37rpx 37rpx;
						border: 1rpx solid #929DAC;

						.picture {
							margin-right: 10rpx;
							display: flex;
							align-items: center;

							image {
								width: 86rpx;
								height: 40rpx;
							}
						}

						.info {
							width: 90%;
							height: 100%;

							.swiper {
								width: 100%;
								height: 100%;

								.swiper-item {
									display: flex;
									align-items: center;
									justify-content: space-between;
									width: 100%;
									height: 100%;

									text {
										font-size: 28rpx;
										color: #C6C6C6;
									}

									.icon {
										color: #d9cfcf !important;
									}
								}
							}
						}
					}
				}

				.class_box {
					display: flex;
					justify-content: space-between;
					flex-wrap: wrap;

					image {
						width: 344rpx;
						height: 192rpx;
						margin-bottom: 20rpx;
					}
				}

				.class_box1 {
					display: flex;
					justify-content: space-between;
					flex-wrap: wrap;

					image.l {
						width: 344rpx;
						height: 436rpx;
					}

					.r {
						display: flex;
						justify-content: space-between;
						flex-direction: column;

						image {
							width: 344rpx;
							height: 192rpx;
							margin: 0;
						}
					}


				}

				.selected_box {
					width: 100%;

					image {
						width: 100%;
						margin: 0;
						padding: 0;
					}

					.selected_shop {
						width: calc(100% - 12rpx);
						margin: 0 auto;
						margin-top: -9rpx;
						padding: 9rpx 20rpx;
						background: #8554E6;
						border-radius: 0 0 16rpx 16rpx;
						display: flex;
						justify-content: space-between;
						padding-bottom: 20rpx;

						.selected_item {

							image {
								width: 208rpx;
								height: 208rpx;
								border-radius: 8rpx 8rpx 0rpx 0rpx;
							}

							.shop_info {
								margin-top: -9rpx;
								padding: 12rpx;
								width: 208rpx;
								background: #FFFFFF;
								border-radius: 0 0 8rpx 8rpx;

								.title {
									font-weight: 500;
									font-size: 20rpx;
									color: #333333;
									line-height: 23rpx;
									width: 100%;
									overflow: hidden;
									text-overflow: ellipsis;
									display: -webkit-box;
									-webkit-line-clamp: 2;
									-webkit-box-orient: vertical;
								}

								.price {
									margin-top: 10rpx;
									font-weight: 500;
									font-size: 24rpx;
									color: #FF512E;
								}

								.ys {
									font-weight: 500;
									font-size: 16rpx;
									color: #B6B6B6;
								}
							}
						}
					}
				}

				.selected_box1 {
					margin-top: 20rpx;
					width: 100%;
					position: relative;

					.selected_img {
						position: relative;

						image {
							width: 100%;

						}

						.shop_top {
							position: absolute;
							bottom: 0;
							left: 0;
							width: 100%;
							height: 40rpx;
							background: #FFF0EB;
							border-radius: 16rpx 16rpx 0 0;
						}

					}

					.selected_shop {
						padding: 9rpx 20rpx;
						background: #FFF0EB;
						z-index: 10;
						border-radius: 0 0 16rpx 16rpx;

						padding-bottom: 20rpx;

						.shop_box {
							display: flex;
							flex-wrap: wrap;
							justify-content: space-between;
						}

						.shop_store {
							width: 100%;
							display: flex;
							justify-content: space-between;
							align-items: center;

							.l {
								display: flex;
								align-items: center;

								image {
									width: 88rpx;
									height: 88rpx;
								}

								.store_info {
									margin-left: 20rpx;
									margin-bottom: 20rpx;

									.name {
										font-weight: 500;
										font-size: 28rpx;
										color: #333333;
									}
								}
							}


							.r {
								width: 98rpx;
								height: 40rpx;
								color: #FFFFFF;
								background: #ED271D;
								text-align: center;
								line-height: 40rpx;
								font-weight: 500;
								font-size: 24rpx;
								color: #FFFFFF;
								border-radius: 20rpx 20rpx 20rpx 20rpx;
							}
						}

						.selected_item {

							image {
								width: 208rpx;
								height: 208rpx;
								border-radius: 8rpx 8rpx 0rpx 0rpx;
							}

							.shop_info {
								margin-top: -9rpx;
								padding: 12rpx;
								width: 208rpx;
								background: #FFFFFF;
								border-radius: 0 0 8rpx 8rpx;

								.title {
									font-weight: 500;
									font-size: 20rpx;
									color: #333333;
									line-height: 23rpx;
									width: 100%;
									overflow: hidden;
									text-overflow: ellipsis;
									display: -webkit-box;
									-webkit-line-clamp: 2;
									-webkit-box-orient: vertical;
								}

								.price {
									margin-top: 10rpx;
									font-weight: 500;
									font-size: 24rpx;
									color: #FF512E;
								}

								.ys {
									font-weight: 500;
									font-size: 16rpx;
									color: #B6B6B6;
								}
							}
						}
					}
				}

				.banner2 {
					margin-top: 20rpx;
					height: 258rpx;
					border-radius: 10rpx;
					overflow: hidden;

					.screen-swiper {
						height: 258rpx;
						min-height: 100% !important;

						image {
							width: 100%;
							height: 258rpx;
							border-radius: 10rpx;
						}
					}
				}

				.goods-list {
					margin-top: 20rpx;
					display: flex;
					flex-wrap: wrap;
					justify-content: space-between;
					// padding: 0 20rpx;

					.list {
						// padding: 20rpx 0;
						width: 49%;
						margin-bottom: 20rpx;
						background: #FFFFFF;
						box-shadow: 0rpx 2rpx 8rpx 0rpx rgba(0, 0, 0, 0.25);
						border-radius: 12rpx 12rpx 12rpx 12rpx;
						display: flex;
						flex-direction: column;

						.pictrue {
							display: flex;
							justify-content: center;
							align-items: center;
							// padding: 16rpx;

							image {
								width: 342rpx;
								height: 342rpx;
								border-radius: 12rpx 12rpx 0rpx 0rpx;
							}
						}

						.shop {
							display: flex;
							justify-content: space-between;
							flex-direction: column;
						}

						.title-tag {
							width: 100%;
							padding: 20rpx;

							.tag {
								font-family: Source;
								font-weight: 500;
								font-size: 28rpx;
								color: #333333;
								font-style: normal;
								text-transform: none;
								overflow: hidden;
								text-overflow: ellipsis;
								display: -webkit-box;
								-webkit-line-clamp: 2;
								-webkit-box-orient: vertical;
								white-space: normal;


								text {
									font-size: 24rpx;
									color: #FFFFFF;
									padding: 4rpx 16rpx;
									background: linear-gradient(to right, $base, $change-clor);
									border-radius: 6rpx;
									margin-right: 10rpx;
								}
							}
						}

						.tag_text {
							padding: 0 20rpx;
							font-family: Source;
							font-weight: 400;
							font-size: 20rpx;
							font-size: 20rpx;
							color: #AAAAAA;
							text-align: left;
							font-style: normal;
							text-transform: none;
						}

						.price-info {
							margin: 10rpx 20rpx;
							height: 60rpx;
							box-sizing: border-box;
							border-radius: 8rpx 8rpx 8rpx 8rpx;
							display: flex;
							justify-content: space-between;

							.user-price {
								padding: 0 10rpx;
								display: flex;
								align-items: baseline;
								margin-right: 10rpx;

								text {
									color: #FFFFFF;
								}

								.min {
									font-family: Source;
									font-weight: 500;
									font-weight: 500;
									font-size: 28rpx;
									color: #FF512E;
									text-align: left;
									font-style: normal;
									text-transform: none;
								}

								.max {
									font-family: Source;
									font-weight: 500;
									font-weight: 500;
									font-size: 28rpx;
									color: #FF512E;
									text-align: left;
									font-style: normal;
									text-transform: none;
								}
							}

							.tag_text {
								font-weight: 500;
								font-size: 28rpx;
								color: #B6B6B6;
							}

							.vip-price {
								height: 60rpx;
								box-sizing: border-box;
								display: flex;
								justify-content: center;
								font-family: Source;
								align-items: center;
								padding-right: 14rpx;
								border-radius: 132rpx 132rpx 132rpx 132rpx;

								image {
									width: 26rpx;
									height: 26rpx;
									margin-right: 10rpx;
								}

								text {
									font-size: 24rpx;
									color: #FFFFFF;
								}
							}
						}
					}
				}
			}
		}
	}
</style>