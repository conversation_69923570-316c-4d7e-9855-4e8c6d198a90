<template>
	<view class="shop_p">
		<view class="shop_p_top">
			<view class="t_img">
				<image :src="$fun.imgUrl('/static/p/t.png')" mode="widthFix"></image>

			</view>
			<view class="shop_b">
				<image class="shop_img" :src="$fun.imgUrl(info.image)" mode="widthFix"></image>
				<view class="shop_info">
					<view class="title_box">
						<view class="title">
							第{{ info.pin.id }}期
						</view>
						<view class="f_title">
							{{ info.name }}
						</view>
					</view>
					<view class="shop_pr">
						<image :src="$fun.imgUrl(`/static/p/t1.png`)" mode="widthFix"></image>
						<view class="price_b">
							<view class="top_title">
								<view class="title">
									专享价
								</view>
								<view class="title1">
									已售{{ info.stock }}+
								</view>
							</view>
							<view class="top_title">
								<view class="title" style="font-size: 28rpx;">
									¥{{ info.money }}
								</view>
								<view class="title1">
									¥{{ info.price }}
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>

		</view>
		<view class="p_box">
			<view class="title">
				还差<text style="color: #FD081D;">{{ pinInfo.num }}</text>人可瓜分红包
			</view>
			<view class="p_t_img">
				<image style="border:1px solid #FB4807;border-radius: 50%; " :src="$fun.imgUrl(pinInfo.tuan.avatar)">
				</image>
				<view class="name">
					{{ pinInfo.tuan.nickname }}
				</view>


			</view>
			<view class="img_b">
				<image v-for="(item, index) in pinInfo.pin" :key="index" style="border:1px solid #FB4807 "
					:src="$fun.imgUrl(item.avatar)" mode=""></image>
				<image v-for="(item, index) in getRemainingSlots()" :key="'add' + index"
					:src="$fun.imgUrl(`/static/p/add.png`)" mode=""></image>
			</view>
			<!-- #ifdef MP-WEIXIN -->
			<button class="btn_share" open-type="share">
				邀请好友加入

			</button>
			<!-- #endif -->
			<!-- #ifndef MP-WEIXIN -->
			<view class="btn_share" @click="shareShop">
				邀请好友加入

			</view>
			<!-- #endif -->
		</view>
		<view class="main" style="margin-top: 24rpx;">
			<view class="subject">

				<view class="r">
					<image :src="$fun.imgUrl(`/static/p/time.png`)" mode=""></image>
					<text>实时动态</text>
				</view>
			</view>
			<view class="body">
				<swiper class="swiper-container" 
					:circular="true" 
					:vertical="true" 
					:autoplay="true" 
					:interval="2000"
					:duration="1000"
					:display-multiple-items="10">
					<swiper-item v-for="(item, index) in pinInfo.users" :key="item.id + index" class="body_item">
						<view class="l">
							<image :src="$fun.imgUrl(`/static/p/l.png`)" mode="widthFix"></image>
							<view class="line">{{ item.nickname }}</view>
						</view>
						<view class="t">
							{{ item.createtime ? getT(item.createtime) : getT(new Date().getTime() / 1000) }}
						</view>
					</swiper-item>
				</swiper>
			</view>
			<!-- <maoScroll :data="pinInfo.users" :showNum="showNum" :lineHeight="lineHeight" :animationScroll="animationScroll"
					:animation="animation">
					<template v-slot="{ line }">
						<view class="body_item">

							<view class="l">
								<image :src="$fun.imgUrl(`/static/p/l.png`)" mode="widthFix"></image>
								<view class="line">{{ line.nickname }}</view>
							</view>
							<view class="t"> {{ line.createtime?getT(line.createtime): getT(new Date().getTime() / 1000) }}</view>
						</view>
					</template>
</maoScroll> -->
		</view>
	</view>
	</view>
</template>

<script>
import {
	sumo_time
} from "@/util/sumo_time.js"
import maoScroll from '@/components/mao-scroll/mao-scroll.vue';
export default {
	components: {
		maoScroll
	},
	data() {
		return {
			count: 30,
			showNum: 12,
			lineHeight: 70,
			animationScroll: 800,
			animation: 2000,
			list: [],
			id: '',
			info: {
				pin: {}
			},
			userinfo: {},
			pinInfo: {
				pin: [],
				tuan: {},
				users: []
			}
		}
	},


	mounted() {

	},
	onLoad(option) {
		this.id = option.id;
		this.userinfo = uni.getStorageSync('userinfo');

	},
	mounted() {
		this.getinfo();
	},
	methods: {
		shareShop() {
			// // #ifdef MP-WEIXIN
			// // 显示微信分享菜单
			// uni.showShareMenu({
			// 	withShareTicket: true,
			// 	menus: ['shareAppMessage', 'shareTimeline']
			// })
			// // #endif
			// #ifndef MP-WEIXIN
			let copy = this.$baseUrl + `/#/pages/home/<USER>
			this.$fun.copy(copy)
			// #endif
		},

		onShareAppMessage() {
			return {
				title: this.info.name,
				path: `/pages/home/<USER>
				imageUrl: this.$fun.imgUrl(this.info.image),
				success(res) {
					uni.showToast({
						title: '分享成功'
					})
				},
				fail(err) {
					uni.showToast({
						title: '分享失败',
						icon: 'none'
					})
				}
			}
		},

		onShareTimeline() {
			return {
				title: this.info.name,
				query: `id=${this.id}&tuanId=${this.info.pin.id}`,
				imageUrl: this.$fun.imgUrl(this.info.image)
			}
		},

		getinfo() {
			this.$fun.ajax.post('goods/content', {
				id: this.id,

			}).then(res => {
				if (res.status == 1) {
					this.swiperList = res.data.images
					this.info = res.data
					this.$fun.ajax.post('order/pinlist', {
						tid: res.data.pin.id,
					}).then(res => {
						if (res.status == 1) {
							this.pinInfo = res.data;
							// for (let i = 0; i < this.pinInfo.users.length; i++) {
							// 	let obj = {};
							// 	obj.title = this.pinInfo.users[i].nickname.substring(0, 2) + '**'
							// 	obj.createtime = new Date().getTime() / 1000;
							// 	this.list.push(obj);
							// }
						}
					})
				}


			})

		},
		getT(t) {
			let tm = this.$u.timeFormat(t, 'yyyy-mm-dd  hh:MM:ss')
			return sumo_time(tm)
		},
		getRemainingSlots() {
			const total = this.pinInfo.numall || 0;
			const current = this.pinInfo.pin.length || 0;
			const remaining = total - current;
			return remaining > 0 ? remaining : 0;
		},
	}
}
</script>

<style lang="scss">
page {
	background: #FFE7BB;
	padding: 24rpx;
	box-sizing: border-box;

	.shop_p_top {
		margin-top: 20rpx;
		position: relative;
		padding: 18rpx 24rpx;
		background: #FFFFFF;
		border-radius: 16rpx 16rpx 16rpx 16rpx;

		.t_img {
			position: absolute;
			top: -22rpx;
			left: 0;
			right: 0;
			margin: auto;
			width: 510rpx;

			image {
				width: 100%;
			}
		}

		.shop_b {
			margin-top: 100rpx;
			width: 100%;
			display: flex;
			align-items: center;

			.shop_img {
				width: 200rpx;
				height: 200rpx;
				border-radius: 4rpx 4rpx 4rpx 4rpx;
				margin-right: 16rpx;
			}

			.shop_info {
				width: calc(100% - 220rpx);
				height: 100%;
				display: flex;
				flex-direction: column;
				justify-content: space-between;

				.title_box {
					.title {
						font-weight: 400;
						font-size: 28rpx;
						color: #333333;
						-webkit-line-clamp: 2;
						-webkit-box-orient: vertical;
						overflow: hidden;
						text-overflow: ellipsis;
					}

					.f_title {
						margin-top: 8rpx;
						font-weight: 400;
						font-size: 24rpx;
						color: #AAAAAA;
						-webkit-line-clamp: 2;
						-webkit-box-orient: vertical;
						overflow: hidden;
						text-overflow: ellipsis;
					}
				}

				.shop_pr {
					margin-top: 20rpx;
					width: 100%;
					position: relative;

					image {
						width: 100%;
					}

					.price_b {
						width: 100%;
						height: 100%;
						top: 0;
						position: absolute;
						display: flex;
						justify-content: center;
						flex-direction: column;

						.top_title {
							padding: 2rpx 18rpx;
							display: flex;
							align-items: center;

							.title {
								font-weight: 400;
								font-size: 16rpx;
								color: #FFFFFF;
							}

							.title1 {
								margin-left: 16rpx;
								font-weight: 400;
								font-size: 16rpx;
								color: rgba(255, 255, 255, .5);
							}

						}
					}
				}
			}
		}
	}

	.p_box {
		margin-top: 20rpx;
		padding: 24rpx 30rpx;
		background: #FFFFFF;
		border-radius: 16rpx 16rpx 16rpx 16rpx;

		.title {
			text-align: center;
			font-weight: 500;
			font-size: 36rpx;
			color: #333333;
		}

		.p_t_img {
			margin-top: 38rpx;
			margin-bottom: 16rpx;
			display: flex;
			flex-direction: column;
			align-items: center;

			image {
				width: 148rpx;
				height: 148rpx;
			}

			.name {
				font-weight: 400;
				font-size: 28rpx;
				color: #333333;
			}

		}

		.img_b {
			display: flex;
			justify-content: center;

			image {
				width: 90rpx;
				height: 90rpx;
				border-radius: 50%;
				margin-right: 48rpx;
			}

			image:nth-child(3) {

				margin-right: 0;
			}

		}

		.btn_share {
			margin: 48rpx 30rpx;
			margin-bottom: 20rpx;
			height: 90rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			color: #FFFFFF;
			background: linear-gradient(90deg, #FB4807 0%, #FD081D 100%);
			border-radius: 45rpx 45rpx 45rpx 45rpx;
		}
	}

	.main .subject {
		height: 92rpx;
		font-size: 36rpx;
		box-sizing: border-box;
		padding-left: 20rpx;
		color: #fff;
		background: #FFF1DA;
		display: flex;
		align-items: center;
		position: relative;

		.r {
			width: calc(100% - 260rpx);
			height: 92rpx;
			position: absolute;
			top: 0;
			left: 0;
			z-index: 1;
			background: #FF5C37;
			display: flex;
			align-items: center;
			box-sizing: border-box;
			padding-left: 20rpx;
			border-radius: 10rpx 0 300rpx 10rpx;

			image {
				width: 40rpx;
				height: 40rpx;
			}

			text {
				font-family: Source;
				font-weight: 500;
				font-size: 32rpx;
				color: #FFFFFF;
				margin-left: 20rpx;
			}
		}
	}

	.main .body {
		padding: 20rpx;
		background-color: #FFFFFF;
		border-radius: 0 0 16rpx 16rpx;
		.swiper-container {
			height: 600rpx;
		}
	}

	.main .body .line {
		height: 60rpx;
		line-height: 60rpx;
	}

	.body_item {
		display: flex;
		height: 60rpx !important;
		justify-content: space-between;
		align-items: center;

		.l {
			width: 85%;
			display: flex;
			align-items: center;

			image {
				margin-right: 15rpx;
				width: 40rpx;
			}

			.line {
				font-family: Source;
				font-size: 24rpx;
				color: #333333;
				width: calc(100% - 55rpx);
				overflow: hidden;
				/* 确保文本超出容器时会被裁剪 */
				white-space: nowrap;
				/* 确保文本在一行内显示 */
				text-overflow: ellipsis;
				/* 使用省略号表示文本溢出 */
			}
		}

		.t {
			color: #AAAAAA;
			font-family: Source;
			font-weight: 400;
			font-size: 24rpx;
		}

	}

}
</style>