<template>
	<view class="chat">
		<view class="chat_search">
			<u-search placeholder="搜索" v-model="keyword" @change="searchUser" :show-action="false"
				input-align="center"></u-search>
		</view>
		<view class="chat_list">
			<block v-for="(item,index) in list" :key="index">
				<chatItem v-if="item.search==1"  @clear="clearSerach" :list="item"></chatItem>
			</block>
		</view>
		<uni-popup ref="popup" type="bottom" border-radius="10px 10px 0 0">
			<view class="" style="width: 100vw;">
				<view class="chat_san">

				</view>
				<view class="chat_add_box">
					<view class="add_item" @click="nextClick('./group/creat_group')">
						<u-icon name="weixin-fill" size="35"></u-icon>
						<view class="add_title">
							发起群聊
						</view>
					</view>
					<view class="add_item" @click="nextClick(`./add_friend/add_friend`)">
						<u-icon name="man-add-fill" size="35"></u-icon>
						<view class="add_title">
							添加好友
						</view>
					</view>
					<view class="add_item">
						<u-icon name="scan" size="35"></u-icon>
						<view class="add_title">
							扫一扫
						</view>
					</view>
				</view>
			</view>
		</uni-popup>
		<backHome></backHome>
		<view style="height: 100rpx;">

		</view>
		<TabBar :tabBarShow="0"></TabBar>

	</view>
</template>

<script>
	import TabBar from '@/components/TabBar1.vue';
	import chatItem from '../components/chat-item/chat-item.vue';
	export default {
		components: {
			chatItem,
			TabBar
		},
		data() {
			return {
				keyword: '',
				show: false,
				customStyle: {
					background: 'transparent'
				},
				options: [{
						text: '收藏',
						style: {
							backgroundColor: '#007aff'
						}
					},
					{
						text: '删除',
						style: {
							backgroundColor: '#dd524d'
						}
					}
				],
				list: []
			};
		},
		onShow() {
			this.list = [];
			this.getMsgList()
		},
		methods: {
			clearSerach() {
				this.keyword = '';
				this.$refs.popup.close();
			},
			nextClick(e) {
				this.keyword = '';
				this.$refs.popup.close();
				this.$fun.jump(e)
			},
			searchUser(e) {
				this.getMsgList(e)
			},
			getMsgList(search = '') {
				this.$fun.ajax.post('chat/chatList', {
					search
				}).then(res => {
					if (res.status == 1) {
						let list = []
						Object.keys(res.data).forEach(key => {
							list.push(res.data[key])
						})
						this.list = list.reverse()
					}
				})
			}
		},
		onNavigationBarButtonTap(e) {
			if (e.text == '+') {
				if (!this.show) {
					this.show = true;
					this.$refs.popup.open('top');
				} else {
					this.show = false;
					this.$refs.popup.close()
				}
			}
		},
	}
</script>

<style lang="scss">
	page {
		background: #FFFFFF;

		.chat_search {
			margin: 32rpx;
		}

		.chat_list {
			padding: 18rpx 32rpx;
		}

		.chat_san {
			width: 50rpx;
			height: 50rpx;
			position: fixed;
			top: 15rpx;
			right: 30rpx;
			background: #FFFFFF;
			transform: rotate(45deg);
		}



		// 添加好友
		.chat_add_box {
			position: fixed;
			top: 20rpx;
			right: 20rpx;
			display: flex;
			flex-direction: column;
			padding: 46rpx 50rpx;
			background: #FFFFFF;
			border-radius: 12rpx 12rpx 12rpx 12rpx;

			.add_item {
				display: flex;
				margin-bottom: 40rpx;
				align-items: center;

				.add_title {
					margin-left: 10rpx;
					font-weight: 400;
					font-size: 28rpx;
					color: #333333;
					line-height: 33rpx;
					text-align: center;
					font-style: normal;
					text-transform: none;
				}
			}

			.add_item:nth-child(3) {
				margin-bottom: 0rpx;
			}
		}
	}
</style>