<template>
	<view class="page">
		<mescroll-body ref="mescrollRef" @down="downCallback" @up="upCallback" :down="downOption" :up="upOption"
			:top="0">
		<view class="activity-list">
			<view class="list" v-for="(item,index) in goodsList" :key="index" @click="$fun.jump(`./messageInfo?id=${item.id}&name=${name}详情`)">
				<view class="date">
					<text>{{$u.timeFormat(item.createtime, 'yyyy-mm-dd hh:MM')}}</text>
				</view>
				<view class="item">
					<view class="title">
						<text class="one-omit">{{item.name}}</text>
					</view>
					<view class="pictrue" v-if="item.image">
						<image :src="$fun.imgUrl(item.image)" mode=""></image>
						<!-- <view class="hint">
							<text>活动结束</text>
						</view> -->
					</view>
					<view class="describe">
						<text class="two-omit">{{item.title}}</text>
					</view>
				</view>
			</view>
		</view>
		</mescroll-body>
		<view style="height: 120rpx;">
			
		</view>
		
	</view>
</template>

<script>
	import MescrollMixin from "@/components/mescroll-uni/mescroll-mixins.js";
	export default {
		mixins: [MescrollMixin], // 使用mixin
		data() {
			return {
				type:'',
				mescroll: null, // mescroll实例对象 (此行可删,mixins已默认)
				// 下拉刷新的配置(可选, 绝大部分情况无需配置)
				downOption: {},
				// 上拉加载的配置(可选, 绝大部分情况无需配置)
				upOption: {},
				goodsList:[],
				name:''
			};
		},
		onLoad(option) {
			this.type=option.type
			this.name=option.name
			
			uni.setNavigationBarTitle({
				title:option.name
			})
		},
		methods:{
			/*下拉刷新的回调, 有三种处理方式:*/
			downCallback() {
				this.goodsList = []
				this.mescroll.resetUpScroll(false);
			},
			/*上拉加载的回调*/
			async upCallback(e) {
				this.mescroll.removeEmpty();
				const data = {
					page: e.num,
					pagenum: e.size,
					type:this.type
				};
				this.getMessageList(data)
				
			},
			getMessageList(e){
				this.$fun.ajax.post('news/lists',e).then(res=>{
					if (res.status == 1) {
						const curList = res.data;
						if (e.num === 1) {
							this.goodsList = [];
							//第一页清空数据重载
							if (curList.length > 0) {
								uni.pageScrollTo({
									scrollTop: 0,
									duration: 200
								});
							}
						}
						this.goodsList = this.goodsList.concat(curList); //追加新数据
						this.mescroll.endSuccess(curList.length); //结束加载状态
					}
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	.page{
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		background-color: #f6f6f6;
	}
	
	
	.activity-list{
		padding: 20rpx 4%;
		.list{
			width: 100%;
			margin-bottom: 10px;
			.date{
				display: flex;
				align-items: center;
				justify-content: center;
				width: 100%;
				height: 100rpx;
				text{
					padding: 10rpx 30rpx;
					background-color: rgba(0,0,0,0.2);
					color: #FFFFFF;
					font-size: 26rpx;
					border-radius: 10rpx;
				}
			}
			.item{
				padding: 0 4%;
				// height: 500rpx;
				background-color: #FFFFFF;
				border-radius: 10rpx;
				.title{
					display: flex;
					align-items: center;
					width: 100%;
					height: 100rpx;
					text{
						font-size: 32rpx;
						color: #222222;
					}
				}
				.pictrue{
					position: relative;
					width: 100%;
					height: 300rpx;
					image{
						width: 100%;
						height: 100%;
					}
					.hint{
						position: absolute;
						left: 0;
						top: 0;
						display: flex;
						align-items: center;
						justify-content: center;
						width: 100%;
						height: 100%;
						background-color: rgba(0,0,0,0.3);
						text{
							font-size: 32rpx;
							color: #FFFFFF;
						}
					}
				}
				.describe{
					display: flex;
					align-items: center;
					width: 100%;
					height: 100rpx;
					text{
						font-size: 28rpx;
						color: #959595;
					}
				}
			}
		}
	}
	
</style>
