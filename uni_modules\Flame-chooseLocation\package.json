{"id": "Flame-chooseLocation", "displayName": "基于腾讯地图的H5选择位置组件(choseLocation)", "version": "1.0.1", "description": "基于腾讯地图的H5选择位置组件,主要解决uni.chooseLoaction在pc端不能使用的问题", "keywords": ["位置，选择位置，IP定位"], "repository": "https://gitee.com/flameboy-git/flame-chooselocation.git", "engines": {"HBuilderX": "^3.4.10"}, "dcloudext": {"type": "component-vue", "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": ""}, "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y"}, "client": {"Vue": {"vue2": "y", "vue3": "u"}, "App": {"app-vue": "n", "app-nvue": "n"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "y", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "n", "阿里": "n", "百度": "n", "字节跳动": "n", "QQ": "n", "钉钉": "n", "快手": "n", "飞书": "n", "京东": "n"}, "快应用": {"华为": "n", "联盟": "n"}}}}}