import Vue from 'vue';
import ajax from "./req/ajax.js";
import env from "./env.js"; // 跳转 
import AD from "./ad.js"
import uniCopy from '@/components/xb-copy/uni-copy.js'

function jump(url, type = 0, time = 0) {
	// type 0 正常跳转
	// type 1 跳转底部
	if (url == '' && type == 0) {
		msg('开发中...');
		return;
	}
	if (type == 0) {
		setTimeout(() => {
			uni.navigateTo({
				url
			});
		}, time);
	} else if (type == 1) {
		setTimeout(() => {
			uni.switchTab({
				url
			});
		}, time);
	} else if (type == 2) {
		setTimeout(() => {
			uni.redirectTo({
				url
			});
		}, time);
	} else if (type == 3) {
		setTimeout(() => {
			uni.reLaunch({
				url
			});
		}, time);
	} else if (type == 4) {
		console.log(11111111111)
		if (time == 0) {
			time = 1500;
		}

		setTimeout(() => {
			uni.navigateBack();
		}, time);
	} else if (type == 5) {
		setTimeout(() => {
			uni.navigateBack();
		}, time);
	} else if (type == 6) {
		uni.navigateBack();
	}
} // 图片处理 


function imgUrl(image) {

	//统一提示方便全局修改
	if (image) {
		if (process.env.NODE_ENV === 'development') {
			if (image.slice(0, 4) == '/sta') {
				return image;
			}
		}
		if (image.slice(0, 4) == 'http') {
			return image;
		} else if (image.slice(0, 4) == 'data') {
			return image;
		} else {
			return env.dev.baseUrl + image;
		}
	}

	return image;
} // 提示信息


const msg = (title, duration = 1500, mask = false, icon = 'none') => {
	if (Boolean(title) === false) {
		return;
	}

	uni.showToast({
		title,
		duration,
		mask,
		icon
	});
}; // 提示信息


const Console = text => {
	console.log(text);
}; // 保存

const formatRichText = (html) => {
	let newContent = html.replace(/\<p/gi, '<p style="display: flex;flex-direction: column;"').replace(/\<img/gi,
		'<img style="width:100%;height:auto;margin:0rpx;padding:0rpx;vertical-align:bottom;"');

	return newContent;

}
const saveScene = str => {
	uni.setStorageSync('saveScene', str);
}; // 微信授权


const wxLogin = (url = '') => {
	if (process.env.NODE_ENV === 'development') {
		return env.dev.baseUrl + "/third/connect/wechat" + url;
	} else {
		return env.prod.baseUrl + "/third/connect/wechat" + url;
	}
};
const getUrlCode = () => {

	var url = location.href //获取打开的公众号的路径
	//console.log(url,'url')
	// this.winUrl = url
	var theRequest = new Object()
	if (url.indexOf("?") != -1) {
		var str = url.split("?")
		var strs = [str[1].split("&")[0], str[1].split("&")[1], str[1].split("&")[2]]
		for (var i = 0; i < strs.length; i++) {
			theRequest[strs[i].split("=")[0]] = (strs[i].split("=")[1])
		}
	}
	return theRequest
};

// 微信授权
const baseUrl = () => {
	if (process.env.NODE_ENV === 'development') {
		return env.dev.baseUrl;
	} else {
		return env.prod.baseUrl;
	}
}; // 获取app版本


const getVersion = () => {
	// #ifdef APP-PLUS
	return plus.runtime.version; // #endif
}; // 判断手机号


const iphoneTest = num => {
	if (!/(^1[2|3|4|5|6|7|8|9][0-9]{9}$)/.test(num)) {
		return false;
	} else {
		return true;
	}
}; // 保存  sesstion


const setSesstion = (str, arr) => {
	return uni.setStorageSync(str, arr);
}; // 获取  sesstion


const getSesstion = str => {
	return uni.getStorageSync(str);
}; // 清楚  sesstion


const clearSesstion = str => {
	return uni.clearStorage();
}; // 分页      str 请求接口 _this 当前this page当前页数 pageSize  


const paging = (str, _this, prams, con) => {
	return new Promise((resolve, reject) => {
		uni.showLoading({
			title: "加载中..."
		});

		ajax.post(str, prams).then(res => {
			uni.hideLoading();

			if (res.status == 1) {
				if (res.data.length > 0) {
					if (_this.page == 1) {
						_this[con] = [];
					}
					let data = res.data;
					_this[con] = _this[con].concat(res.data);
					_this.page++;
				} else {
					if (_this.page != 1) {
						msg('没有更多数据了...');
					}
				}
			}
			resolve(res)
		}).catch(err => {
			reject(err)
		})

	})
}; // 拆分对象  转数组



const objToArr = (arr, str) => {
	let data = [];

	for (var i = 0; i < arr.length; i++) {
		data.push(arr[i][str]);
	}

	return data;
}; // time1  1593790157203  TO>>>  刚刚 || 3分钟前 || 1小时前 || 1天前 || 1周前 || 2月前 || 2019年11月11日
// time2  1593790157203  TO>>>  上午12:01 || 昨天 || 星期日 || 2019-11-11
// time3  1593790157203  TO>>>  2019-11-11 12:01
// time4  2019/11/11 || 2019/11/11 12:03 TO>>> 1593790157203
// time5  (1593790157203,1593790157205) （开始与结束时间戳）  TO>>> 00:40 || 01:25 || 01:20:11 （时长）


const time1 = timer => {
	var arrTimestamp = (timer + '').split('');

	for (var start = 0; start < 13; start++) {
		if (!arrTimestamp[start]) {
			arrTimestamp[start] = '0';
		}
	}

	timer = arrTimestamp.join('') * 1;
	var minute = 1000 * 60;
	var hour = minute * 60;
	var day = hour * 24;
	var halfamonth = day * 15;
	var month = day * 30;
	var now = new Date().getTime();
	var diffValue = now - timer; // 如果本地时间反而小于变量时间

	if (diffValue < 0) {
		return '不久前';
	} // 计算差异时间的量级


	var monthC = diffValue / month;
	var weekC = diffValue / (7 * day);
	var dayC = diffValue / day;
	var hourC = diffValue / hour;
	var minC = diffValue / minute; // 数值补0方法

	var zero = function (value) {
		if (value < 10) {
			return '0' + value;
		}

		return value;
	}; // 使用


	if (monthC > 12) {
		// 超过1年，直接显示年月日
		return function () {
			var date = new Date(timer);
			return date.getFullYear() + '年' + zero(date.getMonth() + 1) + '月' + zero(date.getDate()) + '日';
		}();
	} else if (monthC >= 1) {
		return parseInt(monthC) + "月前";
	} else if (weekC >= 1) {
		return parseInt(weekC) + "周前";
	} else if (dayC >= 1) {
		return parseInt(dayC) + "天前";
	} else if (hourC >= 1) {
		return parseInt(hourC) + "小时前";
	} else if (minC >= 1) {
		return parseInt(minC) + "分钟前";
	}

	return '刚刚';
};

const time2 = timer => {
	var arrTimestamp = (timer + '').split('');

	for (var start = 0; start < 13; start++) {
		if (!arrTimestamp[start]) {
			arrTimestamp[start] = '0';
		}
	}

	var Etime = arrTimestamp.join('') * 1; //参数时间

	var Etimer = new Date(Etime);
	var Ntime = new Date().getTime(); //现在时间

	var Ntimer = new Date();
	var Eyear = Etimer.getFullYear(); //取得4位数的年份

	var Emonth = Etimer.getMonth() + 1 < 10 ? '0' + (Etimer.getMonth() + 1) : Etimer.getMonth() +
		1; //取得日期中的月份，其中0表示1月，11表示12月

	var Edate = Etimer.getDate() < 10 ? '0' + Etimer.getDate() : Etimer.getDate(); //返回日期月份中的天数（1到31）

	var Eweek = Etimer.getDay(); //返回日期月份中的周

	var Ehour = Etimer.getHours() < 10 ? '0' + Etimer.getHours() : Etimer.getHours(); //返回日期中的小时数（0到23）

	var Eminute = Etimer.getMinutes() < 10 ? '0' + Etimer.getMinutes() : Etimer.getMinutes(); //返回日期中的分钟数（0到59）

	var today = new Date(new Date().setHours(0, 0, 0, 0)) / 1000 * 1000; //今天0点时间戳

	var yesterday = today - 24 * 60 * 60 * 1000; //昨天0点时间戳

	var beforeWeek = today - 7 * 24 * 60 * 60 * 1000; //一周前0点时间戳
	// 今天的时间

	if (today < Etime) {
		if (Ehour < 12) {
			return '上午' + Ehour + ':' + Eminute;
		} else {
			return '下午' + Ehour + ':' + Eminute;
		}
	} // 昨天的时间


	if (yesterday < Etime && Etime < today) {
		return '昨天';
	} // 一周内的时间


	if (beforeWeek < Etime) {
		if (Eweek == 0) {
			return "星期日";
		} else if (Eweek == 1) {
			return "星期一";
		} else if (Eweek == 2) {
			return "星期二";
		} else if (Eweek == 3) {
			return "星期三";
		} else if (Eweek == 4) {
			return "星期四";
		} else if (Eweek == 5) {
			return "星期五";
		} else if (Eweek == 6) {
			return "星期六";
		}
	} // 更早的时间


	return Eyear + '-' + Emonth + '-' + Edate;
};

const time3 = timer => {
	var arrTimestamp = (timer + '').split('');

	for (var start = 0; start < 13; start++) {
		if (!arrTimestamp[start]) {
			arrTimestamp[start] = '0';
		}
	}

	var Etime = arrTimestamp.join('') * 1; //参数时间

	var Etimer = new Date(Etime);
	var Eyear = Etimer.getFullYear(); //取得4位数的年份

	var Emonth = Etimer.getMonth() + 1 < 10 ? '0' + (Etimer.getMonth() + 1) : Etimer.getMonth() +
		1; //取得日期中的月份，其中0表示1月，11表示12月

	var Edate = Etimer.getDate() < 10 ? '0' + Etimer.getDate() : Etimer.getDate(); //返回日期月份中的天数（1到31）

	var Ehour = Etimer.getHours() < 10 ? '0' + Etimer.getHours() : Etimer.getHours(); //返回日期中的小时数（0到23）

	var Eminute = Etimer.getMinutes() < 10 ? '0' + Etimer.getMinutes() : Etimer.getMinutes(); //返回日期中的分钟数（0到59）

	return Eyear + "-" + Emonth + "-" + Edate + " " + Ehour + ":" + Eminute;
};

const time4 = timer => {
	var date = timer.replace(/\//g, '-');
	date = timer.replace(/：/g, ':');
	var Etimer = new Date(date);
	var Etime = Etimer.getTime();
	return Etime;
};

const uploadPic = (src, name) => {
	const path = '/api/common/upload';
	let url = '';

	if (process.env.NODE_ENV === 'development') {
		url += env.dev.baseUrl + path;
	} else {
		url += env.prod.baseUrl + path;
	}

	return new Promise(resolve => {
		uni.uploadFile({
			url,
			filePath: src,
			name: 'file',
			header: {
				token: uni.getStorageSync('token')
			},
			success: uploadFileRes => {
				resolve(JSON.parse(uploadFileRes.data));
			}
		});
	});
};

const time5 = (sTime, eTime) => {
	if (sTime.length == 13) sTime = (sTime - 0) / 1000;
	if (eTime.length == 13) eTime = (eTime - 0) / 1000;
	var lang = 0;
	var interval = Math.ceil((eTime - sTime) / 1000); // 一分钟以内

	if (interval < 60) {
		if (interval < 10) {
			lang = '0' + interval;
		} else {
			lang = interval;
		}

		return '00:' + lang;
	} // 一小时以内


	if (60 < interval && interval < 3600) {
		var langM = Math.floor(interval / 60);
		var langS = Math.floor(interval - langM * 60);
		if (langM < 10) langM = '0' + langM;
		if (langS < 10) langS = '0' + langS;
		return langM + ':' + langS;
	} // 一小时以上


	if (3600 < interval) {
		var langH = Math.floor(interval / 3600);
		var langM = Math.floor(interval / 60 - langH * 60);
		var langS = Math.floor(interval - langH * 3600 - langM * 60);
		if (langH < 10) langH = '0' + langH;
		if (langM < 10) langM = '0' + langM;
		if (langS < 10) langS = '0' + langS;
		return langH + ':' + langM + ':' + langS;
	}
};

const str2unicode = str => {
	const res = []
	for (let i = 0; i < str.length; i++) {
		res.push(str.charCodeAt(i))
	}
	return res.join()
}
const lookAdever = (str, aid,) => {
	return new Promise((resolve, reject) => {
		AD.show({
			adpid: 1410212499, // HBuilder 基座测试广告位
			adType: "RewardedVideo"
		}, (res) => {
			// 用户点击了【关闭广告】按钮
			if (res && res.isEnded) {
				// msg('完成')
				// 正常播放结束
				// console.log("onClose " + res.isEnded);
				ajax.post(str, {
					aid
				}).then((res => {
					resolve(res)
				}))
			} else {
				resolve(res);
				// 播放中途退出
				console.log("onClose " + res.isEnded);
				msg('中途退出')
			}

			// 在此处理服务器回调逻辑
		}, (err) => {
			resolve(err);
			// 广告加载错误
			msg('广告加载失败请重新点击')
		})
	})
};
const lookVideo = str => {
	AD.show({
		adpid: 1786364432, // HBuilder 基座测试广告位
		adType: "RewardedVideo"
	}, (res) => {
		// 用户点击了【关闭广告】按钮
		if (res && res.isEnded) {
			this.lookVideo()
			// 正常播放结束
			console.log("onClose " + res.isEnded);
		} else {
			// 播放中途退出
			console.log("onClose " + res.isEnded);
			msg('中途退出')
		}

		// 在此处理服务器回调逻辑
	}, (err) => {
		console.log(err)
		// 广告加载错误
		msg('广告加载失败请重新点击')
	})
};
const lookImg = str => {
	console.log(str)
	uni.previewImage({
		urls: [imgUrl(str)] //预览图片的地址，必须要数组形式，如果不是数组形式就转换成数组形式就可以
	})
};
const copy = text => {
	console.log(text)
	uniCopy({
		content: text,
		success: (res) => {
			uni.showToast({
				title: res,
				icon: 'none'
			})
		},
		error: (e) => {
			uni.showToast({
				title: e,
				icon: 'none',
				duration: 3000,
			})
		}
	})
};
const splitImg = imgArr => {
	return imgArr.split(',')
};
const isAssetTypeAnImage = imgUrl => {
	var index = imgUrl.lastIndexOf(".");
	//获取后缀
	var ext = imgUrl.substr(index + 1);
	return [
		'png', 'jpg', 'jpeg', 'bmp', 'gif', 'webp', 'psd', 'svg', 'tiff'
	].
		indexOf(ext.toLowerCase()) !== -1;
};
// 下载视频
const uploadVideo = url => {
	// #ifdef H5
	location.href = url
	// #endif
	// #ifdef APP-PLUS
	// 提醒用户下载中
	uni.showToast({
		title: "下载中",
		icon: "loading"
	})
	console.log(url)
	// 1 将远程文件下载到小程序的内存中
	uni.downloadFile({
		url,
		success: (res) => {
			console.log(res)
			// 2 成功下载后而且状态码为200时将视频保存到本地系统
			if (res.statusCode === 200) {
				uni.saveVideoToPhotosAlbum({
					filePath: res.tempFilePath
				})
				uni.hideLoading();
				// 提示用户下载成功
				uni.showToast({
					title: "下载成功",
					icon: "none"
				});
			}
			// 如果该资源不可下载或文件格式出错则提示用户
			else {
				uni.showToast({
					title: "资源格式错误，请联系管理员"
				});
			}
		},
		fail: (err) => {
			console.log(err)
			// 下载失败提醒
			uni.hideLoading();
			uni.showToast({
				title: "下载失败"
			})
		}
	})
	// #endif
}
module.exports = {
	jump,
	// 页面跳转
	imgUrl,
	// img  判断
	msg,
	// 提示消息
	wxLogin,
	// 微信登录
	Console,
	// 控制台输出
	getVersion,
	// 获取版本号
	iphoneTest,
	// 手机验证
	setSesstion,
	// 修改sesstion
	getSesstion,
	// 获取sesstion
	clearSesstion,
	// 清楚sesstion
	paging,
	// 分页
	ajax,
	objToArr,
	// 拆分对象  转数组
	setTime: 2000,
	// 轮训时间
	baseUrl,
	time1,
	time2,
	time3,
	time4,
	time5,
	uploadPic,
	saveScene,
	getUrlCode,
	str2unicode,
	lookAdever,
	formatRichText,
	lookImg,
	copy,
	uploadVideo,
	splitImg,
	isAssetTypeAnImage
}; // // 页面跳转
// Vue.prototype.$jump=jump;
// // img  判断
// Vue.prototype.$imgUrl=imgUrl;
// // 提示消息
// Vue.prototype.$msg=msg;
// // 微信登录
// Vue.prototype.$wxLogin=wxLogin;
// // 控制台输出
// Vue.prototype.$Console=Console;
// // 获取版本号
// Vue.prototype.$getVersion=getVersion;
// // 手机验证
// Vue.prototype.$iphoneTest=iphoneTest;
// // 登录  注册  验证码
// Vue.prototype.$loginApi=loginApi;
// // 修改sesstion
// Vue.prototype.$setSesstion=setSesstion;
// // 获取sesstion
// Vue.prototype.$getSesstion=getSesstion;