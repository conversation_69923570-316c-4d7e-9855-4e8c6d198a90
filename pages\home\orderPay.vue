<template>
	<view class="page">
		<!-- 价格和倒计时区域 -->
		<view class="price-count-down">
			<view class="price">
				<text class="min">￥</text>
				<text class="max">
					{{ orderInfo.money }}
					<text v-if="orderInfo.dikou > 0" class="dikou"> + {{ orderInfo.dikou }}</text>
				</text>
			</view>
			<view class="count-down" v-if="CountDown">
				<view class="title">支付剩余时间</view>
				<view class="count">
					<text class="time">{{ formatTime(hour) }}</text>
					<text class="dot">:</text>
					<text class="time">{{ formatTime(min) }}</text>
					<text class="dot">:</text>
					<text class="time">{{ formatTime(sec) }}</text>
				</view>
			</view>
			<view class="count-down" v-else>
				<view class="title">订单已超时</view>
			</view>
		</view>

		<!-- 支付方式列表 -->
		<view class="pay-way">
			<view class="pay-list">
				<view class="list" v-for="(item, index) in orderInfo.pay" @click="onPayWay(item, index)" :key="index">
					<view class="pay-type">
						<image :src="$fun.imgUrl(item.image)" mode="aspectFit"></image>
						<text>{{ item.name }}</text>
						<text class="money-text">({{ item.money }})</text>
					</view>
					<view class="check">
						<radio :checked="PayWay == index" color="#310FFF" />
					</view>
				</view>
			</view>
		</view>

		<!-- 微信支付提示弹窗 -->
		<!-- <u-modal 
			v-model="show1" 
			:mask-close-able="true" 
			:show-confirm-button="false" 
			:show-title="false" 
			width="100%"
		>
			<view class="slot-content">
				<image 
					@click="show1 = !show1"
					:src="$fun.imgUrl('/uploads/20240423/25b9ac6d66fd88377e9fa577b1becfbd.png')"
					style="height: 100vh;width: 100vw;" 
					mode="aspectFit"
				></image>
			</view>
		</u-modal> -->

		<!-- 支付提交按钮 -->
		<view class="pay-submit">
			<view class="submit" @click="onSubmit">{{ PayPirce }}</view>
		</view>

		<!-- 支付密码键盘 -->
		<passkeyborad ref="passkeyborad" @configPay="configPay" :show="show" @close="onSubmit" :payTitle="payTitle"
			:payMoney="orderInfo.money + '' || 0 + ''"></passkeyborad>
	</view>
</template>

<script>
	export default {
		name: 'OrderPay',
		data() {
			return {
				// 支付相关
				PayList: [],
				PayWay: 0,
				PayPirce: '',
				payTitle: '',
				show: false,

				// 倒计时相关
				CountDown: 1800,
				day: 0,
				hour: 0,
				min: 0,
				sec: 0,

				// 订单信息
				orderInfo: {
					money: 0,
					pay: []
				},
				oid: null,
				gid: null,

				// 其他
				isJump: false,
				show1: false,
				isWeixin: 1
			};
		},

		onLoad(option) {
			this.initPageData(option);
		},

		onShow() {
			this.handleH5WeixinCheck();
			this.getpayWallet(this.oid);
		},

		methods: {
			/**
			 * 初始化页面数据
			 */
			initPageData(option) {
				this.oid = option.oid;
				this.gid = option.gid;
				this.isJump = option.isJump;
				this.isWeixin = option.isWeixin;
				console.log('微信环境标识:', this.isWeixin);
			},

			/**
			 * H5环境下的微信检查
			 */
			handleH5WeixinCheck() {
				// #ifdef H5
				const ua = navigator.userAgent.toLowerCase();
				const isWeixin = ua.indexOf('micromessenger') !== -1;

				if (!isWeixin && this.isWeixin * 1) {
					this.handleH5Alipay();
					return;
				}
				// #endif
			},

			/**
			 * H5支付宝支付处理
			 */
			handleH5Alipay() {
				const params = {
					oid: this.oid,
					type: 'alipay'
				};

				this.$fun.ajax.post('order/pay', params).then(res => {
					if (res.status == 1) {
						document.querySelector('body').innerHTML = res.data;
						this.$nextTick(() => {
							window.document.forms[0].submit();
						});
					}
				});
			},

			/**
			 * 支付方式切换
			 */
			onPayWay(item, index) {
				console.log('选择支付方式:', item);
				this.PayWay = index;
				this.PayPirce = `${item.name}￥${this.orderInfo.total}`;
				this.payTitle = this.orderInfo.pay[this.PayWay].name;
			},

			/**
			 * 格式化时间显示
			 */
			formatTime(time) {
				return time > 9 ? time : '0' + time;
			},

			/**
			 * 倒计时处理
			 */
			CountDownData() {
				setTimeout(() => {
					this.CountDown--;
					this.day = parseInt(this.CountDown / (24 * 60 * 60));
					this.hour = parseInt(this.CountDown / (60 * 60) % 24);
					this.min = parseInt(this.CountDown / 60 % 60);
					this.sec = parseInt(this.CountDown % 60);

					if (this.CountDown <= 0) {
						return;
					}
					this.CountDownData();
				}, 1000);
			},

			/**
			 * 支付密码确认
			 */
			configPay(pay, type = 1) {
				this.$fun.ajax.post('order/pay', {
					oid: this.oid,
					type: this.orderInfo.pay[this.PayWay].code,
					pay
				}).then(res => {
					this.show = false;
					if (res.status == 1) {
						this.$fun.msg(res.msg);

						if (type == 1) {
							this.handlePaymentSuccess();
						} else {
							return res.data;
						}
					}
				});
			},

			/**
			 * 支付提交处理
			 */
			async onSubmit() {
				const payCode = this.orderInfo.pay[this.PayWay].code;

				// 非微信支付宝支付（如余额支付）
				if (payCode.indexOf('wechat') == -1 && payCode.indexOf('alipay') == -1) {
					this.show = !this.show;
					this.$refs.passkeyborad.clear();
					return;
				}

				// 微信支付宝支付
				let type = this.getPlatformType();
				this.getpayInfo(type);
			},

			/**
			 * 获取平台类型
			 */
			getPlatformType() {
				// #ifdef H5
				const ua = navigator.userAgent.toLowerCase();
				const isWeixin = ua.indexOf('micromessenger') !== -1;
				let type = "H5";

				if (this.orderInfo.pay[this.PayWay].code.indexOf('alipay') != -1) {
					if (!isWeixin) {
						type = "H5_jsApi";
					} else {
						this.show1 = true;
						return null;
					}
				}
				return type;
				// #endif

				// #ifdef APP-PLUS
				return "App";
				// #endif

				// #ifdef MP-WEIXIN
				return "wechatmini";
				// #endif
			},

			/**
			 * 获取支付信息并处理支付
			 */
			getpayInfo(type) {
				const payParams = {
					oid: this.oid,
					type: this.orderInfo.pay[this.PayWay].code
				};

				// 添加openid参数（如果存在）
				if (uni.getStorageSync('openid')) {
					payParams.openid = uni.getStorageSync('openid');
				}

				this.$fun.ajax.post('order/pay', payParams).then(res => {
					this.show = false;

					if (res.status !== 1) {
						this.$fun.msg(res.msg || '支付失败');
						return;
					}

					const payData = res.data;
					const payCode = this.orderInfo.pay[this.PayWay].code;

					console.log('支付数据:', payData);
					console.log('支付方式:', payCode);

					// 根据支付方式分发处理
					if (payCode.indexOf('alipay') !== -1) {
						this.handleAlipayPayment(payData, type);
					} else {
						this.handleWechatPayment(payData, type);
					}
				}).catch(err => {
					console.error('支付请求失败:', err);
					this.$fun.msg('支付请求失败');
				});
			},

			/**
			 * 处理支付宝支付
			 */
			handleAlipayPayment(payData, type) {
				// #ifdef H5
				if (type === 'H5_jsApi' || this.isWeixin * 1) {
					// H5支付宝支付
					document.querySelector('body').innerHTML = payData;
					this.$nextTick(() => {
						window.document.forms[0].submit();
					});
					return;
				}
				// #endif

				// APP支付宝支付
				uni.requestPayment({
					provider: 'alipay',
					orderInfo: payData,
					success: (res) => {
						console.log('支付宝支付成功:', res);
						this.$fun.msg('支付成功');
						this.handlePaymentSuccess();
					},
					fail: (err) => {
						console.log('支付宝支付失败:', err);
						this.$fun.msg('支付失败');
					}
				});
			},

			/**
			 * 处理微信支付
			 */
			handleWechatPayment(payData, type) {
				// #ifdef APP-PLUS
				if (this.orderInfo.pay[this.PayWay].code.indexOf('hfwechat') !== -1) {
					// 微信小程序支付
					this.handleMiniProgramPayment(payData);
				} else {
					// 微信H5支付
					this.handleWechatH5Payment(payData);
				}
				// #endif

				// #ifdef H5
				this.handleWechatH5Payment(payData);
				// #endif

				// #ifdef MP-WEIXIN
				this.handleWechatMiniPayment(payData);
				// #endif
			},

			/**
			 * 处理微信小程序支付
			 */
			handleMiniProgramPayment(payData) {
				var sweixin = null;
				plus.share.getServices(function(services) {
					for (var i in services) {
						var service = services[i];
						if (service.id == 'weixin') {
							sweixin = service;
							break;
						}
					}
					console.log(sweixin);
					sweixin ? sweixin.launchMiniProgram({
						id: 'wx0db7249e3bdedc18', // 替换为你的微信小程序 ID
						path: 'pages/home/<USER>', // 替换为你想打开的小程序页面路径
						type: 2, // 0-正式版；1-测试版；2-体验版。默认值为 0。
					}) : plus.nativeUI.alert('当前环境不支持微信操作!');
				});
			},

			/**
			 * 处理微信H5支付
			 */
			handleWechatH5Payment(payData) {
				// #ifdef APP-PLUS
				const platform = uni.getSystemInfoSync().platform;
				const webview = plus.webview.create('', this.$fun.baseUrl());
				const referer = platform === 'ios' ? 'xiaoyutusc.com://' : this.$fun.baseUrl();

				webview.loadURL(payData, {
					'Referer': referer
				});
				// #endif

				// #ifdef H5
				const ua = navigator.userAgent.toLowerCase();
				const isWeixin = ua.indexOf('micromessenger') !== -1;

				if (!isWeixin) {
					location.href = payData;
				} else {
					this.handleWeixinBridgePayment(payData);
				}
				// #endif
			},

			/**
			 * 处理微信小程序内支付
			 */
			handleWechatMiniPayment(payData) {
				uni.requestPayment({
					appId: payData.appId,
					provider: 'wxpay',
					timeStamp: payData.timeStamp,
					nonceStr: payData.nonceStr,
					package: payData.package,
					signType: payData.signType,
					paySign: payData.paySign,
					success: (res) => {
						console.log('微信小程序支付成功:', res);
						this.$fun.msg('支付成功');
						this.handlePaymentSuccess();
					},
					fail: (err) => {
						console.log('微信小程序支付失败:', err);
						this.$fun.msg('支付失败');
					}
				});
			},

			/**
			 * 处理微信浏览器内支付
			 */
			handleWeixinBridgePayment(payData) {
				const vm = this;

				if (typeof WeixinJSBridge === 'undefined') {
					if (document.addEventListener) {
						document.addEventListener('WeixinJSBridgeReady', () => vm.wxpay(payData), false);
					} else if (document.attachEvent) {
						document.attachEvent('WeixinJSBridgeReady', () => vm.wxpay(payData));
						document.attachEvent('onWeixinJSBridgeReady', () => vm.wxpay(payData));
					}
				} else {
					this.wxpay(payData);
				}
			},

			/**
			 * 微信支付调用
			 */
			wxpay(data) {
				const vm = this;
				WeixinJSBridge.invoke(
					'getBrandWCPayRequest', {
						'appId': data.appId,
						'timeStamp': data.timeStamp,
						'nonceStr': data.nonceStr,
						'package': data.package,
						'signType': data.signType,
						'paySign': data.paySign
					},
					function(res) {
						if (res.err_msg === 'get_brand_wcpay_request:ok') {
							vm.$fun.msg('支付成功');
							uni.redirectTo({
								url: '/pagesC/order/orderDetails?order_num=' + vm.oid
							});
						} else {
							vm.$fun.msg('支付失败');
						}
					}
				);
			},

			/**
			 * 处理支付成功后的逻辑
			 */
			handlePaymentSuccess() {
				// 根据订单类型跳转到不同页面
				if (this.orderInfo.types === 'HD') {
					this.$fun.jump(`/pages/my/myOrder/orderDetails?order_num=${this.oid}`, 2, 800);
				} else if (this.orderInfo.types === 'YK') {
					this.$fun.jump('/pagesB/recharge_bill/recharge_list', 2, 800);
				} else if (this.orderInfo.types === 'PV') {
					setTimeout(() => {
						uni.navigateBack();
					}, 800);
				}
			},

			/**
			 * 检查支付状态
			 */
			getIsPay() {
				// #ifdef APP-PLUS || H5
				this.$fun.ajax.post('order/getOrderPay', {
					oid: this.oid,
				}).then(res => {
					console.log('支付状态检查:', res);
					if (res.status == 1 && res.data.isPay == 0) {
						this.$fun.msg(res.msg);
						this.handlePaymentSuccess();
					}
				});
				// #endif
			},

			/**
			 * 获取支付钱包信息
			 */
			getpayWallet(oid) {
				this.$fun.ajax.post('order/getPay', {
					oid
				}).then(res => {
					if (res.status == 1) {
						this.orderInfo = res.data;
						this.getIsPay();
						this.PayPirce = ` ${res.data.pay[this.PayWay].name}￥${res.data.total}`;

						// 设置倒计时
						if (res.data.deltime - (new Date().getTime() / 1000) > 0) {
							this.CountDown = res.data.deltime - (new Date().getTime() / 1000);
							this.CountDownData();
						} else {
							this.CountDown = null;
						}
					}
				});
			}
		}
	};
</script>

<style scoped lang="scss">
	.page {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: #f6f6f6;
	}

	/* 金额倒计时 */
	.price-count-down {
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 200rpx;
		background-color: #FFFFFF;

		.price {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100%;
			height: 80rpx;

			text {
				color: $price-clor;
				font-weight: bold;
			}

			.min {
				font-size: 32rpx;
			}

			.max {
				font-size: 52rpx;
			}

			.dikou {
				margin-left: 10rpx;
				color: #310FFF;
				font-size: 20rpx;
			}
		}

		.count-down {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100%;
			height: 60rpx;

			.title {
				font-size: 24rpx;
				color: #222222;
			}

			.count {
				display: flex;
				align-items: center;
				margin-left: 20rpx;

				.time {
					padding: 4rpx 4rpx;
					background-color: #EEEEEE;
					font-size: 24rpx;
					color: #222222;
					border-radius: 2rpx;
				}

				.dot {
					margin: 0 10rpx;
					font-size: 24rpx;
					color: #222222;
				}
			}
		}
	}

	/* 支付方式 */
	.pay-way {
		width: 100%;
		background-color: #FFFFFF;
		margin-top: 220rpx;

		.pay-list {
			padding: 0 4%;

			.list {
				display: flex;
				align-items: center;
				justify-content: space-between;
				width: 100%;
				height: 100rpx;
				border-bottom: 2rpx solid #f6f6f6;

				.pay-type {
					display: flex;
					align-items: center;
					max-width: 90%;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;

					image {
						width: 40rpx;
						height: 40rpx;
					}

					text {
						font-size: 28rpx;
						color: #222222;
						margin-left: 20rpx;
					}

					.money-text {
						color: #310FFF;
						font-size: 20rpx;
					}
				}

				.check {
					display: flex;
					align-items: center;

					text {
						font-size: 42rpx;
						color: #C0C0C0;
					}

					.action {
						color: #310FFF;
					}
				}
			}
		}
	}

	/* 支付提交 */
	.pay-submit {
		position: absolute;
		left: 0;
		bottom: 0;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 100rpx;
		background-color: #FFFFFF;

		.submit {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 90%;
			height: 70%;
			background-color: #310FFF;
			color: #FFFFFF;
			border-radius: 100rpx;
			font-size: 26rpx;
		}
	}

	/* 弹窗样式 */
	::v-deep .u-mode-center-box {
		width: 100vw;
		height: 100vh;
		background: transparent !important;
	}

	::v-deep .u-model {
		background: transparent !important;
	}
</style>