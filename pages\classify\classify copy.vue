<template>
	<view class="page" ref="page">
		<!-- 搜索 -->
		<view class="search-index">
			<!-- #ifndef H5 -->
			<view class="icon" @click="onCode">
				<text class="iconfont icon-saoyisao"></text>
			</view>
			<!-- #endif -->
			<!-- #ifdef  H5 -->
			<view class="icon" @click="onPayCode">
				<text class="iconfont icon-fukuanma"></text>
			</view>
			<!-- #endif -->
			<view class="search">
				<view class="iconfont icon-fadajing"></view>
				<input type="text" placeholder="输入搜索内容" />
			</view>
			<view class="icon">
				<text class="iconfont icon-xiaoxi"></text>
			</view>
		</view>
		<!-- 分类数据 -->
		<view class="classify-data" :style="'height:'+height+'px'">
			<view class="classify-one">
				<scroll-view scroll-y class="classify-list">
					<view :class="index==tabIndex?'list action':'list'" v-for="(item,index) in navList" :key="index" @click="ChnageTab(index)">
						<text>{{item.name}}</text>
					</view>
				</scroll-view>
			</view>
			<view class="classify-two-three">
				<scroll-view scroll-y class="scroll">
					<view class="classify-two" v-if="navList.length>0">
						<view class="two-name">
							<view class="name">{{navList[tabIndex].name}}</view>
						</view>
						<view class="classify-three" >
							<view class="list" v-for="(item,index) in navShopList" :key="index" @click="$fun.jump(`/pages/home/<USER>">
								<image :src="$fun.imgUrl(item.image)"></image>
								<text>{{item.name}}</text>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</view>
		<!-- tabbar -->
		<TabBar ></TabBar>
	</view>
</template>

<script>
	import TabBar from '@/components/TabBar.vue';
	export default {
		components: {
			TabBar,
		},
		data() {
			return {
				height: 0,
				tabIndex:0,
				page:1,
				navList:[],
				navShopList:[]
			};
		},
		onReady() {
			setTimeout(() => {
				uni.hideTabBar()
			}, 100)
			let info = uni.createSelectorQuery().select(".page");
			info.boundingClientRect((data) => { //data - 各种参数
				console.log(data.height);
				this.height = data.height - 100;
				// #ifdef APP-PLUS 
				this.height = data.height - 130;
				// #endif 

			}).exec()
		},
		onLoad() {
			this.init()
		},
		// 下拉刷新
		onPullDownRefresh(){
			this.page = []
			this.navShopList = []
			this.init()
		},
		// 加载更多数据
		onReachBottom(){
			if(this.page!=1){
				this.getNavShop(this.navList[this.tabIndex].id)
			}
		},
		methods: {
			/**
			 * 初始化
			 */
			init(){
				this.getnavList()
			},
			/**
			 * 获取分类列表
			 */
			getnavList(){
				this.$fun.ajax.post('category/list',{type:'type'}).then(res=>{
					if(res.status==1){
						this.navList = res.data;
						this.getNavShop(res.data[0].id)
					}
				})
			},
			/**
			 * 获取右侧商品数据
			 */
			getNavShop(cid){
				let prams = {
					cid,
					page:this.page,
				}
				this.$fun.paging('goods/list',this,prams,'navShopList')
			},
			/**
			 * 切换分类
			 */
			ChnageTab(index){
				if(this.tabIndex != index){
					this.tabIndex = index
					this.page = 1
					this.navShopList = []
					this.getNavShop(this.navList[index].id)
				}
			},
			/**
			 * 扫一扫点击
			 */
			onCode() {
				// 只允许通过相机扫码
				uni.scanCode({
					onlyFromCamera: true,
					success: function(res) {
						console.log('条码类型：' + res.scanType);
						console.log('条码内容：' + res.result);
					}
				});
			},
			/**
			 * 付款码点击
			 */
			onPayCode() {
				uni.navigateTo({
					url: '/pages/PaymentCode/PaymentCode'
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	@import 'classify.scss'
</style>
