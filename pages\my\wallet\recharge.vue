<template>
	<view class="address-manage rf-row-wrapper">
		<view style="padding: 30rpx;color:#310FFF;text-align: right;"
			@click="$fun.jump(`/pages/my/wallet/withdrawList?name=充值记录&type=recharge`,0,0)">
			充值记录
		</view>
		<view class="top">
			<view class="left">
				<view class="title">{{titleName}}</view>
				<view class="num">{{balance}}</view>
			</view>
		</view>
		<view style="display: flex;justify-content: center;" v-if="rechargeList.length>0">
			<image v-for="(item,index) in rechargeList" :key="index" :src="$fun.imgUrl(item)" @click="lookImg(item)"
				style="width: 200rpx;height: 200rpx;margin: 10rpx auto;" mode="">
			</image>
		</view>
		<view class="row b-b" v-for="(item,index) in  recharge_arr" :key="index">
			<text class="tit">{{item.key}}</text>
			<text class="tit" @click="copy(item.value)">{{item.value}}</text>
		</view>
		<view class="content_box">
			<u-form labelPosition="top" :model="model"  ref="form1">
				<u-form-item label="姓名"  prop="name" borderBottom ref="item1">
					<u-input placeholder="请输入姓名" :border="true"  v-model="model.name"></u-input>
				</u-form-item>
				<u-form-item label="手机号"  prop="phone" borderBottom ref="item1">
					<u-input placeholder="请输入手机号" :border="true" v-model="model.phone"></u-input>
				</u-form-item>
			</u-form>
		</view>
		<view class="conter">
			<view class="cent">
				<block v-for="(item,index) in moneyList">
					<view v-if="item!=''" @click="checkePrice(item,index)">
						<view v-if="currentIndex==index" class="list" :style="{
							border:'1px solid '+themeColor.color,
							color :themeColor.color,
							height:'100%'
						}">{{item}}</view>
						<view v-else class="list" :style="{
							height:'100%'
						}">{{item}}</view>
					</view>
					<view v-else>
						<view v-if="currentIndex==index" class="list" :style="{
							border:'1px solid '+themeColor.color,
							color :themeColor.color,
							height:'124upx'
						}"><input type="number" style="color: #000000;" v-model="money1" placeholder="自定义" @focus="currentIndex=index" />
						</view>
						<view v-else class="list" :style="{
							height:'124upx'
						}"><input type="number" style="color: #000000;" v-model="money1" placeholder="自定义" @focus="currentIndex=index" />
						</view>
					</view>
				</block>
			</view>
		</view>
				<view class="top">
			<view class="left">
				<view class="title">上传凭证</view>
			</view>
		</view>
		<view class="top">
			<view class="left">
				<image :src="image?$fun.imgUrl(image):$fun.imgUrl('/static/ico-101.png')" @click="uploadImage()"
					style="width: 150rpx;height:150rpx;" mode=""></image>
			</view>
		</view>
		<view style="height: 100rpx;">

		</view>
		<button class="add-btn" @tap="confirm">
			提交
		</button>
	</view>
</template>
<script>
	/**
	 * @des 用户账户中心
	 *
	 * @<NAME_EMAIL>
	 * @date 2020-01-10 15:17
	 * @copyright 2019
	 */
	// import { memberInfo, memberUpdate, uploadImage } from '@/api/userInfo';
	import uniCopy from '@/components/xb-copy/uni-copy.js'
	export default {
		data() {
			return {
				wallet: {},
				loading: true,
				visible: false,
				// 支付方式
				curType: 'wxpay',
				showPwd: false,
				moneyList: [],

				money1: '', //其他金额
				image: '', //充值图片
				id: '',
				recharge: '',
				rechargeList: '',
				recharge_arr: '',
				titleName: '', //标题
				currentIndex: 0,
				balance: '==',
				themeColor: {
					color: '#310FFF'
				},
				model: {
					name: '',
					phone: '',
					money: "", //列表金额
				}
			};
		},
		onLoad(options) {
			this.id = options.id
			this.titleName = options.name
			this.initData();
		},
		methods: {
			lookImg(item) {
				uni.previewImage({
					urls: [this.$fun.imgUrl(item)] //预览图片的地址，必须要数组形式，如果不是数组形式就转换成数组形式就可以
				})
			},
			copy(text) {
				uniCopy({
					content: text,
					success: (res) => {
						uni.showToast({
							title: res,
							icon: 'none'
						})
					},
					error: (e) => {
						uni.showToast({
							title: e,
							icon: 'none',
							duration: 3000,
						})
					}
				})
			},
			// 初始化数据
			initData() {
				this.getRecharge(this.id);
			},
			checkePrice(item, index) {
				this.money = item;
				this.currentIndex = index
				// //console.log(this.moneyList)
			},
			// 通用跳转
			navTo(route) {
				this.$mRouter.push({
					route
				});
			},
			confirm() {
				let {
					name,
					phone,
				} = this.model
				let prams = {
					name,
					id:this.id,
					mobile:phone,
					money:this.money,
					image:this.image
				}
				if (this.money1 != '') {
					prams.money = this.money1
				}
				if (!prams.name) {
					this.$fun.msg('请输入真实姓名');
					return
				}
				if (!prams.mobile) {
					this.$fun.msg('请输入手机号');
					return
				}
				if (!prams.money) {
					this.$fun.msg('请输入充值金额');
					return
				}
				if (!prams.image) {
					this.$fun.msg('请上传充值截图');
					return
				}
				this.$fun.ajax.post('wallet/recharge', {
					...prams
				}).then(res => {
					if (res.status == 1) {
						this.$fun.msg(res.msg)
						setTimeout(()=>{
							uni.navigateBack({

							})
						},1200)
					}
				})
			},
			// confirm() {
			// 	let vm = this;
			// 	let {
			// 		name,
			// 		phone,
			// 	} = this.model
			// 	let prams = {
			// 		// name,
			// 		id: this.id,
			// 		// mobile:phone,
			// 		money: this.money,
			// 		// openid: uni.getStorageSync('openid')
			// 		// image:this.image
			// 	}
			// 	if (uni.getStorageSync('openid')) {
			// 		prams.openid = uni.getStorageSync('openid')
			// 	}
			// 	if (this.money1 != '') {
			// 		prams.money = this.money1
			// 	}
			// 	// if (!prams.name) {
			// 	// 	this.$fun.msg('请输入真实姓名');
			// 	// 	return
			// 	// }
			// 	// if (!prams.mobile) {
			// 	// 	this.$fun.msg('请输入手机号');
			// 	// 	return
			// 	// }
			// 	if (!prams.money) {
			// 		this.$fun.msg('请输入充值金额');
			// 		return
			// 	}
			// 	// if (!prams.image) {
			// 	// 	this.$fun.msg('请上传充值截图');
			// 	// 	return
			// 	// }
			// 	this.$fun.ajax.post('wallet/recharge', {
			// 		...prams
			// 	}).then(res => {
			// 		if (res.status == 1) {
			// 			let data = res.data
			// 			// uni.requestPayment({
			// 			// 	appId: data.appId,
			// 			// 	provider: 'wxpay',
			// 			// 	timeStamp: data.timeStamp,
			// 			// 	nonceStr: data.nonceStr,
			// 			// 	package: data.package,
			// 			// 	signType: data.signType,
			// 			// 	paySign: data.paySign,
			// 			// 	success: function(res) {
			// 			// 		vm.$fun.msg('支付成功')
			// 			// 		setTimeout(() => {
			// 			// 			uni.navigateBack()
			// 			// 		}, 1200)
			// 			// 	},
			// 			// 	fail: function(err) {
			// 			// 		vm.$fun.msg('支付失败')
			// 			// 	}
			// 			// });
			// 			// #ifdef H5
			// 			var ua = navigator.userAgent.toLowerCase();
			// 			var isWeixin = ua.indexOf('micromessenger') !== -1;
			// 			let type = 0;
			// 			if (!isWeixin) {
			// 				type = 0
			// 			} else {
			// 				type = 1
			// 			}
			// 			// #endif
			// 			this.$fun.jump(
			// 				`/pages/home/<USER>
			// 			)
			// 			// setTimeout(()=>{
			// 			// 	uni.navigateBack({

			// 			// 	})
			// 			// },1200)
			// 		}
			// 	})
			// },
			// 获取用户信息
			async getRecharge(id) {
				await this.$fun.ajax.post('wallet/config', {
					id,
					type: 'recharge',
				}).then(res => {
					if (res.status == 1) {
						this.rechargeList = res.data.recharge
						this.balance = res.data.money
						this.recharge_arr = res.data.recharge_arr
						this.moneyList = res.data.money_arr
						this.money = res.data.money_arr[0]
					}
				})
			},
			// 上传头像
			uploadImage() {
				// 从相册选择图片
				const _this = this;
				uni.chooseImage({
					count: 1,
					sizeType: ['original', 'compressed'],
					sourceType: ['album'],
					success: function(res) {
						_this.handleUploadFile(res.tempFilePaths);
					}
				});
			},
			// 上传头像
			handleUploadFile(data) {
				const _this = this;
				const filePath = data.path || data[0];
				this.$fun.uploadPic(
					filePath
				).then(res => {
					this.$fun.msg(res.msg)
					if (res.status == 1) {
						_this.image = res.data.url;
					}
				})
			},
			navTo(route) {
				this.$mRouter.push({
					route
				});
			}
		},
		onNavigationBarButtonTap(e) {
			// #ifdef APP-PLUS
			// eslint-disable-next-line
			const pages = getCurrentPages();
			const page = pages[pages.length - 1];
			const currentWebview = page.$getAppWebview();
			currentWebview.hideTitleNViewButtonRedDot({
				index
			});
			// #endif
			this.navTo(`/pages/user/account/listAll?id=${this.id}&name=充值记录`);
		},
	};
</script>
<style lang="scss">
	page {
		background: #FFFFFF;
	}

	.content_box {
		padding: 30rpx
	}

	.my-account {
		background-color: #FFFFFF;
		/*  #ifndef H5  */
		height: 100vh;
		/*  #endif  */
		padding: 32upx 20upx;
		width: 100%;

		.header {
			padding: 30upx;
			height: 200upx;
			display: flex;
			align-items: center;
			opacity: 0.9;
			border-radius: 20upx;
			color: rgba(255, 255, 255, 0.6);
			font-size: 24rpx;
			position: relative;

			.account {
				width: calc(100% - 60upx);
				display: flex;
				position: absolute;
				z-index: 2;
				justify-content: space-between;

				.assets {
					.money {
						color: #fff;
						font-size: 30upx;
						margin: 0;
					}
				}

				.recharge {
					font-size: 28rpx;
					width: 150upx;
					height: 54upx;
					line-height: 54upx;
					border-radius: 28rpx;
					background-color: #fff9f8;
					text-align: center;
					margin-top: 10upx;
				}
			}

			.cumulative {
				width: calc(100% - 240upx);
				position: absolute;
				bottom: 20upx;
				display: flex;
				justify-content: space-between;

				.money {
					color: #fff;
					font-size: 36rpx;
					margin: 0;
				}
			}

			.header-bg {
				position: absolute;
				width: 100%;
				height: 320upx;
				z-index: 1;
				top: 0;

				image {
					width: 100%;
					height: 320upx;
				}
			}
		}

		.nav {
			border-bottom: 1px solid #f5f5f5;
			display: flex;

			.item {
				flex: 1;
				margin: 20upx;
				font-size: 26rpx;
				display: inline-block;
				text-align: center;
				color: #999;

				.iconfont {
					display: block;
					margin: 0 auto;
					font-size: 40rpx;
				}
			}
		}

		.advert {
			display: flex;

			.item {
				flex: 1;
				border-radius: 24upx;
				padding: 10upx 0;
				margin: 20upx 10upx;
				display: flex;
				justify-content: space-between;

				.iconfont {
					font-size: 40upx;
					margin-right: 20upx;
				}

				.text {
					margin-left: 20upx;

					.name {
						font-size: 28rpx;
						font-weight: bold;
						height: 40upx;
					}

					.desc {
						font-size: 24rpx;
					}
				}
			}

			.on {
				background-color: #fff3f3;
			}
		}
	}

	/* 内容区 */
	.conter {
		.title {
			color: #666;
			margin-top: 50rpx;
			font-size: 30rpx;
		}

		.cent {
			margin-top: 25rpx;
			overflow: hidden;

			.list {
				width: 200rpx;
				height: 120rpx;
				border: 1rpx solid #e8e8e8;
				float: left;
				margin: 0 18rpx;
				margin-bottom: 45rpx;
				position: relative;
				text-align: center;
				line-height: 120rpx;
				border-radius: 15rpx;

				// &:nth-child(3n) {
				// 	margin-right: 0;
				// }

				input {
					height: 50rpx;
					margin-left: 10rpx;
					margin-top: 35rpx;
				}

				.van-cell {
					padding: 0;
					width: 180rpx;
				}

				image {
					width: 40rpx;
					height: 37rpx;
					position: absolute;
					bottom: 0;
					right: 0;
					display: none;
				}

				&.active {
					color: #dd1021;
					border: 1rpx solid #dd1021;

					image {
						display: block;
					}
				}
			}
		}
	}

	.top {
		display: flex;
		justify-content: space-between;
		margin-left: 20rpx;

		.left {
			margin-top: 40rpx;

			.title {
				font-size: 24rpx;
				color: #666;
			}

			.num {
				font-size: 48rpx;
				color: #000;
				margin-top: 30rpx;
				font-weight: bold;
			}

			.jyjl {
				font-size: 24rpx;
				color: #dd1021;
				margin-top: 50rpx;
			}
		}

		.rightimg {
			width: 233rpx;
			height: 240rpx;
			margin-right: 50rpx;
		}
	}
</style>