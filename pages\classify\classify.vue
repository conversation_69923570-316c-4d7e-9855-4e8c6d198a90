<template>
	<view class="page" ref="page">
		<view class="head-search">
			<view class="search" @click="$fun.jump(`/pages/home/<USER>">
				<view class="icon">
					<image :src="$fun.imgUrl('/static/fdj_ico.png')" mode=""></image>
				</view>
				<view class="hint">
					<text class="min">热门内容</text>
				</view>
			</view>
		</view>
		<!-- 分类数据 -->
		<view class="classify-data" :style="'height:' + height + 'px'">
			<view class="classify-one">
				<scroll-view scroll-y class="classify-list">
					<view :class="index == tabIndex ? 'list action' : 'list'" v-for="(item, index) in navList" :key="index"
						@click="ChnageTab(index)">
						<text>{{ item.name }}</text>
						<image v-if="index == tabIndex" :src="$fun.imgUrl(`/static/index/a.png`)" style="width: 44rpx;" mode="widthFix">
						</image>
						<image v-else :src="$fun.imgUrl(`/static/index/n.png`)" style="width: 44rpx;" mode="widthFix"></image>
					</view>
				</scroll-view>
			</view>
			<view class="classify-two-three">
				<scroll-view scroll-y class="scroll">
					<!-- <view class="store_list">
						<view class="store_item" v-for="(item,index) in 8" :key="index"
							@click="$fun.jump(`./classify_store?name=三只松鼠`)">
							<image :src="$fun.imgUrl(`/static/d.png`)" mode=""></image>
							<view class="store_name">
								三只松鼠
							</view>
						</view>
					</view> -->
					<view class="classify-two" v-if="navList[0]">
						<view class="two-name">
							<view class="name">{{ navList[tabIndex].name }}</view>
							<view class="two_gd"
								@click="$fun.jump(`/pages/home/<USER>">
								<image :src="$fun.imgUrl('/static/index/hot.png')" mode=""></image>
								<text>查看更多 ></text>
							</view>
						</view>
						<view class="classify-three">
							<view class="goods-list">
								<view class="goods-list-left">
									<YWaterfall :items="leftList" :isbtn="true" ref="yWaterfall">
									</YWaterfall>
								</view>
								<view class="goods-list-right">
									<YWaterfall :items="rightList" :isbtn="true" ref="yWaterfall">
									</YWaterfall>
								</view>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</view>
		<!-- tabbar -->
		<TabBar :tabBarShow="1"></TabBar>
	</view>
</template>

<script>
import TabBar from '@/components/TabBar/TabBar.vue';
import YWaterfall from '@/components/y-components/y-waterfall.vue';
export default {
	components: {
		TabBar,
		YWaterfall
	},
	data() {
		return {
			height: 0,
			tabIndex: 0,
			page: 1,
			navList: [],
			swiperList: [],
			navShopList: [],
			islist: true,
			leftList: [],
			rightList: []
		};
	},

	onReady() {
		setTimeout(() => {
			uni.hideTabBar()
		}, 100)
		let info = uni.createSelectorQuery().select(".page");
		info.boundingClientRect((data) => { //data - 各种参数
			console.log(data.height);
			this.height = data.height - 60 - 52;
			// #ifdef APP-PLUS 
			this.height = data.height - 60 - 52;
			// #endif 

		}).exec()
	},
	onLoad(option) {
		if (option.id) {
			this.classId = option.id
		}
		this.init()
	},
	// 下拉刷新
	onPullDownRefresh() {
		this.page = []
		this.navShopList = []
		this.leftList = []
		this.rightList = []
		this.init()
	},
	// 加载更多数据

	onReachBottom() {
		if (this.page != 1) {
			this.getNavShop(this.navList[this.tabIndex].id)
		}
	},
	methods: {
		/**
		 * 初始化
		 */
		init() {
			this.getnavList()
			// this.getIndexSwipter()
		},
		getIndexSwipter() {
			this.$fun.ajax.post('News/lists', {
				type: 'index'
			}).then(res => {
				if (res.status == 1) {
					this.swiperList = res.data
				}
			})
		},
		/**
		 * 获取分类列表
		 */
		getnavList() {
			this.$fun.ajax.post('category/list', {
				type: 'type'
			}).then(res => {
				if (res.status == 1) {
					if (this.classId) {
						for (var i = 0; i < res.data.length; i++) {
							if (res.data[i].id == this.classId) {
								this.tabIndex = i
							}
						}
						this.getNavShop(this.classId)
						this.navList = res.data;
						return
					}
					this.getNavShop(res.data[0].id)
					this.navList = res.data;
				}
			})
		},
		/**
		 * 获取右侧商品数据
		 */
		async getNavShop(cid) {
			let prams = {
				cid: cid,
				page: 1,
				limt: 999
			}
			this.navShopList = [];
			this.leftList = [];
			this.rightList = [];
			try {
				this.$refs.uWaterfall.clear();
			} catch (e) {


			}
			// this.$refs.uWaterfall.clear();
			await this.$fun.paging('goods/list', this, prams, 'navShopList').then(res => {
				if (res.status == 1) {
					for (let i = 0; i < this.navShopList.length; i++) {
						if (i % 2 == 0) {
							this.leftList.push(this.navShopList[i])

						} else {
							this.rightList.push(this.navShopList[i])
						}
					}
				}
			})
		},

		/**
		 * 切换分类




		 */
		ChnageTab(index) {
			if (this.tabIndex != index) {
				this.tabIndex = index
				this.page = 1
				this.navShopList = []
				this.getNavShop(this.navList[index].id)
			}
		},
		/**
		 * 扫一扫点击
		 */
		onCode() {
			// 只允许通过相机扫码
			uni.scanCode({
				onlyFromCamera: true,
				success: function (res) {
					console.log('条码类型：' + res.scanType);
					console.log('条码内容：' + res.result);
				}
			});
		},
		/**
		 * 付款码点击
		 */
		onPayCode() {
			uni.navigateTo({
				url: '/pages/PaymentCode/PaymentCode'
			})
		}
	}
}
</script>

<style scoped lang="scss">
@import 'classify.scss';

.goods-list {
	// padding: 0 25rpx;
	overflow: hidden;
	width: 100%;
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;

	.list-view {
		width: 49%;
		background-color: #ffffff;
		// margin-right: 8%;
		margin-bottom: 20rpx;
		overflow: hidden;
		background: #FFFFFF;
		box-shadow: 0rpx 8rpx 8rpx 0rpx rgba(0, 0, 0, 0.08);
		border-radius: 12rpx 12rpx 12rpx 12rpx;

		.thumb {
			width: 100%;
			// height: 228rpx;

			image {
				height: 228rpx;
				border-radius: 12rpx 12rpx 0rpx 0rpx;
			}
		}

		.item {
			width: 100%;
			padding: 20rpx 20rpx 0 20rpx;


			.title {

				text {
					width: 100%;
					font-family: Source Han Sans CN, Source Han Sans CN;
					font-weight: 400;
					font-size: 28rpx;
					color: #333333;
				}
			}

			.price {
				.retail-price {
					display: flex;

					align-items: flex-end;
					align-items: center;
					width: 100%;
					height: 40rpx;

					.min {
						display: inline-block;
						font-size: 20rpx;
						color: #310FFF;
					}

					.max {
						font-size: 28rpx;
						color: #310FFF;
					}

					.tag {
						position: relative;
						background-color: $base;
						border-radius: 4rpx;
						margin-left: 10rpx;

						text {
							display: inline-block;
							color: #ffffff;
							font-size: 24rpx;
							transform: scale(0.7);
						}
					}

					.tag:before {
						position: absolute;
						left: -6rpx;
						top: 0;
						content: '';
						width: 0;
						height: 0;
						border-top: 0rpx solid transparent;
						border-right: 10rpx solid $base;
						border-bottom: 6rpx solid transparent;
					}
				}

				.vip-price {
					display: flex;
					width: 100%;
					text-decoration: line-through;

					// height: 40rpx;
					.min {
						display: inline-block;
						font-size: 20rpx;
						color: #212121;
					}

					.max {
						font-size: 20rpx;
						color: #212121;
					}
				}
			}
		}
	}

	.list-view:nth-child(2n) {
		margin-right: 0;
	}

	// 列表
	.list-li {
		display: flex;
		align-items: center;
		width: 100%;
		height: 300rpx;
		background-color: #ffffff;

		.thumb {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 30%;
			height: 100%;

			image {
				width: 200rpx;
				height: 200rpx;
				border-radius: 10rpx;
			}
		}

		.item {
			display: flex;
			flex-direction: column;
			justify-content: center;
			width: 70%;
			height: 100%;
			border-bottom: 2rpx solid #f6f6f6;

			.title {
				padding: 20rpx;

				text {
					width: 100%;
					color: #212121;
					font-size: 26rpx;
				}
			}

			.price {
				padding: 0 20rpx;

				.retail-price {
					display: flex;
					align-items: flex-end;
					width: 100%;
					height: 40rpx;

					.min {
						display: inline-block;
						font-size: 24rpx;
						color: $base;
						font-weight: bold;
						transform: scale(0.7);
					}

					.max {
						font-size: 28rpx;
						color: $base;
						font-weight: bold;
					}

					.tag {
						position: relative;
						background-color: $base;
						border-radius: 4rpx;
						margin-left: 10rpx;

						text {
							display: inline-block;
							color: #ffffff;
							font-size: 24rpx;
							transform: scale(0.7);
						}
					}

					.tag:before {
						position: absolute;
						left: -6rpx;
						top: 0;
						content: '';
						width: 0;
						height: 0;
						border-top: 0rpx solid transparent;
						border-right: 10rpx solid $base;
						border-bottom: 6rpx solid transparent;
					}
				}

				.vip-price {
					display: flex;
					align-items: flex-end;
					width: 100%;
					height: 40rpx;

					.min {
						display: inline-block;
						font-size: 24rpx;
						color: #212121;
					}

					.max {
						font-size: 24rpx;
						color: #212121;
					}
				}
			}
		}
	}
}

::v-deep .u-column {
	width: 50%;

	.list-view {
		padding-bottom: 20rpx;
		width: 97% !important;
		// margin-left: 3%;
		height: auto !important;

		image {
			width: 100%;
			height: auto;
		}
	}
}

::v-deep .u-waterfall {
	width: 100% !important;
}
</style>