.page{
	position: absolute;
	width: 100%;
	// height: 100%;
	padding-bottom: 100rpx;
}
page{
	width: 750rpx;
	background: #F5F5F5 !important;
	background-color: #FFFFFF;
}
.my-top{
	position: relative;
	width: 100%;
	height: 486rpx;
	/* #ifdef APP-PLUS */
	height: 486rpx;
	/* #endif */
	/* #ifdef MP-WEIXIN */
	height: 640rpx;
	/* #endif */
	background: #FFF7E8;
	// overflow: hidden;
	.my_bg{
		position: absolute;
		top: 0;
		left: 0;
		z-index: 1;
		width: 100%;
		
	}
	.wallet_top{
		padding: 0 40rpx;
		position: relative;
		z-index: 1;
		display: flex;
		flex-wrap: wrap;
		justify-content: space-around;
		margin-top: 56rpx;
		width: 100%;
		.wallet_i{
			width: 30%;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			.l{
				display: flex;
				align-items: center;
				.name{
					margin-left: 2rpx;
					font-weight: 500;
					font-size: 24rpx;
					color: #666666;
				}
			}
			.r{
				margin-top: 10rpx;
				font-weight: 500;
				font-size: 36rpx;
				color: #333333;
			}
		}
		
	}
	
		.vip_box{
			position: relative;
			bottom: -60rpx;
			/* #ifdef APP-PLUS */
			bottom: -30rpx;
			/* #endif */
			z-index: 1;
			padding: 0 20rpx;
			image{
				width: 100%;
			}
			.vip_info{
				position: absolute;
				width:calc(100% - 50rpx);
				height: calc(100% - 20rpx);
				z-index: 1;
				bottom: 20rpx;
				padding-left: 116rpx;
				padding-right: 26rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
				.left{
					.name{
						font-weight: 800;
						font-size: 40rpx;
						color: #B2C6FA;
					}
					.info{
						font-weight: 400;
						font-size: 20rpx;
						color: #B2C6FA;
					}
				}
				.right{
					display: flex;
					align-items: center;
					justify-content: center;
					width: 120rpx;
					height: 52rpx;
					font-weight: 500;
					font-size: 24rpx;
					color: #AE7137;
					background: linear-gradient( 90deg, #FFE5C5 0%, #FFBC6C 100%);
					border-radius: 26rpx 26rpx 26rpx 26rpx;
				}
				
			}
			
		}
	.head{
		position: fixed;
		left: 0;
		top: 0;
		z-index: 80;
		display: flex;
		align-items: center;
		justify-content: space-between;
		width: 100%;
		height: 100rpx;
		/* #ifdef APP-PLUS */
		height: calc(100rpx + var(--status-bar-height));
		padding-top: var(--status-bar-height);
		/* #endif */
		/* #ifdef MP */
		height: calc(200rpx + var(--status-bar-height));
		padding-top: calc(100rpx + var(--status-bar-height));
		/* #endif */
		background-color: rgba(255,255,255,0);
		// transition: all 1s;
		.portrait{
			display: flex;
			width: 60rpx;
			height: 60rpx;
			margin-left: 20rpx;
			image{
				width: 100%;
				height: 100%;
				border-radius: 100%;
			}
		}
		.title{
			display: flex;
			align-items: center;
			text{
				color: #212121;
				font-size: 28rpx;
			}
		}
		.setting-mess{
			display: flex;
			align-items: center;
			height: 100%;
			margin-right: 20rpx;
			.setting{
				display: flex;
				justify-content: center;
				align-items: center;
				width: 80rpx;
				height: 100%;
				text{
					color: #FFFFFF;
					font-size: 38rpx;
				}
			}
			.mess{
				display: flex;
				justify-content: center;
				align-items: center;
				width: 80rpx;
				height: 100%;
				text{
					color: #FFFFFF;
					font-size: 38rpx;
				}
			}
		}
	}
	/* 用户信息 */
	.user-info{
		position: relative;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 40rpx;
		height: 120rpx;
		z-index: 10;
		// margin-top: 100rpx;
		/* #ifdef APP-PLUS */
		margin-top: 130rpx;
		/* #endif */
		/* #ifdef MP */
		margin-top: 200rpx;
		/* #endif */
		.portrait{
			width: 120rpx;
			height: 120rpx;
			margin-right: 20rpx;
			box-sizing: border-box;
			image{
				width: 100%;
				height: 100%;
				border-radius: 100%;
				box-sizing: border-box;
			}
		}
		.endTime{
			display: flex;
			justify-content: center;
			flex-direction: column;
			align-items: center;
			height: 80px;
			color: #FFFFFF;
		}
		.info{
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			width: 55%;
			height: 100%;
			.nickname{
				width: 100%;
				padding: 10rpx 0;
				display: flex;
				margin-right: 10rpx;
				text{
					font-family: YaHei;
					font-weight: bold;
					font-size: 40rpx;
					color: #333333;
				}
				.identity{
					margin-left: 10rpx;
					width: max-content;
					padding: 0 10rpx;
					background: #FFFFFF;
					border-radius: 20rpx 20rpx 20rpx 20rpx;
					display: flex;
					align-items: center;
					text{
							font-family: Source Han Sans CN, Source Han Sans CN;
							font-weight: 400;
							font-size: 20rpx;
							color: #310FFF
					}
					image{
						width: 20rpx;
						height: 20rpx;
					}
				}
			}
			.rank{
				display: flex;
				align-items: center;
				width: fit-content;
				height: 35rpx;
				padding-right: 10rpx;
				border-radius: 30rpx;
				image{
					width: 24rpx;
					height: 24rpx;
				}
				text{
				font-weight: 500;
				font-size: 24rpx;
				color: #666666;
				}
			}
		}
	}
	/* 关注区 */
	.focus-area{
		display: flex;
		align-items: center;
		width: 100%;
		height: 120rpx;
		.list{
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			width: 33%;
			height: 100%;
			.num{
				display: flex;
				align-items: center;
				text{
					color: #FFFFFF;
					font-size: 32rpx;
					font-weight: bold;
				}
			}
			.title{
				display: flex;
				align-items: center;
				margin-top: 5rpx;
				text{
					color: #FFFFFF;
					font-size: 24rpx;
				}
			}
		}
	}
	/* vip */
	.vip-info{
		position: absolute;
		left: 50%;
		bottom: 0;
		display: flex;
		justify-content: space-between;
		padding: 0 4%;
		width: 90%;
		height: 80rpx;
		background-color: #464C5B;
		transform: translate(-50%,0);
		border-radius: 10rpx 10rpx 0 0;
		.vip{
			position: relative;
			display: flex;
			align-items: center;
			width: 50%;
			height: 60rpx;
			text{
				color: #ffe678;
				font-size: 26rpx;
			}
			.line{
				position: absolute;
				right: 0;
				top: 40%;
				width: 2rpx;
				height: 20rpx;
				background-color: #ffe678;
			}
		}
		.vip-explain{
			display: flex;
			align-items: center;
			height: 60rpx;
			margin: 0 10rpx;
			text{
				color: #ffe678;
				font-size: 24rpx;
			}
		}
		.vip-btn{
			display: flex;
			align-items: center;
			justify-content: center;
			width: 140rpx;
			height: 40rpx;
			background-color: #ffe678;
			border-radius: 30rpx;
			margin-top: 10rpx;
			text{
				font-size: 24rpx;
				color: #464C5B;

			}
		}
	}
	.my-service{
		position: absolute;
		bottom: -110rpx;
		left: 0;
		right: 0;
		margin: auto;
	}
}
/* 订单信息 */
.order-info{
	display: flex;
	// width: 94%;
	// height: 200rpx;
	border-radius: 20rpx;
	padding-bottom: 24rpx;
	background-color: #FFFFFF;
	// margin: 0rpx auto;
	.list{
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		width: 20%;
		height: 100%;
		position: relative;
		.icon{
			position: relative;
			display: flex;
			align-items: center;
			.iconfont{
				font-size: 38rpx;
				color: #333333;
			}
			.num{
				position: absolute;
				right: -20rpx;
				top: -20rpx;
				padding: 4rpx;
				font-size: 18rpx;
				color: $base;
				border: 2rpx solid #e4393c;
				border-radius: 100%;
				background-color: #e4393c;
			}
		}
		.title1{
			display: flex;
			align-items: center;
			margin-top: 12rpx;
			text{
				color: #333333;
				font-size: 24rpx;
			}
		}
	}
}
/* 钱包 */
.wallet-info{
	display: flex;
	justify-content: space-between;
	flex-wrap: wrap;
	width: 94%;
	// height: 200rpx;
	border-radius: 20rpx;
	background-color: #FFFFFF;
	margin: 20rpx auto;
	// padding-top: 30rpx ;
	.list{
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		// width: 25%;
		min-width: 25%;
		height: 100%;
		// margin-bottom: 30rpx;
		.icon{
			position: relative;
			display: flex;
			align-items: center;
			.iconfont{
				font-size: 38rpx;
				color: $base;
			}
			.number{
				font-size: 28rpx;
				color: #212121;
				font-weight: bold;
			}
		}
		.title{
			display: flex;
			align-items: center;
			margin-top: 10rpx;
			text{
				color: #666666;
				font-size: 24rpx;
			}
		}
	}
}
/* 签到，付款码 */
.integral-payment{
	display: flex;
	justify-content: space-between;
	width: 94%;
	height: 180rpx;
	margin: 20rpx auto;
	.list{
		width: 48%;
		height: 100%;
		background-color: #FFFFFF;
		border-radius: 20rpx;
		.title{
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100%;
			height: 120rpx;
			.iconfont{
				font-size: 48rpx;
				margin-right: 10rpx;
				font-weight: normal;
			}
			text{
				color: #212121;
				font-size: 28rpx;
				font-weight: bold;
			}
		}
		.mess{
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100%;
			text{
				color: #C0C0C0;
				font-size: 26rpx;
			}
		}
	}
}
				.banner1 {
					margin: 20rpx 24rpx;
					height: 258rpx;
					border-radius: 10rpx;
					overflow: hidden;

					.screen-swiper {
						height: 258rpx;
						min-height: 100% !important;

						image {
							width: 100%;
							height: 258rpx;
							border-radius: 10rpx;
						}
					}
				}
/* 我的服务 */
.my-service{
	background-color: #FFFFFF;
	border-radius: 12rpx 12rpx 12rpx 12rpx;
	margin: 20rpx ;
	.title{
		display: flex;
		align-items: center;
		padding: 26rpx 32rpx;
		text{
			font-weight: 500;
			font-size: 32rpx;
			color: #333333;
		}
	}
	.service-list{
		display: flex;
		flex-wrap: wrap;
		padding: 0 20rpx;
		.list{
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			width: 25%;
			height: 140rpx;
			.thumb{
				image{
					width: 60rpx;
				}
			}
			.name{
				display: flex;
				align-items: center;
				justify-content: center;
				height: 50rpx;
				text{
					color: #212121;
					font-size: 24rpx;
				}
			}
		}
	}
	// .service-list{
	// 	display: flex;
	// 	flex-wrap: wrap;
	// 	padding: 24rpx;
	// 	.list{
	// 		position: relative;
	// 		display: flex;
	// 		flex-direction: column;
	// 		width: 100%;
	// 		height: 140rpx;
	// 		.l{
	// 			display: flex;
	// 			.thumb{
	// 				image{
	// 					width: 60rpx;
	// 				}
	// 			}
	// 			.name{
	// 				display: flex;
	// 				align-items: center;
	// 				justify-content: center;
	// 				height: 50rpx;
	// 				text{
	// 					color: #212121;
	// 					font-size: 24rpx;
	// 				}
	// 			}
				
	// 		}
			
	// 	}
	// }

}
/* 为你推荐 */
	.recommend_box {
		width: 90%;
		height: 200rpx;
		margin: 0 auto;
		margin-top: 54rpx;
				display: flex;
				justify-content: space-between;
				.i{
					position: relative;
					width: 50%;
					display: flex;
					justify-content: flex-end;
					image {
						width: 95%;
						height: 150rpx;
					}
				}
				.i:nth-child(1) {
					justify-content: flex-start;
				}
				.textInfo{
					position: absolute;
					top:20%;
					right: 25rpx;
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-items: center;
					.title{
						color: #000000;
						font-size: 24rpx;
					}
					.num{
						margin-bottom: 20rpx;
						font-size: 40rpx;
						color: #000000;
						font-weight: bold;
					}
				}
				
					.i:nth-child(2) {
						.textInfo{
							right: 10rpx;
						}
					}
			}

			
	/* 为你推荐 */
			.recommend-info {
				margin: 0 20rpx;
				box-sizing: border-box;
				width: calc(100% - 40rpx);
				background: #FFFFFF;
				border-radius: 20rpx 20rpx 0 0;
				position: relative;
				height: fit-content;
				clear: both;

				image.bg {
					position: absolute;
					top: 0;
					left: 0;
					width: 100%;
				}

				.title {
					font-family: 'YaHei';
					font-weight: bold;
					font-size: 32rpx;
					color: #FFFFFF;
					height: 60rpx;
					line-height: 38rpx;
					text-align: center;
					font-style: normal;
					text-transform: none;
					z-index: 1;
				}

				.leftBox {
					position: absolute;
					width: 100%;
					height: 100%;
					border: none;
					opacity: 0;
				}

				::v-deep .u-column {
					width: 50%;

					.list {
						width: 98% !important;
						margin-left: 2%;
						height: auto !important;
					}
				}

				::v-deep #u-right-column {

					.list {
						width: 98% !important;
						margin-left: 2%;
					}
				}

				.recommend-title {
					display: flex;
					align-items: center;
					justify-content: center;
					width: 100%;

					.title {
						display: flex;
						align-items: center;

						image {
							width: 136rpx;
							height: 48rpx;
						}
					}
				}

				.goods-list {
					margin-top: 20rpx;
					// justify-content: space-between;
					padding: 0 20rpx;

					.list {
						width: 49%;
						height: 540rpx;
						margin-bottom: 20rpx;
						background-color: #FFFFFF;
						border-radius: 10rpx;
						overflow: hidden;
						margin-left: 0;
						box-shadow: 0rpx 4rpx 8rpx 0rpx rgba(0, 0, 0, 0.25);

						.pictrue {
							display: flex;
							justify-content: center;
							width: 100%;

							image {
								height: 350rpx;
							}
						}

						.title-tag {
							// display: flex;
							max-height: 100rpx;
							padding: 20rpx;

							.tag {
								font-family: YaHei;
								font-weight: 400;
								font-size: 28rpx;
								color: #333333;
								line-height: 33rpx;
								text-align: left;
								font-style: normal;
								text-transform: none;
								overflow: hidden;
								text-overflow: ellipsis;
								display: -webkit-box;
								-webkit-line-clamp: 2;
								-webkit-box-orient: vertical;
								white-space: normal;


								text {
									font-size: 24rpx;
									color: #FFFFFF;
									padding: 4rpx 16rpx;
									background: linear-gradient(to right, $base, $change-clor);
									border-radius: 6rpx;
									margin-right: 10rpx;
								}
							}
						}

						.tag_text {
							padding: 0 20rpx;
							font-family: YaHei;
							font-weight: 400;
							font-size: 20rpx;
							color: #888888;
							line-height: 23rpx;
							text-align: left;
							font-style: normal;
							text-transform: none;
						}

						.price-info {
							margin: 10rpx 20rpx;
							padding-left: 14rpx;
							height: 60rpx;
							background: #310FFF;
							border-radius: 8rpx 8rpx 8rpx 8rpx;
							display: flex;
							justify-content: space-between;

							.user-price {
								display: flex;
								align-items: center;
								margin-right: 10rpx;

								text {
									color: $price-clor;
								}

								.min {
									font-family: YaHei;
									font-weight: 400;
									font-size: 20rpx;
									color: #FFFFFF;
									text-align: left;
									font-style: normal;
									text-transform: none;
								}

								.max {
									font-family: YaHei;
									font-weight: 400;
									font-size: 32rpx;
									color: #FFFFFF;
									text-align: left;
									font-style: normal;
									text-transform: none;
								}
							}

							.vip-price {
								display: flex;
								font-family: YaHei;
								align-items: center;
								color: #FFFFFF;
								width: 104rpx;
								background: #FF1A1B;
								justify-content: center;
								border-radius: 8rpx 8rpx 8rpx 8rpx;

								image {
									width: 26rpx;
									height: 26rpx;
									margin-right: 10rpx;
								}

								text {
									color: #FFFFFF;
									font-size: 24rpx;
								}
							}
						}
					}
				}
			}
		/* 客服热线弹窗 */
.serve-hotline{
	.cu-dialog{
		width: 100%;
		border-radius: 20rpx 20rpx 0 0 !important;
		.contact-list{
			width: 100%;
			.list{
				display: flex;
				align-items: center;
				justify-content: center;
				width: 100%;
				height: 100rpx;
				text{
					color: #222222;
					font-size: 32rpx;
				}
			}
		}
	}
}
	/* 设置列表 */
	.setting-list {
		padding:0 24rpx;
		background-color: #FFFFFF;
		border-radius: 20rpx;
		margin: 0 auto;
		margin-bottom: 20rpx;

		.list {
			display: flex;
			align-items: center;
			justify-content: space-between;
			width: 100%;
			height: 100rpx;
			border-bottom: 2rpx solid #f6f6f6;

			.title123 {
				display: flex;
				align-items: center;
				image{
					width: 48rpx;
					margin-right: 12rpx;
				}
				text {
					font-size: 28rpx;
					color: #333333;
				}
			}

			.more-content {
				position: relative;
				display: flex;
				align-items: center;

				.content {
					font-size: 28rpx;
					color: #959595;
				}

				.more {
					font-size: 24rpx;
					color: #959595;
					margin-left: 20rpx;
				}
			}
		}
	}
