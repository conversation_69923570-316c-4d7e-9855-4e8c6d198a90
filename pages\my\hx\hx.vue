<template>
	<view class="index_class">
		<view class="address-input">
			<view class="list-input">
				<view class="title">
					<text>核销</text>
				</view>
				<view class="content">
					<input type="text" v-model="addressInfo.code" placeholder="请填写核销码">
					<image :src="$fun.imgUrl('/static/icon/s.png')" @click="getCode()" style="width: 50rpx;height: 50rpx;" mode="">
					</image>
				</view>
			</view>

		</view>
		<view class="footer-btn">
			<view class="btn" @click="saveAddress()">
				<text>提交</text>
			</view>
		</view>
		<view class="all-order">
			<!--  app 端状态栏站位 -->
			<mescroll-uni top="280" style="padding-top:0!important" :up="upOption" @up="upCallback" @down="downCallback"
				@init="mescrollInit" @emptyclick="emptyClick">
				<view style="background-color:#FFFFFF">
					<view class="team-box u-m-b-10">
						<view class="u-flex u-p-20" v-for="(item,index) in list" :key="index">
							<u-avatar class="u-m-r-20" :src="$fun.imgUrl(item.avatar)" size="100"
								mode="circle"></u-avatar>
							<view style="margin-left: 50rpx;">
								<view class="introduce">用户名：{{item.username}}</view>
								<view class="introduce">手机号：{{item.mobile}}</view>
								<view class="introduce">核销码：{{item.code}}</view>
								<view class="introduce">核销时间：{{$u.timeFormat(item.createtime*1000, 'yyyy-mm-dd hh:MM')}}
								</view>
							</view>
						</view>
					</view>
				</view>
			</mescroll-uni>
		</view>

	</view>
</template>

<script>
	let zf_number = []; // 支付订单号
	let iconarr = []; // 合并支付订单号
	let orderNumber = "";
	let backindex = "";
	let payOrderNumer;
	// #ifdef H5
	var jweixin = require('@/util/jweixin.js');
	// #endif
	import lotusAddress from "@/components/Winglau14-lotusAddress/Winglau14-lotusAddress.vue";
	import MescrollUni from "@/components/mescroll-uni/mescroll-uni.vue";

	export default {
		components: {
			MescrollUni,
			lotusAddress
		},
		data() {
			return {
				addressInfo: {
					code: '',
				},
				list: [],
				upOption: {
					page: {
						num: 0, // 当前页码,默认0,回调之前会加1,即callback(page)会从1开始
						size: 10 // 每页数据的数量
					},
					noMoreSize: 4, //如果列表已无数据,可设置列表的总数量要大于半页才显示无更多数据;避免列表数据过少(比如只有一条数据),显示无更多数据会不好看; 默认5
					empty: {
						tip: "~暂无数据~", // 提示
						icon: 'https://woshop-1258844920.cos.ap-nanjing.myqcloud.com/static/images/nullStatus/noList.png'
					},
					wechatMiniProgram: 0,
				},
			};
		},
		onLoad(params) {
			uni.setNavigationBarTitle({
				title: params.name ? params.name : '核销'
			})
			this.list = [];
			this.mescroll && this.mescroll.resetUpScroll();
			this.getConfig()
		},
		onReachBottom: function() {
			this.mescroll && this.mescroll.onReachBottom();
		},
		//注册列表滚动事件,用于下拉刷新
		onPageScroll(e) {
			this.mescroll && this.mescroll.onPageScroll(e);
		},
		onBackPress() {
			if (backindex == 2) {
				uni.switchTab({
					url: "/pages/home"
				});
				return true;
			}
		},
		methods: {
			getCode() {
				let that = this;
				jweixin.scanQRCode({
					desc: 'scanQRCode desc',
					needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
					scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是一维码，默认二者都有
					success: function(res) {
						let device = res.resultStr;
						that.addressInfo.code = device
					}
				})
			},
			getConfig() {
				this.$fun.ajax.post('config/wxconfig', {
					url: location.href.split('#')[0]
				}).then(res => {
					if (res.status == 1) {
						let prams = res.data
						jweixin.config({
							debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
							appId: prams.appId, // 必填，公众号的唯一标识
							timestamp: prams.timestamp, // 必填，生成签名的时间戳
							nonceStr: prams.nonceStr, // 必填，生成签名的随机串
							signature: prams.signature, // 必填，签名
							jsApiList: ["scanQRCode", "checkJsApi"] //根据需要看需要哪些SDK的功能
						})
					}
				})
			},
			mescrollInit(mescroll) {
				this.mescroll = mescroll;
			},
			// 下拉刷新的回调
			downCallback(mescroll) {
				mescroll.resetUpScroll(); // 重置列表为第一页 (自动执行 mescroll.num=1, 再触发upCallback方法 )
				iconarr = [] //下拉刷新重置  iconarr
				this.numberpay = false; // 重置合并付款显示状态
			},
			/*上拉加载的回调: mescroll携带page的参数, 其中num:当前页 从1开始, size:每页数据条数,默认10 */
			upCallback(mescroll) {
				var pageNum = mescroll.num; // 页码, 默认从1开始
				var pageSize = mescroll.size; // 页长, 默认每页10条
				this.$fun.ajax.post('user/pitList', {
						page: pageNum
					}).then(res => {
						if (res.status == 1) {
							this.mescroll.endByPage(
								res.data.per_page,
								res.data.last_page
							);
							if (pageNum == 1) this.list = []; //如果是第一页需手动制空列表
							this.list = this.list.concat(res.data.data); //追加新数据
						}
					})
					.catch(err => {
						// 失败隐藏下拉加载状态
						this.mescroll.endErr();
					});
			},
			// 点击空布局按钮的回调
			emptyClick() {
				uni.switchTab({
					url: "/pages/home"
				});
			},
			getbacktel() {
				if (backindex == 2) {
					uni.switchTab({
						url: "/pages/cart"
					});
				} else {
					this.getback();
				}
			},
			showOptions: function(i) {
				if (this.showingIndex == i) return false;
				this.showingIndex = i;
				this.list = []; // 在这里手动置空列表,可显示加载中的请求进度
				this.mescroll.resetUpScroll(); // 刷新列表数据
				this.numberpay = false;
			},
			hideAl() {
				this.valShow = false;
			},
			saveAddress() {
				if (!this.addressInfo.code) {
					this.$fun.msg('请输入核销码')
					return false
				}

				let prams = {
					code: this.addressInfo.code,
				}
				this.$fun.ajax.post('user/setbowl', prams).then(res => {
					this.$fun.msg(res.msg)
					if (res.status == 1) {
						this.$fun.msg(res.msg)
						this.$fun.jump('', 4)
					}
				})
			},
			//回传已选的省市区的值
			choseValue(res) {
				//res数据源包括已选省市区与省市区code
				console.log(res);
				//res.isChose = 1省市区已选 res.isChose = 0;未选
				if (res.isChose) {
					this.lotusAddressData.visible = res.visible; //visible为显示与关闭组件标识true显示false隐藏
					this.lotusAddressData.provinceName = res.province; //省
					this.lotusAddressData.cityName = res.city; //市
					this.lotusAddressData.townName = res.town; //区
					this.addressInfo.pro = res.province; //省
					this.addressInfo.city = res.city; //市
					this.addressInfo.area = res.town; //区
					this.address = `${res.province} ${res.city} ${res.town}`; //region为已选的省市区的值
					this.$forceUpdate()
				} else {
					this.$fun.msg('请完整选择地址')
				}
			},
			getAddressInfo(id) {
				this.$fun.ajax.post('address/get', {
					id
				}).then(res => {
					if (res.status == 1) {
						this.addressInfo = res.data
						this.address = `${res.data.pro} ${res.data.city} ${res.data.area}`
					}
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	.page {
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
		background-color: #FFFFFF;
	}


	.address-input {
		width: 100%;
		background-color: #FFFFFF;

		.list-input {
			display: flex;
			align-items: center;
			padding: 0 4%;
			height: 100rpx;
			border-bottom: 2rpx solid #f6f6f6;

			.title {
				display: flex;
				align-items: center;
				width: 20%;
				height: 100%;

				text {
					color: #222222;
					font-size: 26rpx;
				}
			}

			.content {
				display: flex;
				align-items: center;
				width: 70%;
				height: 100%;

				input {
					width: 100%;
					height: 100%;
					font-size: 26rpx;
					color: #222222;
				}
			}
		}

		.list-textarea {
			display: flex;
			align-items: center;
			padding: 0 4%;
			height: 200rpx;
			border-bottom: 2rpx solid #f6f6f6;

			.title {
				display: flex;
				width: 20%;
				height: 80%;

				text {
					color: #222222;
					font-size: 26rpx;
				}
			}

			.content {
				display: flex;
				align-items: center;
				width: 70%;
				height: 100%;

				textarea {
					width: 100%;
					height: 80%;
					font-size: 26rpx;
					color: #222222;
				}
			}
		}
	}

	.tag-default {
		width: 100%;
		border-top: 20rpx solid #f6f6f6;

		.tag-list {
			display: flex;
			align-items: center;
			padding: 0 4%;
			height: 200rpx;

			.title {
				width: 20%;
				height: 80%;

				text {
					font-size: 26rpx;
					color: #222222;
				}
			}

			.content {
				display: flex;
				width: 70%;
				height: 80%;

				.list {
					display: flex;
					align-items: center;
					justify-content: center;
					min-width: 120rpx;
					height: 60rpx;
					border: 2rpx solid #f6f6f6;
					border-radius: 60rpx;
					margin-right: 20rpx;

					text {
						color: #555555;
						font-size: 24rpx;
					}
				}

				.action {
					background-color: $base;
					border: 2rpx solid $base;

					text {
						color: #FFFFFF;
					}
				}
			}
		}

		.default-address {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 4%;
			height: 100rpx;

			.title {
				display: flex;
				align-items: center;
				width: 20%;
				height: 80%;
			}

			.switch-default {
				uni-switch .uni-switch-input {
					background: #22AA44 !important;
				}
			}
		}
	}

	.footer-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 100rpx;
		background-color: #FFFFFF;

		.btn {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 80%;
			height: 70rpx;
			background: linear-gradient(to right, $base, $change-clor);
			border-radius: 70rpx;
			box-shadow: 0 10rpx 10rpx $base;

			text {
				font-size: 28rpx;
				color: #FFFFFF;
			}
		}
	}

	@import "uview-ui/index.scss";

	page,
	.index_class {
		height: 100%;
	}

	.team-box {
		background-color: #fff;
	}

	.active {
		border-bottom: 2upx solid #310FFF;
		color: #310FFF;
	}

	.shou_sure {
		span {
			display: block;
			width: 100upx;
			height: 40upx;
			line-height: 40upx;
			border-radius: 20upx;
			border: 1upx solid #999;
			color: #999;
			text-align: center;
			font-size: 24upx;
		}
	}

	.padding-btn {
		padding-bottom: 80upx;
	}

	.mescroll-uni.mescroll-uni-fixed {
		top: 96upx !important;
	}

	.to_comment {
		width: 100upx !important;
		margin-left: 0 !important;
	}

	.paybtn {
		width: 100%;
		position: fixed;
		bottom: 0;
		left: 0;
		background-color: #fff;
		padding: 20upx;
	}

	.all-order {
		padding-top: 88upx;
		background-color: #fff;

		.order_list {
			margin-bottom: 20upx;
			background-color: #fff;

			.order_top {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 20upx 30upx;
				border-bottom: 1upx solid #f4f4f4;
				color: #333;
			}
		}

		.order-all-text {
			text-align: right;
			font-size: 24upx;
			color: #333;
			margin-top: 15upx;
		}
	}

	::v-deep .mescroll-uni {
		background: #FFFFFF;
	}
</style>